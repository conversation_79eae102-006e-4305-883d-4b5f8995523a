#!/usr/bin/env python3
"""
RPG屬性計算器演示腳本
展示AttributeCalculator的各種功能
"""
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

from rpg_system.battle_system.services.attribute_calculator import (
    AttributeCalculator, AttributeCalculatorError
)
from unittest.mock import Mock


def create_mock_config_loader():
    """創建模擬配置加載器"""
    mock_loader = Mock()
    
    # 設置卡牌配置
    card_configs = {
        "fire_mage": {
            "base_stats": {
                "max_hp": 120,
                "max_mp": 80,
                "mp_regen_per_turn": 8,
                "patk": 50,
                "pdef": 30,
                "matk": 100,
                "mdef": 60,
                "spd": 75,
                "crit_rate": 0.08,
                "crit_dmg_multiplier": 1.4,
                "accuracy": 0.92,
                "evasion": 0.12
            },
            "growth_per_rpg_level": {
                "max_hp_growth": 12,
                "max_mp_growth": 6,
                "patk_growth": 3,
                "pdef_growth": 2,
                "matk_growth": 8,
                "mdef_growth": 4,
                "spd_growth": 2
            },
            "star_level_effects_key": "fire_mage_effects"
        },
        "warrior": {
            "base_stats": {
                "max_hp": 150,
                "max_mp": 40,
                "mp_regen_per_turn": 4,
                "patk": 90,
                "pdef": 70,
                "matk": 30,
                "mdef": 40,
                "spd": 60,
                "crit_rate": 0.12,
                "crit_dmg_multiplier": 1.6,
                "accuracy": 0.95,
                "evasion": 0.05
            },
            "growth_per_rpg_level": {
                "max_hp_growth": 15,
                "max_mp_growth": 2,
                "patk_growth": 6,
                "pdef_growth": 5,
                "matk_growth": 1,
                "mdef_growth": 3,
                "spd_growth": 1
            },
            "star_level_effects_key": "warrior_effects"
        }
    }
    
    # 設置怪物配置
    monster_configs = {
        "goblin": {
            "max_hp": 80,
            "max_mp": 20,
            "mp_regen_per_turn": 2,
            "patk": 45,
            "pdef": 25,
            "matk": 20,
            "mdef": 15,
            "spd": 55,
            "crit_rate": 0.05,
            "crit_dmg_multiplier": 1.2,
            "accuracy": 0.85,
            "evasion": 0.15
        },
        "dragon": {
            "max_hp": 500,
            "max_mp": 100,
            "mp_regen_per_turn": 10,
            "patk": 120,
            "pdef": 80,
            "matk": 150,
            "mdef": 100,
            "spd": 40,
            "crit_rate": 0.15,
            "crit_dmg_multiplier": 2.0,
            "accuracy": 0.95,
            "evasion": 0.05
        }
    }
    
    # 設置星級效果配置
    star_effects_configs = {
        "fire_mage_effects": {
            "1": {
                "additional_stats_flat": {
                    "max_hp": 15,
                    "matk": 12
                }
            },
            "2": {
                "additional_stats_percent": {
                    "matk": 0.1,  # 10%
                    "max_mp": 0.05  # 5%
                }
            },
            "3": {
                "additional_stats_flat": {
                    "max_hp": 20,
                    "matk": 15,
                    "crit_rate": 0.02
                }
            }
        },
        "warrior_effects": {
            "1": {
                "additional_stats_flat": {
                    "max_hp": 25,
                    "patk": 10
                }
            },
            "2": {
                "additional_stats_percent": {
                    "patk": 0.08,  # 8%
                    "pdef": 0.1    # 10%
                }
            },
            "3": {
                "additional_stats_flat": {
                    "max_hp": 30,
                    "patk": 12,
                    "pdef": 8
                }
            }
        }
    }
    
    def get_card_config(card_id):
        return card_configs.get(card_id)
    
    def get_monster_config(monster_id):
        return monster_configs.get(monster_id)
    
    def get_star_level_effects_config(effects_key):
        return star_effects_configs.get(effects_key)
    
    mock_loader.get_card_config = get_card_config
    mock_loader.get_monster_config = get_monster_config
    mock_loader.get_star_level_effects_config = get_star_level_effects_config
    
    return mock_loader


def demo_card_base_attributes():
    """演示卡牌基礎屬性計算"""
    print("=== 卡牌基礎屬性演示 ===")
    
    calculator = AttributeCalculator()
    config_loader = create_mock_config_loader()
    
    # 1級火法師，無星級
    result = calculator.calculate_attributes(
        "fire_mage", "CARD", 1, 0, config_loader
    )
    
    print("1級火法師 (無星級):")
    for attr, value in result.items():
        if isinstance(value, float) and value != int(value):
            print(f"  {attr}: {value:.2f}")
        else:
            print(f"  {attr}: {int(value)}")
    print()


def demo_rpg_level_growth():
    """演示RPG等級成長"""
    print("=== RPG等級成長演示 ===")
    
    calculator = AttributeCalculator()
    config_loader = create_mock_config_loader()
    
    levels = [1, 5, 10, 15, 20]
    
    print("戰士不同等級的屬性變化:")
    print("等級\tHP\tMP\t物攻\t物防\t魔攻\t魔防\t速度")
    
    for level in levels:
        result = calculator.calculate_attributes(
            "warrior", "CARD", level, 0, config_loader
        )
        print(f"{level}\t{int(result['max_hp'])}\t{int(result['max_mp'])}\t"
              f"{int(result['patk'])}\t{int(result['pdef'])}\t"
              f"{int(result['matk'])}\t{int(result['mdef'])}\t{int(result['spd'])}")
    print()


def demo_star_level_effects():
    """演示星級效果"""
    print("=== 星級效果演示 ===")
    
    calculator = AttributeCalculator()
    config_loader = create_mock_config_loader()
    
    star_levels = [0, 1, 2, 3]
    
    print("10級火法師不同星級的屬性變化:")
    print("星級\tHP\tMP\t魔攻\t暴擊率")
    
    for star in star_levels:
        result = calculator.calculate_attributes(
            "fire_mage", "CARD", 10, star, config_loader
        )
        print(f"{star}\t{int(result['max_hp'])}\t{int(result['max_mp'])}\t"
              f"{int(result['matk'])}\t{result['crit_rate']:.3f}")
    print()


def demo_monster_attributes():
    """演示怪物屬性"""
    print("=== 怪物屬性演示 ===")
    
    calculator = AttributeCalculator()
    config_loader = create_mock_config_loader()
    
    monsters = ["goblin", "dragon"]
    
    for monster_id in monsters:
        result = calculator.calculate_attributes(
            monster_id, "MONSTER", 1, 0, config_loader
        )
        
        print(f"{monster_id.capitalize()}:")
        key_attrs = ["max_hp", "max_mp", "patk", "matk", "spd", "crit_rate"]
        for attr in key_attrs:
            value = result[attr]
            if isinstance(value, float) and value != int(value):
                print(f"  {attr}: {value:.2f}")
            else:
                print(f"  {attr}: {int(value)}")
        print()


def demo_attribute_comparison():
    """演示屬性對比"""
    print("=== 屬性對比演示 ===")
    
    calculator = AttributeCalculator()
    config_loader = create_mock_config_loader()
    
    # 比較不同配置的戰鬥力
    configs = [
        ("火法師", "fire_mage", "CARD", 15, 2),
        ("戰士", "warrior", "CARD", 15, 2),
        ("哥布林", "goblin", "MONSTER", 1, 0),
        ("龍", "dragon", "MONSTER", 1, 0)
    ]
    
    print("戰鬥單位屬性對比 (15級2星 vs 怪物):")
    print("單位\t\tHP\t物攻\t魔攻\t速度\t暴擊率")
    
    for name, unit_id, unit_type, level, star in configs:
        result = calculator.calculate_attributes(
            unit_id, unit_type, level, star, config_loader
        )
        print(f"{name}\t\t{int(result['max_hp'])}\t{int(result['patk'])}\t"
              f"{int(result['matk'])}\t{int(result['spd'])}\t{result['crit_rate']:.3f}")
    print()


def demo_error_handling():
    """演示錯誤處理"""
    print("=== 錯誤處理演示 ===")
    
    calculator = AttributeCalculator()
    config_loader = create_mock_config_loader()
    
    error_cases = [
        ("不存在的卡牌", "invalid_card", "CARD"),
        ("不存在的怪物", "invalid_monster", "MONSTER"),
        ("無效類型", "fire_mage", "INVALID")
    ]
    
    for description, unit_id, unit_type in error_cases:
        try:
            result = calculator.calculate_attributes(
                unit_id, unit_type, 1, 0, config_loader
            )
            print(f"{description}: 計算成功 (意外)")
        except AttributeCalculatorError as e:
            print(f"{description}: {str(e)}")
    print()


def demo_attribute_bounds():
    """演示屬性邊界處理"""
    print("=== 屬性邊界處理演示 ===")
    
    calculator = AttributeCalculator()
    
    # 創建有異常值的配置
    mock_loader = Mock()
    
    # 異常屬性配置
    abnormal_config = {
        "base_stats": {
            "max_hp": -50,      # 負HP
            "max_mp": -10,      # 負MP
            "crit_rate": 1.5,   # 超過100%暴擊率
            "accuracy": 1.2,    # 超過100%命中率
            "evasion": -0.1,    # 負閃避率
            "crit_dmg_multiplier": 0.5  # 小於1.0的暴擊倍率
        }
    }
    
    mock_loader.get_card_config.return_value = abnormal_config
    
    result = calculator.calculate_attributes(
        "abnormal_card", "CARD", 1, 0, mock_loader
    )
    
    print("異常屬性修正結果:")
    bounds_attrs = [
        "max_hp", "max_mp", "crit_rate", "accuracy", 
        "evasion", "crit_dmg_multiplier"
    ]
    for attr in bounds_attrs:
        print(f"  {attr}: {result[attr]}")
    print()


def demo_integration_with_combatant():
    """演示與Combatant模型的集成"""
    print("=== 與戰鬥者模型集成演示 ===")

    from rpg_system.battle_system.models import Combatant, CombatantStats

    calculator = AttributeCalculator()
    config_loader = create_mock_config_loader()

    # 計算火法師屬性
    calculated_attrs = calculator.calculate_attributes(
        "fire_mage", "CARD", 10, 2, config_loader
    )

    # 創建CombatantStats
    stats = CombatantStats(
        hp=int(calculated_attrs["max_hp"]),
        max_mp=int(calculated_attrs["max_mp"]),
        mp_regen_per_turn=int(calculated_attrs["mp_regen_per_turn"]),
        patk=int(calculated_attrs["patk"]),
        pdef=int(calculated_attrs["pdef"]),
        matk=int(calculated_attrs["matk"]),
        mdef=int(calculated_attrs["mdef"]),
        spd=int(calculated_attrs["spd"]),
        crit_rate=calculated_attrs["crit_rate"],
        crit_dmg_multiplier=calculated_attrs["crit_dmg_multiplier"],
        accuracy=calculated_attrs["accuracy"],
        evasion=calculated_attrs["evasion"]
    )

    # 創建Combatant
    combatant = Combatant(
        combatant_id="fire_mage_001",
        name="10級2星火法師",
        stats=stats,
        is_player=True,
        card_id="fire_mage",
        star_level=2,
        rpg_level=10
    )

    print(f"創建戰鬥者: {combatant}")
    print(f"血量百分比: {combatant.get_hp_percent():.1%}")
    print(f"MP百分比: {combatant.get_mp_percent():.1%}")
    print()


def main():
    """主函數"""
    print("RPG屬性計算器演示")
    print("=" * 50)
    print()

    demo_card_base_attributes()
    demo_rpg_level_growth()
    demo_star_level_effects()
    demo_monster_attributes()
    demo_attribute_comparison()
    demo_error_handling()
    demo_attribute_bounds()
    demo_integration_with_combatant()

    print("演示完成！")


if __name__ == "__main__":
    main()
