"""
戰鬥系統演示
"""
import logging
from unittest.mock import Mock

from rpg_system.battle_system.models.battle import Battle
from rpg_system.battle_system.models.combatant import Combatant, CombatantStats
from rpg_system.battle_system.models.enums import SkillType
from rpg_system.battle_system.models.skill_instance import SkillInstance

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def create_mock_dependencies():
    """創建模擬依賴"""
    config_loader = Mock()
    effect_applier = Mock()
    target_selector = Mock()
    passive_trigger_handler = Mock()
    formula_evaluator = Mock()

    # 模擬配置
    config_loader.get_card_config.return_value = {}

    # 模擬技能配置字典
    mock_active_skills = {
        "hero_slash": {
            "name": "英雄斬擊",
            "target_type": "SINGLE_ENEMY",
            "target_logic_details": [],
            "description_template": "對單個敵人造成物理傷害",
            "tags": ["PHYSICAL", "ATTACK"],
            "effects_by_level": {
                "1": {
                    "mp_cost": 0,
                    "cooldown_turns": 0,
                    "effect_definitions": [
                        {
                            "type": "DAMAGE",
                            "value_formula": "25",
                            "damage_type": "PHYSICAL"
                        }
                    ]
                }
            }
        },
        "goblin_strike": {
            "name": "哥布林攻擊",
            "target_type": "SINGLE_ENEMY",
            "target_logic_details": [],
            "description_template": "哥布林的基本攻擊",
            "tags": ["PHYSICAL", "ATTACK"],
            "effects_by_level": {
                "1": {
                    "mp_cost": 0,
                    "cooldown_turns": 0,
                    "effect_definitions": [
                        {
                            "type": "DAMAGE",
                            "value_formula": "20",
                            "damage_type": "PHYSICAL"
                        }
                    ]
                }
            }
        }
    }

    config_loader.active_skills = mock_active_skills
    config_loader.passive_skills = {}
    config_loader.innate_passive_skills = {}

    # 模擬效果應用
    effect_applier.apply_skill_effects.return_value = []

    # 模擬目標選擇
    def mock_target_selector(caster, target_logic, battle_context, formula_evaluator, config_loader):
        enemies = battle_context.get_all_alive_enemies_of(caster)
        return enemies[:1] if enemies else []

    target_selector.select_targets.side_effect = mock_target_selector

    return config_loader, effect_applier, target_selector, passive_trigger_handler, formula_evaluator


def create_test_combatants():
    """創建測試戰鬥者"""
    # 玩家屬性
    player_stats = CombatantStats(
        hp=120, max_mp=60, mp_regen_per_turn=10,
        patk=25, pdef=12, matk=20, mdef=10,
        spd=15, crit_rate=0.15, crit_dmg_multiplier=1.6,
        accuracy=0.9, evasion=0.1
    )
    
    # 怪物屬性
    monster_stats = CombatantStats(
        hp=100, max_mp=40, mp_regen_per_turn=5,
        patk=22, pdef=10, matk=15, mdef=8,
        spd=12, crit_rate=0.08, crit_dmg_multiplier=1.4,
        accuracy=0.85, evasion=0.05
    )
    
    # 創建戰鬥者
    player = Combatant(
        combatant_id="player_hero",
        name="勇者",
        stats=player_stats,
        is_player=True,
        card_id="hero_card",
        definition_id="hero_card"
    )
    
    monster = Combatant(
        combatant_id="goblin_warrior",
        name="哥布林戰士",
        stats=monster_stats,
        is_player=False,
        definition_id="goblin_warrior"
    )
    
    # 添加技能
    player_attack = SkillInstance(
        skill_id="hero_slash",
        skill_type=SkillType.PRIMARY_ATTACK,
        current_level=1,
        current_cooldown=0
    )
    player.add_skill(player_attack)
    
    monster_attack = SkillInstance(
        skill_id="goblin_strike",
        skill_type=SkillType.PRIMARY_ATTACK,
        current_level=1,
        current_cooldown=0
    )
    monster.add_skill(monster_attack)
    monster.skill_order_preference = ["goblin_strike"]
    
    return player, monster


def simulate_battle_turn(battle, turn_number):
    """模擬一個戰鬥回合"""
    print(f"\n=== 第 {turn_number} 回合 ===")
    
    acting_combatant = battle.get_acting_combatant()
    if not acting_combatant:
        print("沒有行動者，戰鬥結束")
        return False
    
    print(f"行動者: {acting_combatant.name}")
    print(f"  HP: {acting_combatant.current_hp}/{acting_combatant.get_stat('hp')}")
    print(f"  MP: {acting_combatant.current_mp}/{acting_combatant.get_stat('max_mp')}")
    
    # 選擇行動
    if acting_combatant.is_player_side:
        # 玩家行動（簡化為自動選擇普攻）
        skill_id = "hero_slash"
        targets = battle.get_all_alive_enemies_of(acting_combatant)
        target_ids = [targets[0].instance_id] if targets else []
    else:
        # 怪物AI行動
        skill_id, target_ids = acting_combatant.get_ai_action(battle, battle.config_loader)
    
    if skill_id and target_ids:
        print(f"  使用技能: {skill_id}")
        print(f"  目標: {[battle.get_combatant_by_id(tid).name for tid in target_ids]}")
        
        # 處理行動
        battle.process_action(acting_combatant, skill_id, target_ids)
    else:
        print("  無法行動")
    
    # 推進到下一個行動者
    next_combatant = battle.next_turn_or_combatant()
    
    # 顯示戰鬥狀態
    print("\n戰鬥狀態:")
    for combatant in battle.player_team + battle.monster_team:
        status = "存活" if combatant.is_alive() else "死亡"
        print(f"  {combatant.name}: {combatant.current_hp}/{combatant.get_stat('hp')} HP ({status})")
    
    return battle.battle_status.value == "IN_PROGRESS"


def main():
    """主演示函數"""
    print("=== 戰鬥系統演示 ===")
    
    # 創建依賴和戰鬥者
    config_loader, effect_applier, target_selector, passive_trigger_handler, formula_evaluator = create_mock_dependencies()
    player, monster = create_test_combatants()
    
    # 創建戰鬥
    battle = Battle(
        player_team=[player],
        monster_team=[monster],
        config_loader=config_loader,
        effect_applier=effect_applier,
        target_selector=target_selector,
        passive_trigger_handler=passive_trigger_handler,
        formula_evaluator=formula_evaluator,
        rng_seed=42
    )
    
    print(f"戰鬥ID: {battle.battle_id}")
    print(f"玩家隊伍: {[c.name for c in battle.player_team]}")
    print(f"怪物隊伍: {[c.name for c in battle.monster_team]}")
    
    # 啟動戰鬥
    print("\n啟動戰鬥...")
    battle.start()
    print(f"戰鬥狀態: {battle.battle_status.value}")
    print(f"當前回合: {battle.current_turn}")
    
    # 模擬戰鬥回合
    turn_count = 0
    max_turns = 10  # 防止無限循環
    
    while battle.battle_status.value == "IN_PROGRESS" and turn_count < max_turns:
        turn_count += 1
        continue_battle = simulate_battle_turn(battle, turn_count)
        
        if not continue_battle:
            break
    
    # 顯示戰鬥結果
    print(f"\n=== 戰鬥結束 ===")
    print(f"結果: {battle.battle_status.value}")
    print(f"總回合數: {turn_count}")
    
    if battle.battle_status.value == "PLAYER_WIN":
        print("🎉 玩家勝利！")
    elif battle.battle_status.value == "MONSTER_WIN":
        print("💀 怪物勝利！")
    else:
        print("🤝 平局！")
    
    # 顯示戰鬥摘要
    print("\n=== 戰鬥摘要 ===")
    summary = battle.get_battle_summary()
    print(f"戰鬥ID: {summary['battle_id']}")
    print(f"狀態: {summary['status']}")
    print(f"回合數: {summary['current_turn']}")
    print(f"日誌條目: {summary['log_entries_count']}")
    
    print("\n最終狀態:")
    for team_name, team_data in [("玩家隊伍", summary['player_team']), ("怪物隊伍", summary['monster_team'])]:
        print(f"  {team_name}:")
        for member in team_data:
            print(f"    {member['name']}: {member['hp']} HP, {member['mp']} MP ({'存活' if member['alive'] else '死亡'})")
    
    print("\n演示完成！")


if __name__ == "__main__":
    main()
