"""
戰鬥日誌模型
"""
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime
import json


@dataclass
class BattleLogEntry:
    """
    戰鬥日誌條目
    
    記錄戰鬥中發生的各種事件
    """
    turn_number: int
    event_type: str
    actor_id: Optional[str]
    target_ids: List[str]
    skill_id: Optional[str]
    damage_dealt: int
    healing_done: int
    mp_consumed: int
    status_effects_applied: List[str]
    status_effects_removed: List[str]
    additional_data: Dict[str, Any]
    timestamp: datetime
    
    def __post_init__(self):
        """後初始化處理"""
        if self.target_ids is None:
            self.target_ids = []
        if self.status_effects_applied is None:
            self.status_effects_applied = []
        if self.status_effects_removed is None:
            self.status_effects_removed = []
        if self.additional_data is None:
            self.additional_data = {}
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    @classmethod
    def create_skill_use(cls, turn_number: int, actor_id: str, skill_id: str,
                        target_ids: List[str], mp_consumed: int = 0,
                        additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建技能使用日誌
        
        Args:
            turn_number: 回合數
            actor_id: 施法者ID
            skill_id: 技能ID
            target_ids: 目標ID列表
            mp_consumed: 消耗的MP
            additional_data: 額外數據
            
        Returns:
            戰鬥日誌條目
        """
        return cls(
            turn_number=turn_number,
            event_type="SKILL_USE",
            actor_id=actor_id,
            target_ids=target_ids,
            skill_id=skill_id,
            damage_dealt=0,
            healing_done=0,
            mp_consumed=mp_consumed,
            status_effects_applied=[],
            status_effects_removed=[],
            additional_data=additional_data or {},
            timestamp=datetime.now()
        )
    
    @classmethod
    def create_damage(cls, turn_number: int, actor_id: Optional[str], 
                     target_id: str, damage: int, skill_id: Optional[str] = None,
                     additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建傷害日誌
        
        Args:
            turn_number: 回合數
            actor_id: 傷害來源ID
            target_id: 目標ID
            damage: 傷害值
            skill_id: 技能ID
            additional_data: 額外數據
            
        Returns:
            戰鬥日誌條目
        """
        return cls(
            turn_number=turn_number,
            event_type="DAMAGE",
            actor_id=actor_id,
            target_ids=[target_id],
            skill_id=skill_id,
            damage_dealt=damage,
            healing_done=0,
            mp_consumed=0,
            status_effects_applied=[],
            status_effects_removed=[],
            additional_data=additional_data or {},
            timestamp=datetime.now()
        )
    
    @classmethod
    def create_healing(cls, turn_number: int, actor_id: Optional[str],
                      target_id: str, healing: int, skill_id: Optional[str] = None,
                      additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建治療日誌
        
        Args:
            turn_number: 回合數
            actor_id: 治療來源ID
            target_id: 目標ID
            healing: 治療量
            skill_id: 技能ID
            additional_data: 額外數據
            
        Returns:
            戰鬥日誌條目
        """
        return cls(
            turn_number=turn_number,
            event_type="HEALING",
            actor_id=actor_id,
            target_ids=[target_id],
            skill_id=skill_id,
            damage_dealt=0,
            healing_done=healing,
            mp_consumed=0,
            status_effects_applied=[],
            status_effects_removed=[],
            additional_data=additional_data or {},
            timestamp=datetime.now()
        )
    
    @classmethod
    def create_status_effect(cls, turn_number: int, actor_id: Optional[str],
                           target_id: str, effect_id: str, applied: bool = True,
                           skill_id: Optional[str] = None,
                           additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建狀態效果日誌
        
        Args:
            turn_number: 回合數
            actor_id: 效果來源ID
            target_id: 目標ID
            effect_id: 效果ID
            applied: True為施加，False為移除
            skill_id: 技能ID
            additional_data: 額外數據
            
        Returns:
            戰鬥日誌條目
        """
        return cls(
            turn_number=turn_number,
            event_type="STATUS_EFFECT",
            actor_id=actor_id,
            target_ids=[target_id],
            skill_id=skill_id,
            damage_dealt=0,
            healing_done=0,
            mp_consumed=0,
            status_effects_applied=[effect_id] if applied else [],
            status_effects_removed=[effect_id] if not applied else [],
            additional_data=additional_data or {},
            timestamp=datetime.now()
        )
    
    @classmethod
    def create_turn_start(cls, turn_number: int, 
                         additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建回合開始日誌
        
        Args:
            turn_number: 回合數
            additional_data: 額外數據
            
        Returns:
            戰鬥日誌條目
        """
        return cls(
            turn_number=turn_number,
            event_type="TURN_START",
            actor_id=None,
            target_ids=[],
            skill_id=None,
            damage_dealt=0,
            healing_done=0,
            mp_consumed=0,
            status_effects_applied=[],
            status_effects_removed=[],
            additional_data=additional_data or {},
            timestamp=datetime.now()
        )
    
    @classmethod
    def create_turn_end(cls, turn_number: int,
                       additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建回合結束日誌

        Args:
            turn_number: 回合數
            additional_data: 額外數據

        Returns:
            戰鬥日誌條目
        """
        return cls(
            turn_number=turn_number,
            event_type="TURN_END",
            actor_id=None,
            target_ids=[],
            skill_id=None,
            damage_dealt=0,
            healing_done=0,
            mp_consumed=0,
            status_effects_applied=[],
            status_effects_removed=[],
            additional_data=additional_data or {},
            timestamp=datetime.now()
        )

    @classmethod
    def create_battle_start(cls, battle_id: str, player_count: int, monster_count: int,
                           additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建戰鬥開始日誌

        Args:
            battle_id: 戰鬥ID
            player_count: 玩家戰鬥者數量
            monster_count: 怪物戰鬥者數量
            additional_data: 額外數據

        Returns:
            戰鬥日誌條目
        """
        data = {"battle_id": battle_id, "player_count": player_count, "monster_count": monster_count}
        if additional_data:
            data.update(additional_data)

        return cls(
            turn_number=0,
            event_type="BATTLE_START",
            actor_id=None,
            target_ids=[],
            skill_id=None,
            damage_dealt=0,
            healing_done=0,
            mp_consumed=0,
            status_effects_applied=[],
            status_effects_removed=[],
            additional_data=data,
            timestamp=datetime.now()
        )

    @classmethod
    def create_battle_end(cls, battle_id: str, result: str,
                         additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建戰鬥結束日誌

        Args:
            battle_id: 戰鬥ID
            result: 戰鬥結果
            additional_data: 額外數據

        Returns:
            戰鬥日誌條目
        """
        data = {"battle_id": battle_id, "result": result}
        if additional_data:
            data.update(additional_data)

        return cls(
            turn_number=-1,  # 特殊標記
            event_type="BATTLE_END",
            actor_id=None,
            target_ids=[],
            skill_id=None,
            damage_dealt=0,
            healing_done=0,
            mp_consumed=0,
            status_effects_applied=[],
            status_effects_removed=[],
            additional_data=data,
            timestamp=datetime.now()
        )

    @classmethod
    def create_skill_cast(cls, caster_name: str, skill_name: str, target_names: List[str],
                         additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建技能施放日誌

        Args:
            caster_name: 施法者名稱
            skill_name: 技能名稱
            target_names: 目標名稱列表
            additional_data: 額外數據

        Returns:
            戰鬥日誌條目
        """
        data = {"caster_name": caster_name, "skill_name": skill_name, "target_names": target_names}
        if additional_data:
            data.update(additional_data)

        return cls(
            turn_number=0,  # 將在添加到戰鬥日誌時設置
            event_type="SKILL_CAST",
            actor_id=caster_name,
            target_ids=target_names,
            skill_id=skill_name,
            damage_dealt=0,
            healing_done=0,
            mp_consumed=0,
            status_effects_applied=[],
            status_effects_removed=[],
            additional_data=data,
            timestamp=datetime.now()
        )

    @classmethod
    def create_skill_unusable(cls, caster_name: str, skill_name: str,
                             additional_data: Optional[Dict[str, Any]] = None) -> 'BattleLogEntry':
        """
        創建技能無法使用日誌

        Args:
            caster_name: 施法者名稱
            skill_name: 技能名稱
            additional_data: 額外數據

        Returns:
            戰鬥日誌條目
        """
        data = {"caster_name": caster_name, "skill_name": skill_name}
        if additional_data:
            data.update(additional_data)

        return cls(
            turn_number=0,  # 將在添加到戰鬥日誌時設置
            event_type="SKILL_UNUSABLE",
            actor_id=caster_name,
            target_ids=[],
            skill_id=skill_name,
            damage_dealt=0,
            healing_done=0,
            mp_consumed=0,
            status_effects_applied=[],
            status_effects_removed=[],
            additional_data=data,
            timestamp=datetime.now()
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "turn_number": self.turn_number,
            "event_type": self.event_type,
            "actor_id": self.actor_id,
            "target_ids": self.target_ids,
            "skill_id": self.skill_id,
            "damage_dealt": self.damage_dealt,
            "healing_done": self.healing_done,
            "mp_consumed": self.mp_consumed,
            "status_effects_applied": self.status_effects_applied,
            "status_effects_removed": self.status_effects_removed,
            "additional_data": self.additional_data,
            "timestamp": self.timestamp.isoformat()
        }
    
    def to_json(self) -> str:
        """轉換為JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    def __str__(self) -> str:
        """字符串表示"""
        if self.event_type == "SKILL_USE":
            return f"T{self.turn_number}: {self.actor_id} uses {self.skill_id}"
        elif self.event_type == "DAMAGE":
            return f"T{self.turn_number}: {self.target_ids[0]} takes {self.damage_dealt} damage"
        elif self.event_type == "HEALING":
            return f"T{self.turn_number}: {self.target_ids[0]} heals {self.healing_done} HP"
        elif self.event_type == "STATUS_EFFECT":
            effect_action = "gains" if self.status_effects_applied else "loses"
            effect_id = (self.status_effects_applied[0] if self.status_effects_applied 
                        else self.status_effects_removed[0])
            return f"T{self.turn_number}: {self.target_ids[0]} {effect_action} {effect_id}"
        else:
            return f"T{self.turn_number}: {self.event_type}"
