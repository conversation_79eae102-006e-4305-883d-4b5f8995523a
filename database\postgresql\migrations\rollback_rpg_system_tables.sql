-- RPG系統數據庫回滾腳本
-- 創建日期: 2024-12-19
-- 描述: 回滾RPG系統相關的數據庫變更
-- 警告: 此腳本將刪除所有RPG相關數據，請謹慎使用！

-- ==========================================
-- 1. 刪除觸發器和函數
-- ==========================================

-- 刪除 rpg_user_progress 表的更新時間戳觸發器
DROP TRIGGER IF EXISTS update_rpg_user_progress_updated_at ON rpg_user_progress;

-- 注意：不刪除 update_updated_at_column 函數，因為可能被其他表使用

-- ==========================================
-- 2. 刪除 rpg_user_progress 表
-- ==========================================
DROP TABLE IF EXISTS rpg_user_progress CASCADE;

-- ==========================================
-- 3. 從 gacha_user_collections 表移除RPG相關字段
-- ==========================================

-- 移除 equipped_common_passives 字段
ALTER TABLE gacha_user_collections 
DROP COLUMN IF EXISTS equipped_common_passives;

-- 移除 equipped_active_skill_ids 字段
ALTER TABLE gacha_user_collections 
DROP COLUMN IF EXISTS equipped_active_skill_ids;

-- 移除 rpg_xp 字段
ALTER TABLE gacha_user_collections 
DROP COLUMN IF EXISTS rpg_xp;

-- 移除 rpg_level 字段
ALTER TABLE gacha_user_collections 
DROP COLUMN IF EXISTS rpg_level;

-- 注意：保留 star_level 和 is_favorite 字段，因為它們可能在RPG系統之前就存在

-- ==========================================
-- 4. 刪除 gacha_user_learned_global_skills 表
-- ==========================================
DROP TABLE IF EXISTS gacha_user_learned_global_skills CASCADE;

-- ==========================================
-- 5. 恢復 gacha_master_cards 表 (如果需要)
-- ==========================================

-- 注意：這裡不恢復 rpg_card_config_key 字段，因為根據設計文檔，
-- 該字段不應該存在於此表中

-- 如果需要恢復 rarity 字段的舊約束，可以在這裡添加
-- 但通常情況下，保持1-7的範圍約束是合理的

-- ==========================================
-- 回滾完成
-- ==========================================

-- 輸出確認信息
DO $$
BEGIN
    RAISE NOTICE 'RPG系統數據庫回滾完成。所有RPG相關的表和字段已被移除。';
    RAISE NOTICE '注意：star_level 和 is_favorite 字段已保留，因為它們可能在RPG系統之前就存在。';
    RAISE NOTICE '如果需要完全回滾這些字段，請手動執行相應的SQL語句。';
END $$;
