"""
屬性計算器服務
"""
from typing import Dict, Literal, TYPE_CHECKING
import logging

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader

logger = logging.getLogger(__name__)


class AttributeCalculatorError(Exception):
    """屬性計算器異常"""
    pass


class AttributeCalculator:
    """
    屬性計算器服務
    
    負責根據基礎配置、RPG等級、培養星級和星級效果來計算戰鬥單位的完整屬性
    """
    
    def __init__(self):
        """初始化屬性計算器"""
        pass
    
    def calculate_attributes(
        self, 
        combatant_definition_id: str, 
        combatant_type: Literal["CARD", "MONSTER"], 
        rpg_level: int, 
        star_level: int, 
        config_loader: 'ConfigLoader'
    ) -> Dict[str, float]:
        """
        計算戰鬥單位的完整屬性
        
        Args:
            combatant_definition_id: 卡牌ID或怪物ID
            combatant_type: "CARD" 或 "MONSTER"
            rpg_level: 戰鬥單位的RPG等級
            star_level: 戰鬥單位的培養星級 (0-35)
            config_loader: ConfigLoader實例
            
        Returns:
            包含所有計算後戰鬥屬性的字典
            
        Raises:
            AttributeCalculatorError: 當配置不存在或計算失敗時
        """
        try:
            logger.debug(
                "計算屬性: ID=%s, 類型=%s, RPG等級=%d, 星級=%d",
                combatant_definition_id, combatant_type, rpg_level, star_level
            )
            
            # 步驟1: 獲取基礎配置
            base_stats_config, growth_config, star_effects_key = (
                self._get_base_configuration(
                    combatant_definition_id, combatant_type, config_loader
                )
            )
            
            # 初始化計算結果
            calculated_attributes = self._initialize_attributes(base_stats_config)
            
            # 步驟2: 計算RPG等級成長 (僅對卡牌)
            if combatant_type == "CARD" and growth_config:
                self._apply_rpg_level_growth(
                    calculated_attributes, growth_config, rpg_level
                )
            
            # 步驟3: 應用星級效果 (僅對卡牌且有star_effects_key)
            if (combatant_type == "CARD" and star_effects_key and star_level > 0):
                self._apply_star_level_effects(
                    calculated_attributes, base_stats_config, 
                    star_effects_key, star_level, config_loader
                )
            
            # 步驟4: 最終處理與返回
            self._finalize_attributes(calculated_attributes)
            
            logger.debug("屬性計算完成: %s", calculated_attributes)
            return calculated_attributes
            
        except Exception as e:
            logger.error(
                "屬性計算失敗: ID=%s, 類型=%s, 錯誤=%s",
                combatant_definition_id, combatant_type, str(e)
            )
            raise AttributeCalculatorError(
                f"計算屬性失敗 ({combatant_definition_id}): {str(e)}"
            ) from e
    
    def _get_base_configuration(
        self, 
        combatant_definition_id: str, 
        combatant_type: str, 
        config_loader: 'ConfigLoader'
    ) -> tuple:
        """
        獲取基礎配置
        
        Returns:
            (base_stats_config, growth_config, star_effects_key)
        """
        if combatant_type == "CARD":
            card_config = config_loader.get_card_config(combatant_definition_id)
            if card_config is None:
                raise AttributeCalculatorError(
                    f"卡牌配置不存在: {combatant_definition_id}"
                )
            
            base_stats_config = card_config.get("base_stats", {})
            growth_config = card_config.get("growth_per_rpg_level", {})
            star_effects_key = card_config.get("star_level_effects_key")
            
            return base_stats_config, growth_config, star_effects_key
            
        elif combatant_type == "MONSTER":
            monster_config = config_loader.get_monster_config(
                combatant_definition_id
            )
            if monster_config is None:
                raise AttributeCalculatorError(
                    f"怪物配置不存在: {combatant_definition_id}"
                )
            
            # 怪物配置直接包含屬性，沒有RPG等級成長或星級效果
            base_stats_config = monster_config
            growth_config = None
            star_effects_key = None
            
            return base_stats_config, growth_config, star_effects_key
            
        else:
            raise AttributeCalculatorError(
                f"未知的戰鬥單位類型: {combatant_type}"
            )
    
    def _initialize_attributes(self, base_stats_config: Dict) -> Dict[str, float]:
        """
        初始化屬性字典
        
        Args:
            base_stats_config: 基礎屬性配置
            
        Returns:
            初始化的屬性字典
        """
        # 定義所有核心屬性的默認值
        default_attributes = {
            "max_hp": 0.0,
            "max_mp": 0.0,
            "mp_regen_per_turn": 0.0,
            "patk": 0.0,
            "pdef": 0.0,
            "matk": 0.0,
            "mdef": 0.0,
            "spd": 0.0,
            "crit_rate": 0.0,
            "crit_dmg_multiplier": 1.0,  # 默認1.0倍
            "accuracy": 1.0,  # 默認100%命中
            "evasion": 0.0
        }
        
        # 從基礎配置中複製屬性
        calculated_attributes = default_attributes.copy()
        for attr_name, value in base_stats_config.items():
            if isinstance(value, (int, float)):
                calculated_attributes[attr_name] = float(value)
        
        return calculated_attributes
    
    def _apply_rpg_level_growth(
        self, 
        calculated_attributes: Dict[str, float], 
        growth_config: Dict, 
        rpg_level: int
    ):
        """
        應用RPG等級成長
        
        Args:
            calculated_attributes: 計算中的屬性字典
            growth_config: 成長配置
            rpg_level: RPG等級
        """
        # 基礎屬性是1級時的屬性，成長從2級開始計算
        effective_rpg_level = rpg_level - 1
        
        if effective_rpg_level > 0:
            for attr_name, growth_value in growth_config.items():
                if isinstance(growth_value, (int, float)):
                    # 移除可能的 "_growth" 後綴來獲取實際屬性名
                    actual_attr_name = attr_name.replace("_growth", "")
                    if actual_attr_name in calculated_attributes:
                        calculated_attributes[actual_attr_name] += (
                            float(growth_value) * effective_rpg_level
                        )
                        logger.debug(
                            "應用RPG等級成長: %s += %f * %d = %f",
                            actual_attr_name, growth_value, effective_rpg_level,
                            calculated_attributes[actual_attr_name]
                        )
    
    def _apply_star_level_effects(
        self, 
        calculated_attributes: Dict[str, float], 
        base_stats_config: Dict, 
        star_effects_key: str, 
        star_level: int, 
        config_loader: 'ConfigLoader'
    ):
        """
        應用星級效果
        
        Args:
            calculated_attributes: 計算中的屬性字典
            base_stats_config: 基礎屬性配置
            star_effects_key: 星級效果配置鍵
            star_level: 星級
            config_loader: 配置加載器
        """
        star_level_effects_config_group = (
            config_loader.get_star_level_effects_config(star_effects_key)
        )
        
        if star_level_effects_config_group is None:
            logger.warning(
                "星級效果配置不存在: %s", star_effects_key
            )
            return
        
        # 遍歷從1到star_level的每個星級
        for s_level in range(1, star_level + 1):
            current_star_effect_config = star_level_effects_config_group.get(
                str(s_level)
            )
            
            if current_star_effect_config:
                self._apply_single_star_effect(
                    calculated_attributes, base_stats_config, 
                    current_star_effect_config, s_level
                )
    
    def _apply_single_star_effect(
        self, 
        calculated_attributes: Dict[str, float], 
        base_stats_config: Dict, 
        star_effect_config: Dict, 
        star_level: int
    ):
        """
        應用單個星級效果
        
        Args:
            calculated_attributes: 計算中的屬性字典
            base_stats_config: 基礎屬性配置
            star_effect_config: 星級效果配置
            star_level: 當前星級
        """
        # 應用扁平值屬性加成
        additional_stats_flat = star_effect_config.get(
            "additional_stats_flat", {}
        )
        for stat_name, value in additional_stats_flat.items():
            if isinstance(value, (int, float)):
                calculated_attributes[stat_name] = (
                    calculated_attributes.get(stat_name, 0) + float(value)
                )
                logger.debug(
                    "應用星級%d扁平加成: %s += %f",
                    star_level, stat_name, value
                )
        
        # 應用百分比屬性加成
        additional_stats_percent = star_effect_config.get(
            "additional_stats_percent", {}
        )
        for stat_name, percent_value in additional_stats_percent.items():
            if isinstance(percent_value, (int, float)):
                # 百分比加成基於卡牌1級時的基礎屬性
                base_value_for_percent_calc = base_stats_config.get(
                    stat_name, 0
                )
                if isinstance(base_value_for_percent_calc, (int, float)):
                    bonus = float(base_value_for_percent_calc) * float(
                        percent_value
                    )
                    calculated_attributes[stat_name] = (
                        calculated_attributes.get(stat_name, 0) + bonus
                    )
                    logger.debug(
                        "應用星級%d百分比加成: %s += %f * %f = %f",
                        star_level, stat_name, base_value_for_percent_calc,
                        percent_value, bonus
                    )
    
    def _finalize_attributes(self, calculated_attributes: Dict[str, float]):
        """
        最終處理屬性
        
        Args:
            calculated_attributes: 計算中的屬性字典
        """
        # 確保所有核心屬性都存在
        required_attributes = [
            "max_hp", "max_mp", "mp_regen_per_turn", "patk", "pdef", 
            "matk", "mdef", "spd", "crit_rate", "crit_dmg_multiplier", 
            "accuracy", "evasion"
        ]
        
        for attr_name in required_attributes:
            if attr_name not in calculated_attributes:
                if attr_name == "crit_dmg_multiplier":
                    calculated_attributes[attr_name] = 1.0
                elif attr_name == "accuracy":
                    calculated_attributes[attr_name] = 1.0
                else:
                    calculated_attributes[attr_name] = 0.0
        
        # 進行屬性校驗和修正
        # HP不應為負
        if calculated_attributes["max_hp"] < 0:
            calculated_attributes["max_hp"] = 1.0
        
        # MP不應為負
        if calculated_attributes["max_mp"] < 0:
            calculated_attributes["max_mp"] = 0.0
        
        # 暴擊率在0-1之間
        calculated_attributes["crit_rate"] = max(
            0.0, min(1.0, calculated_attributes["crit_rate"])
        )
        
        # 命中率在0-1之間
        calculated_attributes["accuracy"] = max(
            0.0, min(1.0, calculated_attributes["accuracy"])
        )
        
        # 閃避率在0-1之間
        calculated_attributes["evasion"] = max(
            0.0, min(1.0, calculated_attributes["evasion"])
        )
        
        # 暴擊傷害倍率不應小於1.0
        if calculated_attributes["crit_dmg_multiplier"] < 1.0:
            calculated_attributes["crit_dmg_multiplier"] = 1.0
