"""
玩家收藏RPG倉庫

負責與 gacha_user_collections 表中 RPG 相關字段的交互
"""
from typing import List, Dict, Any, Optional, TYPE_CHECKING
import json
import logging
from datetime import datetime
from dataclasses import dataclass

import asyncpg

from database.base_repository import BaseRepository

if TYPE_CHECKING:
    from rpg_system.config.pydantic_models.cards_models import CardConfig, DefaultPassiveSlot

logger = logging.getLogger(__name__)


class PlayerCollectionRPGRepositoryError(Exception):
    """玩家收藏RPG倉庫異常"""
    pass


@dataclass
class PlayerCardDBData:
    """玩家卡牌數據庫數據模型"""
    id: int
    user_id: int
    card_id: str
    quantity: int
    star_level: int
    rpg_level: int
    rpg_xp: int
    equipped_active_skill_ids: List[Optional[str]]
    equipped_common_passives: Dict[str, Optional[Dict[str, Any]]]
    is_favorite: bool = False
    first_acquired: Optional[datetime] = None
    last_acquired: Optional[datetime] = None


class PlayerCollectionRPGRepository(BaseRepository):
    """
    玩家收藏RPG倉庫

    負責與 gacha_user_collections 表中 RPG 相關字段的交互
    """

    def __init__(self, pool: asyncpg.Pool):
        """
        初始化玩家收藏RPG倉庫

        Args:
            pool: asyncpg 連接池
        """
        super().__init__(pool)
        self.table_name = 'gacha_user_collections'

    def _parse_player_card_data(self, row) -> Optional[PlayerCardDBData]:
        """
        解析數據庫行為 PlayerCardDBData 對象

        Args:
            row: 數據庫查詢結果行

        Returns:
            PlayerCardDBData 對象或 None
        """
        if not row:
            return None

        try:
            # 解析 JSON 字段
            equipped_active_skill_ids = []
            if row.get('equipped_active_skill_ids'):
                if isinstance(row['equipped_active_skill_ids'], str):
                    equipped_active_skill_ids = json.loads(row['equipped_active_skill_ids'])
                else:
                    equipped_active_skill_ids = row['equipped_active_skill_ids']

            equipped_common_passives = {}
            if row.get('equipped_common_passives'):
                if isinstance(row['equipped_common_passives'], str):
                    equipped_common_passives = json.loads(row['equipped_common_passives'])
                else:
                    equipped_common_passives = row['equipped_common_passives']

            return PlayerCardDBData(
                id=row['id'],
                user_id=row['user_id'],
                card_id=str(row['card_id']),
                quantity=row.get('quantity', 1),
                star_level=row.get('star_level', 0),
                rpg_level=row.get('rpg_level', 1),
                rpg_xp=row.get('rpg_xp', 0),
                equipped_active_skill_ids=equipped_active_skill_ids,
                equipped_common_passives=equipped_common_passives,
                is_favorite=row.get('is_favorite', False),
                first_acquired=row.get('first_acquired'),
                last_acquired=row.get('last_acquired')
            )
        except Exception as e:
            logger.error(f"解析玩家卡牌數據失敗: {e}")
            return None

    async def get_player_card_by_collection_id(self, collection_id: int) -> Optional[PlayerCardDBData]:
        """
        根據收藏ID獲取玩家卡牌數據

        Args:
            collection_id: 收藏ID

        Returns:
            PlayerCardDBData 對象或 None
        """
        try:
            query = f"""
                SELECT id, user_id, card_id, quantity, star_level, rpg_level, rpg_xp,
                       equipped_active_skill_ids, equipped_common_passives, is_favorite,
                       first_acquired, last_acquired
                FROM {self.table_name}
                WHERE id = $1
            """

            row = await self._fetchrow(query, [collection_id])
            return self._parse_player_card_data(row)

        except Exception as e:
            logger.error(f"獲取玩家卡牌數據失敗: collection_id={collection_id}, error={e}")
            raise PlayerCollectionRPGRepositoryError(f"獲取玩家卡牌數據失敗: {str(e)}") from e

    async def get_player_cards_by_user_id(self, user_id: int) -> List[PlayerCardDBData]:
        """
        根據用戶ID獲取所有玩家卡牌數據

        Args:
            user_id: 用戶ID

        Returns:
            PlayerCardDBData 對象列表
        """
        try:
            query = f"""
                SELECT id, user_id, card_id, quantity, star_level, rpg_level, rpg_xp,
                       equipped_active_skill_ids, equipped_common_passives, is_favorite,
                       first_acquired, last_acquired
                FROM {self.table_name}
                WHERE user_id = $1
                ORDER BY id
            """

            rows = await self._fetch(query, [user_id])

            result = []
            for row in rows:
                card_data = self._parse_player_card_data(row)
                if card_data:
                    result.append(card_data)

            return result

        except Exception as e:
            logger.error(f"獲取用戶卡牌數據失敗: user_id={user_id}, error={e}")
            raise PlayerCollectionRPGRepositoryError(f"獲取用戶卡牌數據失敗: {str(e)}") from e

    async def update_player_card_rpg_stats(self, collection_id: int, rpg_level: int, rpg_xp: int, star_level: int) -> bool:
        """
        更新玩家卡牌RPG統計數據

        Args:
            collection_id: 收藏ID
            rpg_level: RPG等級
            rpg_xp: RPG經驗值
            star_level: 星級

        Returns:
            更新是否成功
        """
        try:
            query = f"""
                UPDATE {self.table_name}
                SET rpg_level = $2, rpg_xp = $3, star_level = $4
                WHERE id = $1
            """

            result = await self._execute(query, [collection_id, rpg_level, rpg_xp, star_level])

            # 檢查是否有行被更新
            return result.split()[-1] == '1'  # "UPDATE 1" 表示成功更新一行

        except Exception as e:
            logger.error(f"更新玩家卡牌RPG統計失敗: collection_id={collection_id}, error={e}")
            raise PlayerCollectionRPGRepositoryError(f"更新玩家卡牌RPG統計失敗: {str(e)}") from e

    async def update_player_card_active_skills(self, collection_id: int, equipped_active_skill_ids: List[Optional[str]]) -> bool:
        """
        更新玩家卡牌主動技能裝備

        Args:
            collection_id: 收藏ID
            equipped_active_skill_ids: 已裝備的主動技能ID列表

        Returns:
            更新是否成功
        """
        try:
            # 將列表轉換為JSON字符串
            skills_json = json.dumps(equipped_active_skill_ids)

            query = f"""
                UPDATE {self.table_name}
                SET equipped_active_skill_ids = $2
                WHERE id = $1
            """

            result = await self._execute(query, [collection_id, skills_json])

            # 檢查是否有行被更新
            return result.split()[-1] == '1'

        except Exception as e:
            logger.error(f"更新玩家卡牌主動技能失敗: collection_id={collection_id}, error={e}")
            raise PlayerCollectionRPGRepositoryError(f"更新玩家卡牌主動技能失敗: {str(e)}") from e

    async def update_player_card_passive_skills(self, collection_id: int, equipped_common_passives: Dict[str, Optional[Dict[str, Any]]]) -> bool:
        """
        更新玩家卡牌被動技能裝備

        Args:
            collection_id: 收藏ID
            equipped_common_passives: 已裝備的通用被動技能字典

        Returns:
            更新是否成功
        """
        try:
            # 將字典轉換為JSON字符串
            passives_json = json.dumps(equipped_common_passives)

            query = f"""
                UPDATE {self.table_name}
                SET equipped_common_passives = $2
                WHERE id = $1
            """

            result = await self._execute(query, [collection_id, passives_json])

            # 檢查是否有行被更新
            return result.split()[-1] == '1'

        except Exception as e:
            logger.error(f"更新玩家卡牌被動技能失敗: collection_id={collection_id}, error={e}")
            raise PlayerCollectionRPGRepositoryError(f"更新玩家卡牌被動技能失敗: {str(e)}") from e

    async def add_rpg_data_to_new_card(
        self,
        collection_id: int,
        card_config: 'CardConfig',
        default_passives_config: Optional[List['DefaultPassiveSlot']] = None
    ) -> bool:
        """
        為新卡牌初始化RPG數據

        Args:
            collection_id: 收藏ID
            card_config: 卡牌配置
            default_passives_config: 默認被動技能配置

        Returns:
            初始化是否成功
        """
        try:
            # 初始化主動技能槽位
            equipped_active_skills = []

            # 從卡牌配置獲取默認主動技能
            if hasattr(card_config, 'default_active_skill_slot_1_id') and card_config.default_active_skill_slot_1_id:
                equipped_active_skills.append(card_config.default_active_skill_slot_1_id)
            else:
                equipped_active_skills.append(None)

            if hasattr(card_config, 'default_active_skill_slot_2_id') and card_config.default_active_skill_slot_2_id:
                equipped_active_skills.append(card_config.default_active_skill_slot_2_id)
            else:
                equipped_active_skills.append(None)

            if hasattr(card_config, 'default_active_skill_slot_3_id') and card_config.default_active_skill_slot_3_id:
                equipped_active_skills.append(card_config.default_active_skill_slot_3_id)
            else:
                equipped_active_skills.append(None)

            # 初始化被動技能槽位
            equipped_passives = {}

            # 從卡牌配置獲取默認被動技能
            if hasattr(card_config, 'default_passives_on_acquire') and card_config.default_passives_on_acquire:
                for i, passive_slot in enumerate(card_config.default_passives_on_acquire):
                    if passive_slot and hasattr(passive_slot, 'skill_id') and passive_slot.skill_id:
                        slot_key = f"slot_{i}"
                        equipped_passives[slot_key] = {
                            "skill_id": passive_slot.skill_id,
                            "level": getattr(passive_slot, 'level', 1)
                        }

            # 轉換為JSON
            active_skills_json = json.dumps(equipped_active_skills)
            passives_json = json.dumps(equipped_passives)

            # 更新數據庫
            query = f"""
                UPDATE {self.table_name}
                SET rpg_level = $2, rpg_xp = $3, star_level = $4,
                    equipped_active_skill_ids = $5, equipped_common_passives = $6
                WHERE id = $1
            """

            result = await self._execute(query, [
                collection_id,
                1,  # 默認RPG等級
                0,  # 默認RPG經驗
                0,  # 默認星級
                active_skills_json,
                passives_json
            ])

            # 檢查是否有行被更新
            return result.split()[-1] == '1'

        except Exception as e:
            logger.error(f"初始化新卡牌RPG數據失敗: collection_id={collection_id}, error={e}")
            raise PlayerCollectionRPGRepositoryError(f"初始化新卡牌RPG數據失敗: {str(e)}") from e
