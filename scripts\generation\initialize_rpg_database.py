"""
RPG 數據庫初始化腳本

創建 RPG 系統所需的數據庫表
"""
import os
import sys
import asyncio
import argparse
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import asyncpg
from database.database_manager import DatabaseManager


# RPG 系統所需的 SQL 表創建語句
RPG_TABLES_SQL = """
-- 用戶學習的全局技能表
CREATE TABLE IF NOT EXISTS gacha_user_learned_global_skills (
    user_id BIGINT NOT NULL,
    skill_id VARCHAR(100) NOT NULL,
    skill_type VARCHAR(20) NOT NULL CHECK (skill_type IN ('ACTIVE', 'PASSIVE')),
    skill_level INTEGER NOT NULL DEFAULT 1,
    skill_xp INTEGER NOT NULL DEFAULT 0,
    unlocked_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    PRIMARY KEY (user_id, skill_id, skill_type)
);

-- 用戶 RPG 進度表
CREATE TABLE IF NOT EXISTS rpg_user_progress (
    user_id BIGINT PRIMARY KEY,
    current_floor_unlocked INTEGER NOT NULL DEFAULT 1,
    current_floor_wins INTEGER NOT NULL DEFAULT 0,
    max_floor_cleared INTEGER NOT NULL DEFAULT 0,
    current_team_formation JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 為 gacha_user_collections 表添加 RPG 相關字段（如果不存在）
DO $$
BEGIN
    -- 檢查並添加 rpg_level 字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'gacha_user_collections' 
        AND column_name = 'rpg_level'
    ) THEN
        ALTER TABLE gacha_user_collections 
        ADD COLUMN rpg_level INTEGER DEFAULT 1;
    END IF;
    
    -- 檢查並添加 rpg_xp 字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'gacha_user_collections' 
        AND column_name = 'rpg_xp'
    ) THEN
        ALTER TABLE gacha_user_collections 
        ADD COLUMN rpg_xp INTEGER DEFAULT 0;
    END IF;
    
    -- 檢查並添加 equipped_active_skill_ids 字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'gacha_user_collections' 
        AND column_name = 'equipped_active_skill_ids'
    ) THEN
        ALTER TABLE gacha_user_collections 
        ADD COLUMN equipped_active_skill_ids JSONB DEFAULT '[]'::jsonb;
    END IF;
    
    -- 檢查並添加 equipped_common_passives 字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'gacha_user_collections' 
        AND column_name = 'equipped_common_passives'
    ) THEN
        ALTER TABLE gacha_user_collections 
        ADD COLUMN equipped_common_passives JSONB DEFAULT '{}'::jsonb;
    END IF;
    
    -- 檢查並添加 star_level 字段（如果不存在）
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'gacha_user_collections' 
        AND column_name = 'star_level'
    ) THEN
        ALTER TABLE gacha_user_collections 
        ADD COLUMN star_level INTEGER DEFAULT 0;
    END IF;
END $$;

-- 創建索引以提升查詢性能
CREATE INDEX IF NOT EXISTS idx_learned_global_skills_user_id 
ON gacha_user_learned_global_skills(user_id);

CREATE INDEX IF NOT EXISTS idx_learned_global_skills_skill_type 
ON gacha_user_learned_global_skills(skill_type);

CREATE INDEX IF NOT EXISTS idx_rpg_user_progress_floor 
ON rpg_user_progress(current_floor_unlocked);

CREATE INDEX IF NOT EXISTS idx_gacha_collections_rpg_level 
ON gacha_user_collections(rpg_level);

CREATE INDEX IF NOT EXISTS idx_gacha_collections_star_level 
ON gacha_user_collections(star_level);

-- 創建更新時間觸發器函數（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 為 rpg_user_progress 表創建更新時間觸發器
DROP TRIGGER IF EXISTS update_rpg_user_progress_updated_at ON rpg_user_progress;
CREATE TRIGGER update_rpg_user_progress_updated_at
    BEFORE UPDATE ON rpg_user_progress
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
"""


async def initialize_rpg_database(database_url: str, dry_run: bool = False) -> bool:
    """
    初始化 RPG 數據庫表
    
    Args:
        database_url: 數據庫連接 URL
        dry_run: 是否只是預覽 SQL 而不執行
        
    Returns:
        初始化是否成功
    """
    if dry_run:
        print("🔍 預覽模式 - 將要執行的 SQL:")
        print("-" * 50)
        print(RPG_TABLES_SQL)
        print("-" * 50)
        return True
    
    try:
        print("🔗 連接到數據庫...")
        conn = await asyncpg.connect(database_url)
        
        print("🔧 執行 RPG 表創建 SQL...")
        await conn.execute(RPG_TABLES_SQL)
        
        print("✅ RPG 數據庫表初始化成功！")
        
        # 驗證表是否創建成功
        print("🔍 驗證表創建...")
        
        # 檢查新表
        tables_to_check = [
            'gacha_user_learned_global_skills',
            'rpg_user_progress'
        ]
        
        for table_name in tables_to_check:
            result = await conn.fetchval(
                "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = $1)",
                table_name
            )
            if result:
                print(f"  ✅ 表 {table_name} 創建成功")
            else:
                print(f"  ❌ 表 {table_name} 創建失敗")
                return False
        
        # 檢查 gacha_user_collections 表的新字段
        columns_to_check = [
            'rpg_level',
            'rpg_xp', 
            'equipped_active_skill_ids',
            'equipped_common_passives',
            'star_level'
        ]
        
        for column_name in columns_to_check:
            result = await conn.fetchval(
                """SELECT EXISTS (
                    SELECT 1 FROM information_schema.columns 
                    WHERE table_name = 'gacha_user_collections' 
                    AND column_name = $1
                )""",
                column_name
            )
            if result:
                print(f"  ✅ 字段 gacha_user_collections.{column_name} 添加成功")
            else:
                print(f"  ❌ 字段 gacha_user_collections.{column_name} 添加失敗")
                return False
        
        await conn.close()
        print("🎉 RPG 數據庫初始化完成！")
        return True
        
    except Exception as e:
        print(f"❌ RPG 數據庫初始化失敗: {e}")
        return False


async def main():
    """主函數"""
    parser = argparse.ArgumentParser(description="初始化 RPG 系統數據庫表")
    parser.add_argument(
        "--database-url",
        help="數據庫連接 URL (如果不提供，將嘗試從環境變量獲取)"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="只預覽 SQL 而不執行"
    )
    
    args = parser.parse_args()
    
    # 獲取數據庫 URL
    database_url = args.database_url
    if not database_url:
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            print("❌ 錯誤: 請提供數據庫 URL 或設置 DATABASE_URL 環境變量")
            return 1
    
    print("🚀 開始 RPG 數據庫初始化...")
    print(f"🔗 數據庫 URL: {database_url[:50]}..." if len(database_url) > 50 else database_url)
    print(f"🔍 預覽模式: {'是' if args.dry_run else '否'}")
    print("-" * 50)
    
    # 執行初始化
    success = await initialize_rpg_database(database_url, args.dry_run)
    
    if success:
        print("✅ 初始化成功完成！")
        return 0
    else:
        print("❌ 初始化失敗！")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
