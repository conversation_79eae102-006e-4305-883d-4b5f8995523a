"""
技能實例模型
"""
from typing import Dict, Any, Optional, TYPE_CHECKING
import logging

from .enums import SkillType

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader

logger = logging.getLogger(__name__)


class SkillInstance:
    """
    技能實例類
    
    表示戰鬥中一個具體的技能實例，包含技能ID、類型、等級和冷卻狀態
    """
    
    def __init__(self, skill_id: str, skill_type: SkillType, 
                 current_level: int, current_cooldown: int = 0):
        """
        初始化技能實例
        
        Args:
            skill_id: 技能ID
            skill_type: 技能類型
            current_level: 當前等級
            current_cooldown: 當前冷卻回合數，默認為0
        """
        self.skill_id = skill_id
        self.skill_type = skill_type
        self.current_level = current_level
        self.current_cooldown = current_cooldown
    
    def get_definition(self, all_configs: 'ConfigLoader', 
                      star_level: Optional[int] = None) -> Dict[str, Any]:
        """
        獲取技能定義
        
        根據技能類型和等級從配置中獲取具體的技能定義
        
        Args:
            all_configs: 配置加載器實例
            star_level: 星級（用於天賦被動技能）
            
        Returns:
            包含技能定義的字典
            
        Raises:
            ValueError: 當技能配置不存在或等級無效時
        """
        try:
            # 根據技能類型選擇配置源
            if self.skill_type == SkillType.ACTIVE:
                config_dict = all_configs.active_skills
                level_key = str(self.current_level)
                effects_key = "effects_by_level"
            elif self.skill_type == SkillType.PASSIVE:
                config_dict = all_configs.passive_skills
                level_key = str(self.current_level)
                effects_key = "effects_by_level"
            elif self.skill_type == SkillType.INNATE_PASSIVE:
                config_dict = all_configs.innate_passive_skills
                if star_level is None:
                    raise ValueError(f"star_level is required for INNATE_PASSIVE skill {self.skill_id}")
                level_key = str(star_level)
                effects_key = "effects_by_star_level"
            elif self.skill_type == SkillType.PRIMARY_ATTACK:
                # 普攻通常存儲在主動技能中，但有特殊處理
                config_dict = all_configs.active_skills
                level_key = str(self.current_level)
                effects_key = "effects_by_level"
            else:
                raise ValueError(f"Unknown skill type: {self.skill_type}")
            
            # 獲取基礎技能配置
            if self.skill_id not in config_dict:
                raise ValueError(f"Skill {self.skill_id} not found in {self.skill_type.value} configs")
            
            skill_config = config_dict[self.skill_id]
            
            # 獲取等級相關的效果定義
            if effects_key not in skill_config:
                raise ValueError(f"No {effects_key} found for skill {self.skill_id}")
            
            effects_by_level = skill_config[effects_key]
            if level_key not in effects_by_level:
                raise ValueError(f"Level {level_key} not found for skill {self.skill_id}")
            
            level_effects = effects_by_level[level_key]
            
            # 構建完整的技能定義
            definition = {
                "skill_id": self.skill_id,
                "name": skill_config.get("name", "Unknown Skill"),
                "skill_type": self.skill_type.value,
                "current_level": self.current_level,
                "effect_definitions": level_effects.get("effect_definitions", []),
                "mp_cost": level_effects.get("mp_cost", 0),
                "cooldown_turns": level_effects.get("cooldown_turns", 0),
                "target_type": skill_config.get("target_type", "SINGLE_ENEMY"),
                "target_logic_details": skill_config.get("target_logic_details", []),
                "description": skill_config.get("description_template", ""),
                "tags": skill_config.get("tags", [])
            }
            
            return definition
            
        except Exception as e:
            logger.error(f"Failed to get definition for skill {self.skill_id}: {e}")
            raise
    
    def is_usable(self, caster_mp: int, config_loader: 'ConfigLoader', 
                  star_level: Optional[int] = None) -> bool:
        """
        檢查技能是否可用
        
        僅對主動技能和普攻有意義
        
        Args:
            caster_mp: 施法者當前MP
            config_loader: 配置加載器
            star_level: 星級（用於天賦被動技能）
            
        Returns:
            技能是否可用
        """
        # 被動技能不需要檢查可用性
        if self.skill_type in [SkillType.PASSIVE, SkillType.INNATE_PASSIVE]:
            return True
        
        # 檢查冷卻
        if self.current_cooldown > 0:
            return False
        
        try:
            # 獲取技能定義並檢查MP消耗
            definition = self.get_definition(config_loader, star_level)
            mp_cost = definition.get("mp_cost", 0)
            
            return caster_mp >= mp_cost
            
        except Exception as e:
            logger.error(f"Error checking usability for skill {self.skill_id}: {e}")
            return False
    
    def put_on_cooldown(self, config_loader: 'ConfigLoader', 
                       star_level: Optional[int] = None):
        """
        將技能設置為冷卻狀態
        
        Args:
            config_loader: 配置加載器
            star_level: 星級（用於天賦被動技能）
        """
        try:
            definition = self.get_definition(config_loader, star_level)
            self.current_cooldown = definition.get("cooldown_turns", 0)
            
        except Exception as e:
            logger.error(f"Error putting skill {self.skill_id} on cooldown: {e}")
            # 設置默認冷卻
            self.current_cooldown = 1
    
    def tick_cooldown(self):
        """減少冷卻時間"""
        if self.current_cooldown > 0:
            self.current_cooldown -= 1
    
    def __repr__(self) -> str:
        return (f"SkillInstance(skill_id='{self.skill_id}', "
                f"type={self.skill_type.value}, level={self.current_level}, "
                f"cooldown={self.current_cooldown})")
    
    def __str__(self) -> str:
        return f"{self.skill_id} (Lv.{self.current_level})"
