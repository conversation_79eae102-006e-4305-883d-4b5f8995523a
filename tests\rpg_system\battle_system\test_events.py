"""
事件系統單元測試
"""
import unittest

from rpg_system.battle_system.events import (
    # 事件類型常量
    EVENT_ON_BATTLE_START, EVENT_ON_BATTLE_END, EVENT_ON_TURN_START,
    EVENT_ON_DEAL_DAMAGE, EVENT_ON_TAKE_DAMAGE, EVENT_ON_HEAL_DEALT,
    ALL_EVENT_TYPES, EVENT_DATA_TYPES, validate_event_data,
    
    # 事件數據類型
    EventOnBattleStartData, EventOnDealDamageData, EventOnHealDealtData
)


class TestEventConstants(unittest.TestCase):
    """事件常量測試類"""
    
    def test_event_type_constants(self):
        """測試事件類型常量"""
        # 測試基本事件類型
        self.assertEqual(EVENT_ON_BATTLE_START, "ON_BATTLE_START")
        self.assertEqual(EVENT_ON_BATTLE_END, "ON_BATTLE_END")
        self.assertEqual(EVENT_ON_TURN_START, "ON_TURN_START")
        self.assertEqual(EVENT_ON_DEAL_DAMAGE, "ON_DEAL_DAMAGE")
        self.assertEqual(EVENT_ON_TAKE_DAMAGE, "ON_TAKE_DAMAGE")
        self.assertEqual(EVENT_ON_HEAL_DEALT, "ON_HEAL_DEALT")
    
    def test_all_event_types_set(self):
        """測試所有事件類型集合"""
        # 檢查集合包含預期的事件類型
        self.assertIn(EVENT_ON_BATTLE_START, ALL_EVENT_TYPES)
        self.assertIn(EVENT_ON_DEAL_DAMAGE, ALL_EVENT_TYPES)
        self.assertIn(EVENT_ON_HEAL_DEALT, ALL_EVENT_TYPES)
        
        # 檢查集合大小合理
        self.assertGreaterEqual(len(ALL_EVENT_TYPES), 20)
    
    def test_event_data_types_mapping(self):
        """測試事件數據類型映射"""
        # 檢查映射包含預期的事件類型
        self.assertIn(EVENT_ON_BATTLE_START, EVENT_DATA_TYPES)
        self.assertIn(EVENT_ON_DEAL_DAMAGE, EVENT_DATA_TYPES)
        self.assertIn(EVENT_ON_HEAL_DEALT, EVENT_DATA_TYPES)
        
        # 檢查映射的值是正確的類型
        self.assertEqual(EVENT_DATA_TYPES[EVENT_ON_BATTLE_START], EventOnBattleStartData)
        self.assertEqual(EVENT_DATA_TYPES[EVENT_ON_DEAL_DAMAGE], EventOnDealDamageData)
        self.assertEqual(EVENT_DATA_TYPES[EVENT_ON_HEAL_DEALT], EventOnHealDealtData)


class TestEventDataValidation(unittest.TestCase):
    """事件數據驗證測試類"""
    
    def setUp(self):
        """測試前設置"""
        # 創建模擬對象
        self.mock_battle = object()
        self.mock_combatant = object()
        self.mock_skill = object()
    
    def test_validate_battle_start_event(self):
        """測試戰鬥開始事件驗證"""
        # 有效的事件數據
        valid_data = {
            "battle_context": self.mock_battle
        }
        
        self.assertTrue(validate_event_data(EVENT_ON_BATTLE_START, valid_data))
        
        # 無效的事件數據（缺少必需字段）
        invalid_data = {}
        
        self.assertFalse(validate_event_data(EVENT_ON_BATTLE_START, invalid_data))
    
    def test_validate_deal_damage_event(self):
        """測試造成傷害事件驗證"""
        # 有效的事件數據
        valid_data = {
            "battle_context": self.mock_battle,
            "caster": self.mock_combatant,
            "target": self.mock_combatant,
            "skill_instance": self.mock_skill,
            "status_effect_instance": None,
            "damage_amount": 100.0,
            "damage_type": "PHYSICAL",
            "is_crit": False,
            "is_miss": False,
            "source_element": None,
            "skill_tags": ["ATTACK"]
        }
        
        self.assertTrue(validate_event_data(EVENT_ON_DEAL_DAMAGE, valid_data))
        
        # 無效的事件數據（缺少必需字段）
        invalid_data = {
            "battle_context": self.mock_battle,
            "caster": self.mock_combatant,
            # 缺少其他必需字段
        }
        
        self.assertFalse(validate_event_data(EVENT_ON_DEAL_DAMAGE, invalid_data))
    
    def test_validate_heal_dealt_event(self):
        """測試造成治療事件驗證"""
        # 有效的事件數據
        valid_data = {
            "battle_context": self.mock_battle,
            "caster": self.mock_combatant,
            "target": self.mock_combatant,
            "skill_instance": self.mock_skill,
            "status_effect_instance": None,
            "heal_amount": 50.0,
            "skill_tags": ["HEAL"]
        }
        
        self.assertTrue(validate_event_data(EVENT_ON_HEAL_DEALT, valid_data))
        
        # 無效的事件數據（缺少必需字段）
        invalid_data = {
            "battle_context": self.mock_battle,
            "caster": self.mock_combatant,
            # 缺少其他必需字段
        }
        
        self.assertFalse(validate_event_data(EVENT_ON_HEAL_DEALT, invalid_data))
    
    def test_validate_unknown_event_type(self):
        """測試未知事件類型驗證"""
        unknown_event_type = "UNKNOWN_EVENT"
        valid_data = {"some_field": "some_value"}
        
        self.assertFalse(validate_event_data(unknown_event_type, valid_data))
    
    def test_validate_empty_event_data(self):
        """測試空事件數據驗證"""
        empty_data = {}
        
        # 對於任何已知事件類型，空數據都應該無效
        self.assertFalse(validate_event_data(EVENT_ON_BATTLE_START, empty_data))
        self.assertFalse(validate_event_data(EVENT_ON_DEAL_DAMAGE, empty_data))
        self.assertFalse(validate_event_data(EVENT_ON_HEAL_DEALT, empty_data))


class TestEventDataStructures(unittest.TestCase):
    """事件數據結構測試類"""
    
    def test_event_on_battle_start_data_structure(self):
        """測試戰鬥開始事件數據結構"""
        # 檢查TypedDict的註解
        annotations = EventOnBattleStartData.__annotations__

        self.assertIn("battle_context", annotations)
        # 檢查類型註解包含Battle（不檢查具體字符串格式）
        annotation_str = str(annotations["battle_context"])
        self.assertIn("Battle", annotation_str)
    
    def test_event_on_deal_damage_data_structure(self):
        """測試造成傷害事件數據結構"""
        # 檢查TypedDict的註解
        annotations = EventOnDealDamageData.__annotations__
        
        # 檢查必需字段
        required_fields = [
            "battle_context", "caster", "target", "skill_instance",
            "status_effect_instance", "damage_amount", "damage_type",
            "is_crit", "is_miss", "source_element", "skill_tags"
        ]
        
        for field in required_fields:
            self.assertIn(field, annotations)
    
    def test_event_on_heal_dealt_data_structure(self):
        """測試造成治療事件數據結構"""
        # 檢查TypedDict的註解
        annotations = EventOnHealDealtData.__annotations__
        
        # 檢查必需字段
        required_fields = [
            "battle_context", "caster", "target", "skill_instance",
            "status_effect_instance", "heal_amount", "skill_tags"
        ]
        
        for field in required_fields:
            self.assertIn(field, annotations)


class TestEventSystemIntegration(unittest.TestCase):
    """事件系統集成測試類"""
    
    def test_all_events_have_data_types(self):
        """測試所有事件都有對應的數據類型"""
        for event_type in ALL_EVENT_TYPES:
            self.assertIn(event_type, EVENT_DATA_TYPES,
                         f"事件類型 {event_type} 沒有對應的數據類型")
    
    def test_event_data_types_cover_all_events(self):
        """測試事件數據類型映射覆蓋所有事件"""
        mapped_events = set(EVENT_DATA_TYPES.keys())
        
        # 檢查是否有遺漏的事件類型
        missing_events = ALL_EVENT_TYPES - mapped_events
        self.assertEqual(len(missing_events), 0,
                        f"以下事件類型沒有數據類型映射: {missing_events}")
        
        # 檢查是否有多餘的映射
        extra_mappings = mapped_events - ALL_EVENT_TYPES
        self.assertEqual(len(extra_mappings), 0,
                        f"以下數據類型映射沒有對應的事件類型: {extra_mappings}")
    
    def test_event_type_naming_consistency(self):
        """測試事件類型命名一致性"""
        for event_type in ALL_EVENT_TYPES:
            # 所有事件類型都應該以 "ON_" 開頭
            self.assertTrue(event_type.startswith("ON_"),
                           f"事件類型 {event_type} 不以 'ON_' 開頭")
            
            # 事件類型應該是大寫
            self.assertEqual(event_type, event_type.upper(),
                           f"事件類型 {event_type} 不是大寫")
            
            # 事件類型不應該包含空格
            self.assertNotIn(" ", event_type,
                           f"事件類型 {event_type} 包含空格")


if __name__ == '__main__':
    unittest.main()
