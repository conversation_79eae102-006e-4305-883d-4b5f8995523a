"""
RPG Formula Evaluator
Safe expression evaluation using asteval for RPG system formulas
"""
import math
import logging
from typing import Dict, Any, Union, Optional
from asteval import Interpreter

logger = logging.getLogger(__name__)

class FormulaEvaluationError(Exception):
    """公式求值錯誤"""
    def __init__(self, message: str, formula: str = None, context: Dict[str, Any] = None):
        super().__init__(message)
        self.formula = formula
        self.context = context

class FormulaEvaluator:
    """
    RPG系統公式求值器

    使用asteval庫安全地計算JSON配置文件中定義的公式
    支持數學運算、邏輯判斷和預定義函數
    """

    def __init__(self):
        """初始化公式求值器"""
        self.interpreter = Interpreter()
        self._setup_safe_environment()

    def _setup_safe_environment(self):
        """設置安全的求值環境"""
        # 清除可能不安全的函數
        unsafe_functions = [
            'eval', 'exec', 'compile', 'open', 'input', 'raw_input',
            'reload', '__import__', 'globals', 'locals', 'vars', 'dir',
            'hasattr', 'getattr', 'setattr', 'delattr',
            'isinstance', 'issubclass', 'callable',
            'exit', 'quit'
        ]

        for func_name in unsafe_functions:
            if func_name in self.interpreter.symtable:
                del self.interpreter.symtable[func_name]

        # 明確禁用隨機函數
        random_functions = [
            'rand', 'random', 'randint', 'uniform', 'choice', 'shuffle',
            'rand_int', 'rand_float'
        ]

        for func_name in random_functions:
            self.interpreter.symtable[func_name] = None

        # 添加數學函數
        math_functions = {
            'min': min,
            'max': max,
            'abs': abs,
            'floor': math.floor,
            'ceil': math.ceil,
            'round': round,
            'sqrt': math.sqrt,
            'pow': pow,
            'ln': math.log,
            'log10': math.log10,
            'log': math.log,
            'PI': math.pi,
        }

        self.interpreter.symtable.update(math_functions)

        # 添加自定義函數
        self.interpreter.symtable['clamp'] = self._clamp
        # 避免與Python關鍵字衝突
        self.interpreter.symtable['if_func'] = self._if_function

    def _clamp(self, value: float, min_val: float, max_val: float) -> float:
        """將值限制在指定範圍內"""
        return max(min_val, min(value, max_val))

    def _if_function(self, condition: Any, value_if_true: Any,
                     value_if_false: Any) -> Any:
        """條件函數：if(condition, value_if_true, value_if_false)"""
        return value_if_true if condition else value_if_false

    def evaluate(self, formula_string: str,
                 context_vars: Dict[str, Any]) -> Union[float, int, bool]:
        """
        計算公式

        Args:
            formula_string: 要計算的公式字符串
            context_vars: 公式中可用的變量及其值

        Returns:
            計算結果

        Raises:
            FormulaEvaluationError: 公式求值失敗時拋出
        """
        if not formula_string or not isinstance(formula_string, str):
            logger.warning("空的或無效的公式字符串: %s", formula_string)
            return 0

        # 記錄原始符號表狀態以便恢復
        original_vars = {}
        context_var_names = set(context_vars.keys()) if context_vars else set()

        try:
            # 備份可能被覆蓋的變量
            for var_name in context_var_names:
                if var_name in self.interpreter.symtable:
                    original_vars[var_name] = self.interpreter.symtable[var_name]

            # 添加上下文變量到符號表
            if context_vars:
                self.interpreter.symtable.update(context_vars)

            # 執行求值
            result = self.interpreter.eval(formula_string)

            # 檢查求值錯誤 (asteval會在出錯時返回None或拋出異常)
            if result is None and formula_string.strip():
                # 如果公式不為空但結果為None，可能是求值錯誤
                logger.error("公式求值可能失敗: 公式: %s, 上下文: %s",
                           formula_string, context_vars)
                raise FormulaEvaluationError(
                    "公式求值失敗，結果為None",
                    formula=formula_string,
                    context=context_vars
                )

            # 處理特殊結果
            if result is None:
                logger.warning("公式求值結果為None: %s, 上下文: %s",
                              formula_string, context_vars)
                return 0

            # 確保結果是數值類型
            if isinstance(result, (int, float, bool)):
                return result
            else:
                logger.warning("公式求值結果類型異常: %s (類型: %s), 公式: %s",
                             result, type(result), formula_string)
                return 0

        except Exception as e:
            if isinstance(e, FormulaEvaluationError):
                raise

            logger.error("公式求值異常: %s, 公式: %s, 上下文: %s",
                        str(e), formula_string, context_vars, exc_info=True)
            raise FormulaEvaluationError(
                f"公式求值異常: {str(e)}",
                formula=formula_string,
                context=context_vars
            ) from e

        finally:
            # 清理上下文變量，恢復原始狀態
            for var_name in context_var_names:
                if var_name in original_vars:
                    # 恢復原始值
                    self.interpreter.symtable[var_name] = original_vars[var_name]
                else:
                    # 移除新添加的變量
                    if var_name in self.interpreter.symtable:
                        del self.interpreter.symtable[var_name]

            # asteval的Interpreter沒有errors屬性，不需要清理

    def validate_formula(self, formula_string: str) -> bool:
        """
        驗證公式語法是否正確

        Args:
            formula_string: 要驗證的公式字符串

        Returns:
            True if valid, False otherwise
        """
        # 檢查空字符串
        if not formula_string or not formula_string.strip():
            return False

        try:
            # 使用空上下文進行語法檢查
            self.evaluate(formula_string, {})
            return True
        except FormulaEvaluationError:
            return False
        except Exception:
            return False
