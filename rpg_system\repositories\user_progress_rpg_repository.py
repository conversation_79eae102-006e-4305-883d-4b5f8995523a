"""
用戶進度RPG倉庫

負責與 rpg_user_progress 表的交互
"""
from typing import List, Optional
import json
import logging
from dataclasses import dataclass

import asyncpg

from database.base_repository import BaseRepository

logger = logging.getLogger(__name__)


class UserProgressRPGRepositoryError(Exception):
    """用戶進度RPG倉庫異常"""
    pass


@dataclass
class UserProgressDBData:
    """用戶進度數據庫數據模型"""
    user_id: int
    current_floor_unlocked: int
    current_floor_wins: int
    max_floor_cleared: int
    current_team_formation: List[Optional[int]]  # gacha_user_collections.id 列表


class UserProgressRPGRepository(BaseRepository):
    """
    用戶進度RPG倉庫

    負責與 rpg_user_progress 表的交互
    """

    def __init__(self, pool: asyncpg.Pool):
        """
        初始化用戶進度RPG倉庫

        Args:
            pool: asyncpg 連接池
        """
        super().__init__(pool)
        self.table_name = 'rpg_user_progress'

    def _parse_user_progress_data(self, row) -> Optional[UserProgressDBData]:
        """
        解析數據庫行為 UserProgressDBData 對象

        Args:
            row: 數據庫查詢結果行

        Returns:
            UserProgressDBData 對象或 None
        """
        if not row:
            return None

        try:
            # 解析 JSON 字段
            current_team_formation = []
            if row.get('current_team_formation'):
                if isinstance(row['current_team_formation'], str):
                    current_team_formation = json.loads(row['current_team_formation'])
                else:
                    current_team_formation = row['current_team_formation']

            return UserProgressDBData(
                user_id=row['user_id'],
                current_floor_unlocked=row.get('current_floor_unlocked', 1),
                current_floor_wins=row.get('current_floor_wins', 0),
                max_floor_cleared=row.get('max_floor_cleared', 0),
                current_team_formation=current_team_formation
            )
        except Exception as e:
            logger.error(f"解析用戶進度數據失敗: {e}")
            return None

    async def get_user_progress(self, user_id: int) -> Optional[UserProgressDBData]:
        """
        獲取用戶的PVE進度，如果不存在則創建默認記錄

        Args:
            user_id: 用戶ID

        Returns:
            UserProgressDBData 對象或 None
        """
        try:
            query = f"""
                SELECT user_id, current_floor_unlocked, current_floor_wins,
                       max_floor_cleared, current_team_formation
                FROM {self.table_name}
                WHERE user_id = $1
            """

            row = await self._fetchrow(query, [user_id])

            if row:
                return self._parse_user_progress_data(row)
            else:
                # 如果記錄不存在，創建默認進度記錄
                logger.info(f"用戶進度記錄不存在，創建默認記錄: user_id={user_id}")
                return await self.create_user_progress(user_id)

        except Exception as e:
            logger.error(f"獲取用戶進度失敗: user_id={user_id}, error={e}")
            raise UserProgressRPGRepositoryError(f"獲取用戶進度失敗: {str(e)}") from e

    async def create_user_progress(self, user_id: int) -> UserProgressDBData:
        """
        為新用戶創建默認的進度記錄

        Args:
            user_id: 用戶ID

        Returns:
            創建的 UserProgressDBData 對象
        """
        try:
            # 默認隊伍編成為空
            default_team_formation = json.dumps([])

            query = f"""
                INSERT INTO {self.table_name}
                (user_id, current_floor_unlocked, current_floor_wins, max_floor_cleared, current_team_formation)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING user_id, current_floor_unlocked, current_floor_wins,
                          max_floor_cleared, current_team_formation
            """

            row = await self._fetchrow(query, [
                user_id,
                1,  # 默認解鎖第1層
                0,  # 默認勝利次數為0
                0,  # 默認最高通關層數為0
                default_team_formation
            ])

            result = self._parse_user_progress_data(row)
            if not result:
                raise UserProgressRPGRepositoryError("創建用戶進度記錄後解析失敗")

            return result

        except Exception as e:
            logger.error(f"創建用戶進度失敗: user_id={user_id}, error={e}")
            raise UserProgressRPGRepositoryError(f"創建用戶進度失敗: {str(e)}") from e

    async def update_user_progress(self, user_progress_data: UserProgressDBData) -> bool:
        """
        更新用戶的PVE進度記錄

        Args:
            user_progress_data: 用戶進度數據對象

        Returns:
            更新是否成功
        """
        try:
            # 將隊伍編成轉換為JSON
            team_formation_json = json.dumps(user_progress_data.current_team_formation)

            query = f"""
                UPDATE {self.table_name}
                SET current_floor_unlocked = $2, current_floor_wins = $3,
                    max_floor_cleared = $4, current_team_formation = $5
                WHERE user_id = $1
            """

            result = await self._execute(query, [
                user_progress_data.user_id,
                user_progress_data.current_floor_unlocked,
                user_progress_data.current_floor_wins,
                user_progress_data.max_floor_cleared,
                team_formation_json
            ])

            # 檢查是否有行被更新
            return result.split()[-1] == '1'

        except Exception as e:
            logger.error(f"更新用戶進度失敗: user_id={user_progress_data.user_id}, error={e}")
            raise UserProgressRPGRepositoryError(f"更新用戶進度失敗: {str(e)}") from e

    async def update_team_formation(self, user_id: int, team_formation_collection_ids: List[Optional[int]]) -> bool:
        """
        單獨更新用戶的隊伍編成

        Args:
            user_id: 用戶ID
            team_formation_collection_ids: 隊伍編成的收藏ID列表

        Returns:
            更新是否成功
        """
        try:
            # 將隊伍編成轉換為JSON
            team_formation_json = json.dumps(team_formation_collection_ids)

            query = f"""
                UPDATE {self.table_name}
                SET current_team_formation = $2
                WHERE user_id = $1
            """

            result = await self._execute(query, [user_id, team_formation_json])

            # 檢查是否有行被更新
            return result.split()[-1] == '1'

        except Exception as e:
            logger.error(f"更新隊伍編成失敗: user_id={user_id}, error={e}")
            raise UserProgressRPGRepositoryError(f"更新隊伍編成失敗: {str(e)}") from e
