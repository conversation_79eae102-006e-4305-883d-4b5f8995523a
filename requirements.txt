# Core Discord Bot Dependencies
discord.py>=2.0.1
asyncpg
psycopg2-binary>=2.9.0
redis[hiredis]>=4.3.4
msgpack>=1.0.0  # For Redis message packing/unpacking
dill>=0.3.0  # For advanced Python object serialization in Redis cache

# Configuration and Environment
PyYAML>=6.0  # For YAML configuration files
python-dotenv>=1.0.0  # For .env file loading

# HTTP and Web Dependencies (Security Updated)
aiohttp>=3.10.11  # Security update from 3.8.1
requests>=2.32.0  # Security update (indirect dependency)
certifi>=2024.7.4  # Security update (indirect dependency)

# Image Processing
pillow>=9.0.0
olefile>=0.46 # Optional Pillow dependency for some formats (e.g., FPX)
matplotlib>=3.5.0  # For stock price chart generation

# Data Processing and Validation
pydantic>=2.0.0,<3.0.0 # Added Pydantic V2
pydantic-settings>=0.1.0 # Added pydantic-settings

# Formula Engine (RPG System)
asteval>=0.9.31  # Safe expression evaluation for RPG formulas

# Time Zone Support
pytz  # For timezone operations

# Retry Logic for AI Services
tenacity>=8.0.0  # For retry logic in AI service calls

# Google Cloud (if actually needed - check usage)
google-auth>=2.22.0

# Web Automation
playwright>=1.40.0 # Replaced Selenium with Playwright

# Template Engine Security Update (indirect dependency)
jinja2>=3.1.6  # Security update

# Web Server Security Update (if using in production)
gunicorn>=23.0.0  # Security update

# HTTP Client Security Update
h11>=0.16.0  # Security update (indirect dependency)
idna>=3.7  # Security update (indirect dependency)

# Removed unused dependencies based on deptry analysis:
# - cairosvg (DEP002: not used)
# - jsonschema (DEP002: not used) 
# - google-cloud-aiplatform (DEP002: not used)
# - vertexai (DEP002: not used)
# - imageio (DEP002: not used)
# - numpy (DEP002: not used - if needed, it will be installed as transitive dependency)
# - tqdm (DEP002: not used)
# - concurrent-log-handler (DEP002: not used)
# - python-json-logger (DEP002: not used)
# - webdriver-manager (DEP002: not used)
# - dependency-injector (DEP002: not used)