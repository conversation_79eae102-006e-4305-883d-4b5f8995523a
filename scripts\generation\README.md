# RPG 系統數據生成腳本

這個目錄包含了用於初始化和設置 RPG 系統的腳本。

## 📁 文件說明

### 核心腳本

- **`setup_rpg_system.py`** - 一鍵設置腳本，自動完成所有初始化工作
- **`generate_rpg_configs.py`** - 生成 RPG 配置 JSON 文件的主腳本
- **`generate_rpg_configs_part2.py`** - 配置生成腳本第二部分
- **`generate_rpg_configs_part3.py`** - 配置生成腳本第三部分
- **`initialize_rpg_database.py`** - 數據庫表初始化腳本

## 🚀 快速開始

### 方法一：一鍵設置（推薦）

```bash
# 完整設置（包括配置文件生成和數據庫初始化）
python scripts/generation/setup_rpg_system.py --database-url "postgresql://user:pass@localhost/dbname"

# 預覽模式（查看將要執行的操作）
python scripts/generation/setup_rpg_system.py --dry-run

# 只生成配置文件
python scripts/generation/setup_rpg_system.py --skip-database

# 只初始化數據庫
python scripts/generation/setup_rpg_system.py --skip-configs --database-url "postgresql://user:pass@localhost/dbname"
```

### 方法二：分步執行

#### 1. 生成配置文件

```bash
# 生成所有配置文件
python scripts/generation/generate_rpg_configs.py

# 生成到指定目錄
python scripts/generation/generate_rpg_configs.py --output-dir "custom/config/path"

# 只生成特定類型的配置
python scripts/generation/generate_rpg_configs.py --config-types active_skills passive_skills cards

# 啟用 Pydantic 驗證（可能會因為模型複雜度而失敗）
python scripts/generation/generate_rpg_configs.py --validate
```

#### 2. 初始化數據庫

```bash
# 使用命令行參數指定數據庫 URL
python scripts/generation/initialize_rpg_database.py --database-url "postgresql://user:pass@localhost/dbname"

# 使用環境變量（設置 DATABASE_URL）
export DATABASE_URL="postgresql://user:pass@localhost/dbname"
python scripts/generation/initialize_rpg_database.py

# 預覽 SQL 而不執行
python scripts/generation/initialize_rpg_database.py --dry-run
```

## 📋 生成的配置文件

腳本會生成以下配置文件：

### 核心配置
- **`effect_templates.json`** - 效果模板配置
- **`status_effects.json`** - 狀態效果配置
- **`active_skills.json`** - 主動技能配置
- **`passive_skills.json`** - 被動技能配置
- **`innate_passive_skills.json`** - 天賦被動技能配置

### 遊戲內容
- **`cards.json`** - 卡牌配置
- **`monsters.json`** - 怪物配置
- **`monster_groups.json`** - 怪物群組配置
- **`floors.json`** - 樓層配置
- **`reward_packages.json`** - 獎勵包配置
- **`star_level_effects.json`** - 星級效果配置

## 🗄️ 數據庫表結構

腳本會創建/修改以下數據庫表：

### 新建表
- **`gacha_user_learned_global_skills`** - 用戶學習的全局技能
- **`rpg_user_progress`** - 用戶 RPG 進度

### 修改現有表
- **`gacha_user_collections`** - 添加 RPG 相關字段：
  - `rpg_level` - RPG 等級
  - `rpg_xp` - RPG 經驗值
  - `equipped_active_skill_ids` - 已裝備的主動技能
  - `equipped_common_passives` - 已裝備的被動技能
  - `star_level` - 星級

## ⚙️ 配置選項

### 環境變量

- **`DATABASE_URL`** - 數據庫連接 URL

### 命令行參數

#### setup_rpg_system.py
- `--database-url` - 數據庫連接 URL
- `--config-output-dir` - 配置文件輸出目錄
- `--skip-database` - 跳過數據庫初始化
- `--skip-configs` - 跳過配置文件生成
- `--dry-run` - 預覽模式

#### generate_rpg_configs.py
- `--output-dir` - 輸出目錄（默認：rpg_system/config/generated）
- `--config-types` - 要生成的配置類型
- `--validate` - 啟用 Pydantic 驗證

#### initialize_rpg_database.py
- `--database-url` - 數據庫連接 URL
- `--dry-run` - 只預覽 SQL 而不執行

## 🔧 故障排除

### 常見問題

1. **導入錯誤**
   ```
   ImportError: No module named 'rpg_system'
   ```
   確保從項目根目錄運行腳本，或者正確設置 PYTHONPATH。

2. **數據庫連接失敗**
   ```
   asyncpg.exceptions.InvalidCatalogNameError
   ```
   檢查數據庫 URL 是否正確，數據庫是否存在。

3. **權限錯誤**
   ```
   PermissionError: [Errno 13] Permission denied
   ```
   確保對輸出目錄有寫入權限。

4. **Pydantic 驗證失敗**
   ```
   ValidationError: ...
   ```
   這是正常的，因為生成的數據可能不完全符合複雜的 Pydantic 模型。使用 `--validate` 參數時可能會遇到此問題。

### 調試技巧

1. **使用預覽模式**
   ```bash
   python scripts/generation/setup_rpg_system.py --dry-run
   ```

2. **分步執行**
   先生成配置文件，再初始化數據庫，便於定位問題。

3. **檢查日誌**
   腳本會輸出詳細的執行日誌，注意查看錯誤信息。

## 📝 自定義配置

如果需要修改生成的配置數據：

1. 編輯對應的生成函數（如 `generate_active_skills_config`）
2. 修改數據字典中的值
3. 重新運行生成腳本

## 🔄 更新配置

當需要更新配置時：

1. 修改生成腳本中的數據
2. 重新運行配置生成：
   ```bash
   python scripts/generation/generate_rpg_configs.py
   ```
3. 重啟應用程序以加載新配置

## 📞 支持

如果遇到問題，請檢查：
1. Python 版本是否兼容（推薦 3.8+）
2. 所有依賴是否已安裝
3. 數據庫連接是否正常
4. 文件權限是否正確
