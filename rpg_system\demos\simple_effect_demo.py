#!/usr/bin/env python3
"""
簡化的效果系統演示腳本
"""
import sys
import os

# 添加項目根目錄到路徑
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from rpg_system.battle_system.handlers.target_selector import TargetSelector
from rpg_system.battle_system.handlers.effect_applier import EffectApplier
from rpg_system.formula_engine.evaluator import FormulaEvaluator


def demo_target_selector():
    """演示目標選擇器功能"""
    print("=== 目標選擇器演示 ===")
    
    # 創建模擬戰鬥上下文
    class MockBattle:
        def __init__(self):
            self.current_turn = 1
            
        def get_alive_combatants(self, player_side=None):
            # 模擬戰鬥者
            class MockCombatant:
                def __init__(self, cid, is_player, hp):
                    self.combatant_id = cid
                    self.is_player = is_player
                    self.current_hp = hp
                    self.current_mp = 50
                    self.rpg_level = 10
                    self.star_level = 3
                    
                def get_hp_percent(self):
                    return self.current_hp / 100.0
                    
                def get_mp_percent(self):
                    return self.current_mp / 100.0
                    
                def get_current_stats(self):
                    class Stats:
                        def __init__(self):
                            self.patk = 100
                            self.pdef = 50
                            self.matk = 80
                            self.mdef = 40
                            self.spd = 70
                            self.crit_rate = 0.1
                            self.crit_dmg_multiplier = 1.5
                            self.accuracy = 0.95
                            self.evasion = 0.05
                    return Stats()
            
            all_combatants = [
                MockCombatant("player1", True, 100),
                MockCombatant("player2", True, 80),
                MockCombatant("enemy1", False, 120),
                MockCombatant("enemy2", False, 90),
            ]
            
            if player_side is None:
                return all_combatants
            else:
                return [c for c in all_combatants if c.is_player == player_side]
    
    # 創建模擬依賴
    class MockConfigLoader:
        pass
    
    # 創建實例
    selector = TargetSelector()
    evaluator = FormulaEvaluator()
    config_loader = MockConfigLoader()
    battle = MockBattle()
    
    # 創建施法者
    class MockCaster:
        def __init__(self):
            self.combatant_id = "caster"
            self.is_player = True
            self.current_hp = 100
            self.current_mp = 50
            self.rpg_level = 10
            self.star_level = 3
            
        def get_hp_percent(self):
            return 1.0
            
        def get_mp_percent(self):
            return 0.5
            
        def get_current_stats(self):
            class Stats:
                def __init__(self):
                    self.patk = 100
                    self.pdef = 50
                    self.matk = 80
                    self.mdef = 40
                    self.spd = 70
                    self.crit_rate = 0.1
                    self.crit_dmg_multiplier = 1.5
                    self.accuracy = 0.95
                    self.evasion = 0.05
            return Stats()
    
    caster = MockCaster()
    
    # 測試1: 選擇所有敵人
    print("\n1. 選擇所有敵人:")
    target_logic = {"base_pool": "ENEMIES"}
    targets = selector.select_targets(caster, target_logic, battle, evaluator, config_loader)
    print(f"   選中目標: {[t.combatant_id for t in targets]}")
    
    # 測試2: 選擇血量最低的敵人
    print("\n2. 選擇血量最低的敵人:")
    target_logic = {
        "base_pool": "ENEMIES",
        "sort_by": "current_hp",
        "sort_order": "ASC",
        "count_logic": "FIXED",
        "count": 1,
        "selection_strategy": "FIRST_N"
    }
    targets = selector.select_targets(caster, target_logic, battle, evaluator, config_loader)
    print(f"   選中目標: {[t.combatant_id for t in targets]} (血量: {[t.current_hp for t in targets]})")
    
    # 測試3: 選擇血量低於100的敵人
    print("\n3. 選擇血量低於100的敵人:")
    target_logic = {
        "base_pool": "ENEMIES",
        "conditions": [
            {"formula": "target_current_hp < 100"}
        ]
    }
    targets = selector.select_targets(caster, target_logic, battle, evaluator, config_loader)
    print(f"   選中目標: {[t.combatant_id for t in targets]} (血量: {[t.current_hp for t in targets]})")


def demo_effect_applier():
    """演示效果應用器功能"""
    print("\n\n=== 效果應用器演示 ===")
    
    # 創建模擬對象
    class MockTarget:
        def __init__(self):
            self.combatant_id = "target"
            self.current_hp = 80
            self.current_mp = 30
            self.rpg_level = 8
            self.star_level = 2
            
        def get_hp_percent(self):
            return self.current_hp / 100.0
            
        def get_mp_percent(self):
            return self.current_mp / 50.0
            
        def get_current_stats(self):
            class Stats:
                def __init__(self):
                    self.patk = 60
                    self.pdef = 30
                    self.matk = 40
                    self.mdef = 20
                    self.spd = 50
                    self.crit_rate = 0.05
                    self.crit_dmg_multiplier = 1.2
                    self.accuracy = 0.9
                    self.evasion = 0.1
            return Stats()
            
        def take_damage(self, damage):
            actual_damage = min(damage, self.current_hp)
            self.current_hp -= actual_damage
            return actual_damage
            
        def heal(self, amount):
            actual_heal = min(amount, 100 - self.current_hp)
            self.current_hp += actual_heal
            return actual_heal
            
        def restore_mp(self, amount):
            actual_restore = min(amount, 50 - self.current_mp)
            self.current_mp += actual_restore
            return actual_restore
    
    class MockCaster:
        def __init__(self):
            self.combatant_id = "caster"
            self.is_player = True
            self.current_hp = 100
            self.current_mp = 50
            self.rpg_level = 10
            self.star_level = 3

        def get_hp_percent(self):
            return 1.0

        def get_mp_percent(self):
            return 0.5

        def get_current_stats(self):
            class Stats:
                def __init__(self):
                    self.patk = 100
                    self.pdef = 50
                    self.matk = 80
                    self.mdef = 40
                    self.spd = 70
                    self.crit_rate = 0.1
                    self.crit_dmg_multiplier = 1.5
                    self.accuracy = 0.95
                    self.evasion = 0.05
            return Stats()
    
    class MockBattle:
        def __init__(self):
            self.current_turn = 1
            
        def get_alive_combatants(self, player_side=None):
            return []
    
    # 創建實例
    selector = TargetSelector()
    evaluator = FormulaEvaluator()
    config_loader = object()  # 簡單的mock
    applier = EffectApplier(evaluator, selector, config_loader)
    
    caster = MockCaster()
    target = MockTarget()
    battle = MockBattle()
    
    print(f"\n施法者: {caster.combatant_id} (HP: {caster.current_hp}, MP: {caster.current_mp})")
    print(f"目標: {target.combatant_id} (HP: {target.current_hp}, MP: {target.current_mp})")
    
    # 測試1: 傷害效果
    print("\n1. 應用傷害效果:")
    damage_effects = [
        {
            "type": "DAMAGE",
            "value_formula": "caster_stat_matk * 1.2",
            "damage_type": "MAGICAL"
        }
    ]
    
    log_entries = applier.apply_effect_definitions(
        caster, [target], damage_effects, battle, ["FIRE", "MAGIC"]
    )
    
    for entry in log_entries:
        if entry.event_type == "DAMAGE":
            print(f"   造成傷害: {entry.damage_dealt}")
            print(f"   目標剩餘HP: {target.current_hp}")
    
    # 測試2: 治療效果
    print("\n2. 應用治療效果:")
    heal_effects = [
        {
            "type": "HEAL",
            "value_formula": "caster_stat_matk * 0.8"
        }
    ]
    
    log_entries = applier.apply_effect_definitions(
        caster, [target], heal_effects, battle, ["HEAL", "MAGIC"]
    )
    
    for entry in log_entries:
        if entry.event_type == "HEALING":
            print(f"   治療量: {entry.healing_done}")
            print(f"   目標當前HP: {target.current_hp}")


def main():
    """主函數"""
    print("RPG 效果系統簡化演示")
    print("=" * 50)
    
    try:
        demo_target_selector()
        demo_effect_applier()
        
        print("\n\n演示完成！")
        print("效果系統功能包括:")
        print("- 靈活的目標選擇 (基礎池、條件過濾、排序、數量控制)")
        print("- 多種效果類型 (傷害、治療、MP操作)")
        print("- 公式驅動的數值計算")
        print("- 完整的戰鬥日誌記錄")
        
    except Exception as e:
        print(f"演示過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
