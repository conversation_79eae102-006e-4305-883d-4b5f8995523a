"""
全局技能倉庫

負責與 gacha_user_learned_global_skills 表的交互
"""
from typing import List, Optional, Literal
import logging
from datetime import datetime
from dataclasses import dataclass

import asyncpg

from rpg_system.repositories.player_collection_rpg_repository import BaseRepository

logger = logging.getLogger(__name__)


class GlobalSkillRepositoryError(Exception):
    """全局技能倉庫異常"""
    pass


@dataclass
class LearnedGlobalSkillDBData:
    """已學習全局技能數據庫數據模型"""
    user_id: int
    skill_id: str
    skill_type: Literal["ACTIVE", "PASSIVE"]
    skill_level: int
    skill_xp: int
    unlocked_at: datetime


class GlobalSkillRepository(BaseRepository):
    """
    全局技能倉庫

    負責與 gacha_user_learned_global_skills 表的交互
    """

    def __init__(self, pool: asyncpg.Pool):
        """
        初始化全局技能倉庫

        Args:
            pool: asyncpg 連接池
        """
        super().__init__(pool)
        self.table_name = 'gacha_user_learned_global_skills'

    def _parse_learned_skill_data(self, row) -> Optional[LearnedGlobalSkillDBData]:
        """
        解析數據庫行為 LearnedGlobalSkillDBData 對象

        Args:
            row: 數據庫查詢結果行

        Returns:
            LearnedGlobalSkillDBData 對象或 None
        """
        if not row:
            return None

        try:
            return LearnedGlobalSkillDBData(
                user_id=row['user_id'],
                skill_id=row['skill_id'],
                skill_type=row['skill_type'],
                skill_level=row['skill_level'],
                skill_xp=row['skill_xp'],
                unlocked_at=row['unlocked_at']
            )
        except Exception as e:
            logger.error(f"解析已學習技能數據失敗: {e}")
            return None

    async def get_learned_skill(
        self,
        user_id: int,
        skill_id: str,
        skill_type: Literal["ACTIVE", "PASSIVE"]
    ) -> Optional[LearnedGlobalSkillDBData]:
        """
        獲取特定用戶學習的特定技能

        Args:
            user_id: 用戶ID
            skill_id: 技能ID
            skill_type: 技能類型

        Returns:
            LearnedGlobalSkillDBData 對象或 None
        """
        try:
            query = f"""
                SELECT user_id, skill_id, skill_type, skill_level, skill_xp, unlocked_at
                FROM {self.table_name}
                WHERE user_id = $1 AND skill_id = $2 AND skill_type = $3
            """

            row = await self._fetchrow(query, (user_id, skill_id, skill_type))
            return self._parse_learned_skill_data(row)

        except Exception as e:
            logger.error(f"獲取已學習技能失敗: user_id={user_id}, skill_id={skill_id}, error={e}")
            raise GlobalSkillRepositoryError(f"獲取已學習技能失敗: {str(e)}") from e
