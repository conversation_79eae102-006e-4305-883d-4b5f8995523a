"""
全局技能倉庫

負責與 gacha_user_learned_global_skills 表的交互
"""
from typing import List, Optional, Literal
import logging
from datetime import datetime
from dataclasses import dataclass

import asyncpg

from database.base_repository import BaseRepository

logger = logging.getLogger(__name__)


class GlobalSkillRepositoryError(Exception):
    """全局技能倉庫異常"""
    pass


@dataclass
class LearnedGlobalSkillDBData:
    """已學習全局技能數據庫數據模型"""
    user_id: int
    skill_id: str
    skill_type: Literal["ACTIVE", "PASSIVE"]
    skill_level: int
    skill_xp: int
    unlocked_at: datetime


class GlobalSkillRepository(BaseRepository):
    """
    全局技能倉庫

    負責與 gacha_user_learned_global_skills 表的交互
    """

    def __init__(self, pool: asyncpg.Pool):
        """
        初始化全局技能倉庫

        Args:
            pool: asyncpg 連接池
        """
        super().__init__(pool)
        self.table_name = 'gacha_user_learned_global_skills'

    def _parse_learned_skill_data(self, row) -> Optional[LearnedGlobalSkillDBData]:
        """
        解析數據庫行為 LearnedGlobalSkillDBData 對象

        Args:
            row: 數據庫查詢結果行

        Returns:
            LearnedGlobalSkillDBData 對象或 None
        """
        if not row:
            return None

        try:
            return LearnedGlobalSkillDBData(
                user_id=row['user_id'],
                skill_id=row['skill_id'],
                skill_type=row['skill_type'],
                skill_level=row['skill_level'],
                skill_xp=row['skill_xp'],
                unlocked_at=row['unlocked_at']
            )
        except Exception as e:
            logger.error(f"解析已學習技能數據失敗: {e}")
            return None

    async def get_learned_skill(
        self,
        user_id: int,
        skill_id: str,
        skill_type: Literal["ACTIVE", "PASSIVE"]
    ) -> Optional[LearnedGlobalSkillDBData]:
        """
        獲取特定用戶學習的特定技能

        Args:
            user_id: 用戶ID
            skill_id: 技能ID
            skill_type: 技能類型

        Returns:
            LearnedGlobalSkillDBData 對象或 None
        """
        try:
            query = f"""
                SELECT user_id, skill_id, skill_type, skill_level, skill_xp, unlocked_at
                FROM {self.table_name}
                WHERE user_id = $1 AND skill_id = $2 AND skill_type = $3
            """

            row = await self._fetchrow(query, [user_id, skill_id, skill_type])
            return self._parse_learned_skill_data(row)

        except Exception as e:
            logger.error(f"獲取已學習技能失敗: user_id={user_id}, skill_id={skill_id}, error={e}")
            raise GlobalSkillRepositoryError(f"獲取已學習技能失敗: {str(e)}") from e

    async def get_all_learned_skills_by_user_id(
        self,
        user_id: int,
        skill_type: Optional[Literal["ACTIVE", "PASSIVE"]] = None
    ) -> List[LearnedGlobalSkillDBData]:
        """
        獲取用戶學習的所有技能

        Args:
            user_id: 用戶ID
            skill_type: 技能類型過濾（可選）

        Returns:
            LearnedGlobalSkillDBData 對象列表
        """
        try:
            if skill_type:
                query = f"""
                    SELECT user_id, skill_id, skill_type, skill_level, skill_xp, unlocked_at
                    FROM {self.table_name}
                    WHERE user_id = $1 AND skill_type = $2
                    ORDER BY skill_id
                """
                rows = await self._fetch(query, [user_id, skill_type])
            else:
                query = f"""
                    SELECT user_id, skill_id, skill_type, skill_level, skill_xp, unlocked_at
                    FROM {self.table_name}
                    WHERE user_id = $1
                    ORDER BY skill_type, skill_id
                """
                rows = await self._fetch(query, [user_id])

            result = []
            for row in rows:
                skill_data = self._parse_learned_skill_data(row)
                if skill_data:
                    result.append(skill_data)

            return result

        except Exception as e:
            logger.error(f"獲取用戶已學習技能列表失敗: user_id={user_id}, error={e}")
            raise GlobalSkillRepositoryError(f"獲取用戶已學習技能列表失敗: {str(e)}") from e

    async def add_learned_skill(
        self,
        user_id: int,
        skill_id: str,
        skill_type: Literal["ACTIVE", "PASSIVE"],
        initial_level: int = 1,
        initial_xp: int = 0
    ) -> Optional[LearnedGlobalSkillDBData]:
        """
        添加新的已學習技能記錄

        Args:
            user_id: 用戶ID
            skill_id: 技能ID
            skill_type: 技能類型
            initial_level: 初始等級
            initial_xp: 初始經驗值

        Returns:
            創建的 LearnedGlobalSkillDBData 對象或 None
        """
        try:
            query = f"""
                INSERT INTO {self.table_name}
                (user_id, skill_id, skill_type, skill_level, skill_xp, unlocked_at)
                VALUES ($1, $2, $3, $4, $5, $6)
                RETURNING user_id, skill_id, skill_type, skill_level, skill_xp, unlocked_at
            """

            now = datetime.now()
            row = await self._fetchrow(query, [
                user_id, skill_id, skill_type, initial_level, initial_xp, now
            ])

            return self._parse_learned_skill_data(row)

        except Exception as e:
            logger.error(f"添加已學習技能失敗: user_id={user_id}, skill_id={skill_id}, error={e}")
            raise GlobalSkillRepositoryError(f"添加已學習技能失敗: {str(e)}") from e

    async def update_learned_skill_progress(
        self,
        user_id: int,
        skill_id: str,
        skill_type: Literal["ACTIVE", "PASSIVE"],
        new_level: int,
        new_xp: int
    ) -> bool:
        """
        更新已學習技能的等級和經驗

        Args:
            user_id: 用戶ID
            skill_id: 技能ID
            skill_type: 技能類型
            new_level: 新等級
            new_xp: 新經驗值

        Returns:
            更新是否成功
        """
        try:
            query = f"""
                UPDATE {self.table_name}
                SET skill_level = $4, skill_xp = $5
                WHERE user_id = $1 AND skill_id = $2 AND skill_type = $3
            """

            result = await self._execute(query, [user_id, skill_id, skill_type, new_level, new_xp])

            # 檢查是否有行被更新
            return result.split()[-1] == '1'

        except Exception as e:
            logger.error(f"更新已學習技能進度失敗: user_id={user_id}, skill_id={skill_id}, error={e}")
            raise GlobalSkillRepositoryError(f"更新已學習技能進度失敗: {str(e)}") from e

    async def delete_learned_skill(
        self,
        user_id: int,
        skill_id: str,
        skill_type: Literal["ACTIVE", "PASSIVE"]
    ) -> bool:
        """
        刪除已學習技能記錄（遺忘技能）

        Args:
            user_id: 用戶ID
            skill_id: 技能ID
            skill_type: 技能類型

        Returns:
            刪除是否成功
        """
        try:
            query = f"""
                DELETE FROM {self.table_name}
                WHERE user_id = $1 AND skill_id = $2 AND skill_type = $3
            """

            result = await self._execute(query, [user_id, skill_id, skill_type])

            # 檢查是否有行被刪除
            return result.split()[-1] == '1'

        except Exception as e:
            logger.error(f"刪除已學習技能失敗: user_id={user_id}, skill_id={skill_id}, error={e}")
            raise GlobalSkillRepositoryError(f"刪除已學習技能失敗: {str(e)}") from e
