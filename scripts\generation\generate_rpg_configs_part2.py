"""
RPG 配置數據生成腳本 - 第二部分

繼續實現其他配置文件的生成函數
"""
import os
import sys
from pathlib import Path
from typing import Dict, Any

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from scripts.generation.generate_rpg_configs import save_config_to_json
from rpg_system.config.pydantic_models.innate_passive_skills_models import AllInnatePassiveSkillsConfig
from rpg_system.config.pydantic_models.cards_models import AllCardsConfig
from rpg_system.config.pydantic_models.monsters_models import AllMonstersConfig
from rpg_system.config.pydantic_models.reward_packages_models import AllRewardPackagesConfig
from rpg_system.config.pydantic_models.monster_groups_models import AllMonsterGroupsConfig
from rpg_system.config.pydantic_models.floors_models import AllFloorsConfig


def generate_innate_passive_skills_config(output_dir: str) -> bool:
    """生成天賦被動技能配置"""
    print("🔧 生成天賦被動技能配置...")
    
    innate_passive_skills = {
        "WARRIOR_SPIRIT": {
            "name": "戰士之魂",
            "description_template": "戰士系卡牌的天賦，隨星級提升攻擊力",
            "effects_by_star_level": {
                "0": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "attack",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.05"
                                }
                            ]
                        }
                    ]
                },
                "1": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "attack",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.1"
                                }
                            ]
                        }
                    ]
                },
                "2": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "attack",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.15"
                                }
                            ]
                        }
                    ]
                },
                "3": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "attack",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.2"
                                }
                            ]
                        }
                    ]
                }
            }
        },
        "MAGE_WISDOM": {
            "name": "法師智慧",
            "description_template": "法師系卡牌的天賦，隨星級提升魔法攻擊力和MP",
            "effects_by_star_level": {
                "0": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "magic_attack",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.08"
                                },
                                {
                                    "stat_name": "max_mp",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.1"
                                }
                            ]
                        }
                    ]
                },
                "1": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "magic_attack",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.15"
                                },
                                {
                                    "stat_name": "max_mp",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.2"
                                }
                            ]
                        }
                    ]
                },
                "2": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "magic_attack",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.25"
                                },
                                {
                                    "stat_name": "max_mp",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.3"
                                }
                            ]
                        }
                    ]
                },
                "3": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "magic_attack",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.35"
                                },
                                {
                                    "stat_name": "max_mp",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.4"
                                }
                            ]
                        }
                    ]
                }
            }
        },
        "HEALER_BLESSING": {
            "name": "治療師祝福",
            "description_template": "治療師系卡牌的天賦，隨星級提升治療效果",
            "effects_by_star_level": {
                "0": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "heal_power",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.1"
                                }
                            ]
                        }
                    ]
                },
                "1": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "heal_power",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.2"
                                }
                            ]
                        }
                    ]
                },
                "2": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "heal_power",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.3"
                                }
                            ]
                        }
                    ]
                },
                "3": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "heal_power",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.4"
                                }
                            ]
                        }
                    ]
                }
            }
        }
    }
    
    file_path = os.path.join(output_dir, "innate_passive_skills.json")
    return save_config_to_json(innate_passive_skills, AllInnatePassiveSkillsConfig, file_path, validate=False)


def generate_cards_config(output_dir: str) -> bool:
    """生成卡牌配置"""
    print("🔧 生成卡牌配置...")
    
    cards = {
        "WARRIOR_ROOKIE": {
            "name": "新手戰士",
            "description": "一名剛開始冒險的戰士",
            "rarity": 1,
            "series": "BASIC",
            "base_stats": {
                "max_hp": 120,
                "max_mp": 30,
                "attack": 25,
                "defense": 20,
                "magic_attack": 10,
                "magic_defense": 15,
                "speed": 18,
                "crit_rate": 0.05,
                "crit_damage": 1.5,
                "accuracy": 0.95,
                "evasion": 0.05
            },
            "growth_per_rpg_level": {
                "max_hp": 8,
                "max_mp": 2,
                "attack": 2,
                "defense": 1.5,
                "magic_attack": 0.5,
                "magic_defense": 1,
                "speed": 1,
                "crit_rate": 0.001,
                "crit_damage": 0.01,
                "accuracy": 0.001,
                "evasion": 0.001
            },
            "innate_passive_skill_id": "WARRIOR_SPIRIT",
            "primary_attack_skill_id": "BASIC_ATTACK",
            "default_active_skill_slot_1_id": "BASIC_ATTACK",
            "default_active_skill_slot_2_id": "POWER_STRIKE",
            "passive_skill_slots": 2,
            "max_star_level": 3
        },
        "MAGE_APPRENTICE": {
            "name": "見習法師",
            "description": "正在學習魔法的年輕法師",
            "rarity": 2,
            "series": "BASIC",
            "base_stats": {
                "max_hp": 80,
                "max_mp": 60,
                "attack": 15,
                "defense": 12,
                "magic_attack": 30,
                "magic_defense": 25,
                "speed": 20,
                "crit_rate": 0.03,
                "crit_damage": 1.4,
                "accuracy": 0.92,
                "evasion": 0.08
            },
            "growth_per_rpg_level": {
                "max_hp": 5,
                "max_mp": 4,
                "attack": 1,
                "defense": 0.8,
                "magic_attack": 2.5,
                "magic_defense": 2,
                "speed": 1.2,
                "crit_rate": 0.001,
                "crit_damage": 0.01,
                "accuracy": 0.001,
                "evasion": 0.002
            },
            "innate_passive_skill_id": "MAGE_WISDOM",
            "primary_attack_skill_id": "BASIC_ATTACK",
            "default_active_skill_slot_1_id": "BASIC_ATTACK",
            "default_active_skill_slot_2_id": "FIREBALL",
            "default_active_skill_slot_3_id": "HEAL",
            "passive_skill_slots": 3,
            "max_star_level": 3
        }
    }
    
    file_path = os.path.join(output_dir, "cards.json")
    return save_config_to_json(cards, AllCardsConfig, file_path, validate=False)


def generate_monsters_config(output_dir: str) -> bool:
    """生成怪物配置"""
    print("🔧 生成怪物配置...")

    monsters = {
        "GOBLIN": {
            "name": "哥布林",
            "description": "弱小但狡猾的綠皮怪物",
            "monster_type": "NORMAL",
            "base_stats": {
                "max_hp": 80,
                "max_mp": 20,
                "attack": 18,
                "defense": 12,
                "magic_attack": 8,
                "magic_defense": 10,
                "speed": 22,
                "crit_rate": 0.08,
                "crit_damage": 1.3,
                "accuracy": 0.9,
                "evasion": 0.12
            },
            "available_skills": [
                {
                    "skill_id": "BASIC_ATTACK",
                    "skill_level": 1,
                    "ai_priority": 5
                },
                {
                    "skill_id": "POWER_STRIKE",
                    "skill_level": 1,
                    "ai_priority": 3
                }
            ],
            "ai_behavior": "AGGRESSIVE",
            "reward_xp": 25,
            "reward_gold": 15
        },
        "ORC_WARRIOR": {
            "name": "獸人戰士",
            "description": "強壯的獸人戰士，擅長近戰",
            "monster_type": "ELITE",
            "base_stats": {
                "max_hp": 150,
                "max_mp": 40,
                "attack": 35,
                "defense": 25,
                "magic_attack": 12,
                "magic_defense": 18,
                "speed": 16,
                "crit_rate": 0.12,
                "crit_damage": 1.6,
                "accuracy": 0.88,
                "evasion": 0.05
            },
            "available_skills": [
                {
                    "skill_id": "BASIC_ATTACK",
                    "skill_level": 2,
                    "ai_priority": 4
                },
                {
                    "skill_id": "POWER_STRIKE",
                    "skill_level": 2,
                    "ai_priority": 6
                }
            ],
            "ai_behavior": "BALANCED",
            "reward_xp": 60,
            "reward_gold": 40
        },
        "FIRE_ELEMENTAL": {
            "name": "火元素",
            "description": "由純粹火焰能量構成的元素生物",
            "monster_type": "MAGICAL",
            "base_stats": {
                "max_hp": 100,
                "max_mp": 80,
                "attack": 20,
                "defense": 15,
                "magic_attack": 45,
                "magic_defense": 35,
                "speed": 25,
                "crit_rate": 0.06,
                "crit_damage": 1.4,
                "accuracy": 0.92,
                "evasion": 0.15
            },
            "available_skills": [
                {
                    "skill_id": "BASIC_ATTACK",
                    "skill_level": 1,
                    "ai_priority": 2
                },
                {
                    "skill_id": "FIREBALL",
                    "skill_level": 2,
                    "ai_priority": 7
                }
            ],
            "ai_behavior": "CASTER",
            "reward_xp": 80,
            "reward_gold": 60,
            "resistances": {
                "FIRE": 0.5,
                "ICE": 2.0
            }
        },
        "DRAGON_WHELP": {
            "name": "幼龍",
            "description": "年幼的龍族，已經展現出強大的力量",
            "monster_type": "BOSS",
            "base_stats": {
                "max_hp": 300,
                "max_mp": 120,
                "attack": 50,
                "defense": 40,
                "magic_attack": 60,
                "magic_defense": 45,
                "speed": 20,
                "crit_rate": 0.15,
                "crit_damage": 2.0,
                "accuracy": 0.95,
                "evasion": 0.08
            },
            "available_skills": [
                {
                    "skill_id": "BASIC_ATTACK",
                    "skill_level": 3,
                    "ai_priority": 3
                },
                {
                    "skill_id": "POWER_STRIKE",
                    "skill_level": 3,
                    "ai_priority": 5
                },
                {
                    "skill_id": "FIREBALL",
                    "skill_level": 3,
                    "ai_priority": 6
                }
            ],
            "ai_behavior": "BOSS",
            "reward_xp": 200,
            "reward_gold": 150,
            "resistances": {
                "FIRE": 0.3,
                "PHYSICAL": 0.8
            }
        }
    }

    file_path = os.path.join(output_dir, "monsters.json")
    return save_config_to_json(monsters, AllMonstersConfig, file_path, validate=False)
