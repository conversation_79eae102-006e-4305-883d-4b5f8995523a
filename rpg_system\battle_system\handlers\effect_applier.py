"""
效果應用器服務
"""
from typing import List, Dict, Any, Optional, TYPE_CHECKING
import logging
import random

if TYPE_CHECKING:
    from rpg_system.battle_system.models.combatant import Combatant
    from rpg_system.battle_system.models.battle import Battle
    from rpg_system.battle_system.models.skill_instance import SkillInstance
    from rpg_system.battle_system.models.battle_log import BattleLogEntry
    from rpg_system.formula_engine.evaluator import FormulaEvaluator
    from rpg_system.config.loader import ConfigLoader
    from .target_selector import TargetSelector

logger = logging.getLogger(__name__)


class EffectApplierError(Exception):
    """效果應用器異常"""
    pass


class EffectApplier:
    """
    效果應用器服務
    
    負責應用技能效果和處理各種戰鬥效果
    """
    
    def __init__(
        self,
        formula_evaluator: 'FormulaEvaluator',
        target_selector: 'TargetSelector',
        config_loader: 'ConfigLoader'
    ):
        """
        初始化效果應用器
        
        Args:
            formula_evaluator: 公式求值器
            target_selector: 目標選擇器
            config_loader: 配置加載器
        """
        self.formula_evaluator = formula_evaluator
        self.target_selector = target_selector
        self.config_loader = config_loader
    
    def apply_skill_effects(
        self,
        caster: 'Combatant',
        targets: List['Combatant'],
        skill_instance: 'SkillInstance',
        battle_context: 'Battle'
    ) -> List['BattleLogEntry']:
        """
        應用技能效果
        
        Args:
            caster: 施法者
            targets: 目標列表
            skill_instance: 技能實例
            battle_context: 戰鬥上下文
            
        Returns:
            戰鬥日誌條目列表
            
        Raises:
            EffectApplierError: 當效果應用失敗時
        """
        try:
            logger.debug(
                "應用技能效果: 施法者=%s, 技能=%s, 目標數=%d",
                caster.combatant_id, skill_instance.skill_id, len(targets)
            )
            
            # 獲取技能定義
            skill_definition = skill_instance.get_definition(
                self.config_loader, caster.star_level
            )
            
            effect_definitions = skill_definition.get("effect_definitions", [])
            skill_tags = skill_definition.get("tags", [])
            
            # 應用效果定義
            log_entries = self.apply_effect_definitions(
                caster=caster,
                initial_targets=targets,
                effect_definitions=effect_definitions,
                battle_context=battle_context,
                source_skill_tags=skill_tags,
                source_skill_instance=skill_instance
            )
            
            logger.debug(
                "技能效果應用完成: 生成日誌條目=%d", len(log_entries)
            )
            
            return log_entries
            
        except Exception as e:
            logger.error(
                "技能效果應用失敗: 施法者=%s, 技能=%s, 錯誤=%s",
                caster.combatant_id, skill_instance.skill_id, str(e)
            )
            raise EffectApplierError(
                f"技能效果應用失敗: {str(e)}"
            ) from e
    
    def apply_effect_definitions(
        self,
        caster: 'Combatant',
        initial_targets: List['Combatant'],
        effect_definitions: List[Dict[str, Any]],
        battle_context: 'Battle',
        source_skill_tags: List[str],
        source_skill_instance: Optional['SkillInstance'] = None,
        custom_vars_from_source: Optional[Dict[str, Any]] = None
    ) -> List['BattleLogEntry']:
        """
        應用效果定義列表
        
        Args:
            caster: 施法者
            initial_targets: 初始目標列表
            effect_definitions: 效果定義列表
            battle_context: 戰鬥上下文
            source_skill_tags: 來源技能標籤
            source_skill_instance: 來源技能實例
            custom_vars_from_source: 自定義變量
            
        Returns:
            戰鬥日誌條目列表
        """
        from rpg_system.battle_system.models.battle_log import BattleLogEntry
        
        log_entries = []
        
        for effect_def in effect_definitions:
            try:
                # 確定最終目標
                actual_targets = self._determine_effect_targets(
                    caster, initial_targets, effect_def, battle_context
                )
                
                # 對每個目標應用效果
                for target in actual_targets:
                    effect_logs = self._apply_single_effect_to_target(
                        caster=caster,
                        target=target,
                        effect_def=effect_def,
                        battle_context=battle_context,
                        source_skill_tags=source_skill_tags,
                        source_skill_instance=source_skill_instance,
                        custom_vars_from_source=custom_vars_from_source
                    )
                    log_entries.extend(effect_logs)
                    
            except Exception as e:
                logger.error(
                    "效果定義應用失敗: 效果類型=%s, 錯誤=%s",
                    effect_def.get("type", "UNKNOWN"), str(e)
                )
                # 繼續處理其他效果，不中斷整個流程
                continue
        
        return log_entries
    
    def _determine_effect_targets(
        self,
        caster: 'Combatant',
        initial_targets: List['Combatant'],
        effect_def: Dict[str, Any],
        battle_context: 'Battle'
    ) -> List['Combatant']:
        """
        確定效果的最終目標
        
        Args:
            caster: 施法者
            initial_targets: 初始目標列表
            effect_def: 效果定義
            battle_context: 戰鬥上下文
            
        Returns:
            最終目標列表
        """
        target_override = effect_def.get("target_override")
        
        if target_override:
            # 效果有自己的目標邏輯
            return self.target_selector.select_targets(
                caster, target_override, battle_context,
                self.formula_evaluator, self.config_loader
            )
        else:
            # 使用初始目標
            return initial_targets
    
    def _apply_single_effect_to_target(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        effect_def: Dict[str, Any],
        battle_context: 'Battle',
        source_skill_tags: List[str],
        source_skill_instance: Optional['SkillInstance'] = None,
        custom_vars_from_source: Optional[Dict[str, Any]] = None
    ) -> List['BattleLogEntry']:
        """
        對單個目標應用單個效果
        
        Args:
            caster: 施法者
            target: 目標
            effect_def: 效果定義
            battle_context: 戰鬥上下文
            source_skill_tags: 來源技能標籤
            source_skill_instance: 來源技能實例
            custom_vars_from_source: 自定義變量
            
        Returns:
            戰鬥日誌條目列表
        """
        from rpg_system.battle_system.models.battle_log import BattleLogEntry
        
        log_entries = []
        
        # 準備效果公式上下文
        context_vars = self._prepare_effect_context(
            caster, target, battle_context, source_skill_instance, 
            custom_vars_from_source
        )
        
        # 檢查應用條件
        if not self._check_effect_conditions(effect_def, context_vars):
            logger.debug(
                "效果條件不滿足，跳過: 目標=%s, 效果類型=%s",
                target.combatant_id, effect_def.get("type", "UNKNOWN")
            )
            return log_entries
        
        # 根據效果類型處理
        effect_type = effect_def.get("type", "")
        
        if effect_type == "DAMAGE":
            log_entries.extend(self._apply_damage_effect(
                caster, target, effect_def, context_vars, 
                battle_context, source_skill_tags
            ))
        elif effect_type == "HEAL":
            log_entries.extend(self._apply_heal_effect(
                caster, target, effect_def, context_vars, battle_context
            ))
        elif effect_type == "GAIN_MP":
            log_entries.extend(self._apply_mp_gain_effect(
                caster, target, effect_def, context_vars, battle_context
            ))
        elif effect_type == "LOSE_MP":
            log_entries.extend(self._apply_mp_loss_effect(
                caster, target, effect_def, context_vars, battle_context
            ))
        else:
            logger.warning(
                "未實現的效果類型: %s", effect_type
            )
        
        return log_entries
    
    def _check_effect_conditions(
        self,
        effect_def: Dict[str, Any],
        context_vars: Dict[str, Any]
    ) -> bool:
        """
        檢查效果應用條件
        
        Args:
            effect_def: 效果定義
            context_vars: 上下文變量
            
        Returns:
            是否滿足條件
        """
        conditions = effect_def.get("conditions_to_apply", [])
        
        if not conditions:
            return True
        
        for condition in conditions:
            try:
                condition_formula = condition.get("formula", "")
                if condition_formula:
                    result = self.formula_evaluator.evaluate(
                        condition_formula, context_vars
                    )
                    if not result:
                        return False
            except Exception as e:
                logger.warning(
                    "條件檢查失敗: 公式=%s, 錯誤=%s",
                    condition.get("formula", ""), str(e)
                )
                return False
        
        return True
    
    def _apply_damage_effect(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        effect_def: Dict[str, Any],
        context_vars: Dict[str, Any],
        battle_context: 'Battle',
        source_skill_tags: List[str]
    ) -> List['BattleLogEntry']:
        """
        應用傷害效果
        
        Args:
            caster: 施法者
            target: 目標
            effect_def: 效果定義
            context_vars: 上下文變量
            battle_context: 戰鬥上下文
            source_skill_tags: 來源技能標籤
            
        Returns:
            戰鬥日誌條目列表
        """
        from rpg_system.battle_system.models.battle_log import BattleLogEntry
        
        log_entries = []
        
        try:
            # 計算基礎傷害
            value_formula = effect_def.get("value_formula", "0")
            base_damage = self.formula_evaluator.evaluate(value_formula, context_vars)
            
            # 暴擊判斷
            caster_stats = caster.get_current_stats()
            is_crit = random.random() < caster_stats.crit_rate
            
            # 計算最終傷害
            final_damage = self._calculate_final_damage(
                caster, target, effect_def, base_damage, is_crit, source_skill_tags
            )
            
            # 命中判斷
            was_miss = self._check_miss(caster, target)
            
            if not was_miss and final_damage > 0:
                # 應用傷害
                actual_damage = target.take_damage(int(final_damage))
                
                # 記錄日誌
                log_entries.append(BattleLogEntry.create_damage(
                    turn_number=battle_context.current_turn,
                    actor_id=caster.combatant_id,
                    target_id=target.combatant_id,
                    damage=actual_damage,
                    additional_data={
                        "was_crit": is_crit,
                        "damage_type": effect_def.get("damage_type", "PHYSICAL"),
                        "base_damage": base_damage,
                        "final_damage": final_damage
                    }
                ))
                
                logger.debug(
                    "傷害應用: %s -> %s, 傷害=%d, 暴擊=%s",
                    caster.combatant_id, target.combatant_id, actual_damage, is_crit
                )
            else:
                # 記錄未命中
                if was_miss:
                    log_entries.append(BattleLogEntry.create_damage(
                        turn_number=battle_context.current_turn,
                        actor_id=caster.combatant_id,
                        target_id=target.combatant_id,
                        damage=0,
                        additional_data={"was_miss": True}
                    ))
                    logger.debug(
                        "攻擊未命中: %s -> %s",
                        caster.combatant_id, target.combatant_id
                    )
            
        except Exception as e:
            logger.error(
                "傷害效果應用失敗: %s -> %s, 錯誤=%s",
                caster.combatant_id, target.combatant_id, str(e)
            )
        
        return log_entries
    
    def _apply_heal_effect(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        effect_def: Dict[str, Any],
        context_vars: Dict[str, Any],
        battle_context: 'Battle'
    ) -> List['BattleLogEntry']:
        """
        應用治療效果
        
        Args:
            caster: 施法者
            target: 目標
            effect_def: 效果定義
            context_vars: 上下文變量
            battle_context: 戰鬥上下文
            
        Returns:
            戰鬥日誌條目列表
        """
        from rpg_system.battle_system.models.battle_log import BattleLogEntry
        
        log_entries = []
        
        try:
            # 計算治療量
            value_formula = effect_def.get("value_formula", "0")
            heal_amount = self.formula_evaluator.evaluate(value_formula, context_vars)
            
            if heal_amount > 0:
                # 應用治療
                actual_heal = target.heal(int(heal_amount))
                
                # 記錄日誌
                log_entries.append(BattleLogEntry.create_healing(
                    turn_number=battle_context.current_turn,
                    actor_id=caster.combatant_id,
                    target_id=target.combatant_id,
                    healing=actual_heal,
                    additional_data={
                        "calculated_heal": heal_amount
                    }
                ))
                
                logger.debug(
                    "治療應用: %s -> %s, 治療量=%d",
                    caster.combatant_id, target.combatant_id, actual_heal
                )
            
        except Exception as e:
            logger.error(
                "治療效果應用失敗: %s -> %s, 錯誤=%s",
                caster.combatant_id, target.combatant_id, str(e)
            )
        
        return log_entries

    def _apply_mp_gain_effect(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        effect_def: Dict[str, Any],
        context_vars: Dict[str, Any],
        battle_context: 'Battle'
    ) -> List['BattleLogEntry']:
        """
        應用MP恢復效果

        Args:
            caster: 施法者
            target: 目標
            effect_def: 效果定義
            context_vars: 上下文變量
            battle_context: 戰鬥上下文

        Returns:
            戰鬥日誌條目列表
        """
        from rpg_system.battle_system.models.battle_log import BattleLogEntry

        log_entries = []

        try:
            # 計算MP恢復量
            value_formula = effect_def.get("value_formula", "0")
            mp_gain = self.formula_evaluator.evaluate(value_formula, context_vars)

            if mp_gain > 0:
                # 應用MP恢復
                actual_gain = target.restore_mp(int(mp_gain))

                # 記錄日誌
                log_entries.append(BattleLogEntry(
                    turn_number=battle_context.current_turn,
                    event_type="MP_GAIN",
                    actor_id=caster.combatant_id,
                    target_ids=[target.combatant_id],
                    skill_id=None,
                    damage_dealt=0,
                    healing_done=0,
                    mp_consumed=-actual_gain,  # 負值表示恢復
                    status_effects_applied=[],
                    status_effects_removed=[],
                    additional_data={
                        "calculated_gain": mp_gain,
                        "actual_gain": actual_gain
                    },
                    timestamp=None
                ))

                logger.debug(
                    "MP恢復應用: %s -> %s, MP恢復=%d",
                    caster.combatant_id, target.combatant_id, actual_gain
                )

        except Exception as e:
            logger.error(
                "MP恢復效果應用失敗: %s -> %s, 錯誤=%s",
                caster.combatant_id, target.combatant_id, str(e)
            )

        return log_entries

    def _apply_mp_loss_effect(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        effect_def: Dict[str, Any],
        context_vars: Dict[str, Any],
        battle_context: 'Battle'
    ) -> List['BattleLogEntry']:
        """
        應用MP消耗效果

        Args:
            caster: 施法者
            target: 目標
            effect_def: 效果定義
            context_vars: 上下文變量
            battle_context: 戰鬥上下文

        Returns:
            戰鬥日誌條目列表
        """
        from rpg_system.battle_system.models.battle_log import BattleLogEntry

        log_entries = []

        try:
            # 計算MP消耗量
            value_formula = effect_def.get("value_formula", "0")
            mp_loss = self.formula_evaluator.evaluate(value_formula, context_vars)

            if mp_loss > 0:
                # 計算實際消耗量
                actual_loss = min(int(mp_loss), target.current_mp)
                target.current_mp -= actual_loss

                # 記錄日誌
                log_entries.append(BattleLogEntry(
                    turn_number=battle_context.current_turn,
                    event_type="MP_LOSS",
                    actor_id=caster.combatant_id,
                    target_ids=[target.combatant_id],
                    skill_id=None,
                    damage_dealt=0,
                    healing_done=0,
                    mp_consumed=actual_loss,
                    status_effects_applied=[],
                    status_effects_removed=[],
                    additional_data={
                        "calculated_loss": mp_loss,
                        "actual_loss": actual_loss
                    },
                    timestamp=None
                ))

                logger.debug(
                    "MP消耗應用: %s -> %s, MP消耗=%d",
                    caster.combatant_id, target.combatant_id, actual_loss
                )

        except Exception as e:
            logger.error(
                "MP消耗效果應用失敗: %s -> %s, 錯誤=%s",
                caster.combatant_id, target.combatant_id, str(e)
            )

        return log_entries

    def _calculate_final_damage(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        effect_def: Dict[str, Any],
        base_damage: float,
        is_crit: bool,
        source_skill_tags: List[str]
    ) -> float:
        """
        計算最終傷害

        Args:
            caster: 施法者
            target: 目標
            effect_def: 效果定義
            base_damage: 基礎傷害
            is_crit: 是否暴擊
            source_skill_tags: 來源技能標籤

        Returns:
            最終傷害值
        """
        if base_damage <= 0:
            return 0

        caster_stats = caster.get_current_stats()
        target_stats = target.get_current_stats()

        # 暴擊倍率
        crit_multiplier = caster_stats.crit_dmg_multiplier if is_crit else 1.0

        # 防禦減免
        damage_type = effect_def.get("damage_type", "PHYSICAL")
        defense_reduction = self._calculate_defense_reduction(
            target_stats, damage_type
        )

        # 計算最終傷害
        final_damage = base_damage * crit_multiplier * (1 - defense_reduction)

        return max(0, final_damage)

    def _calculate_defense_reduction(
        self,
        target_stats,
        damage_type: str
    ) -> float:
        """
        計算防禦減免

        Args:
            target_stats: 目標屬性
            damage_type: 傷害類型

        Returns:
            防禦減免比例 (0-1)
        """
        if damage_type == "PHYSICAL":
            defense = target_stats.pdef
        elif damage_type == "MAGICAL":
            defense = target_stats.mdef
        else:
            defense = 0

        # 簡化的防禦公式: 防禦值 / (防禦值 + 100)
        defense_reduction = defense / (defense + 100)
        return min(0.9, defense_reduction)  # 最大90%減免

    def _check_miss(
        self,
        caster: 'Combatant',
        target: 'Combatant'
    ) -> bool:
        """
        檢查是否未命中

        Args:
            caster: 施法者
            target: 目標

        Returns:
            是否未命中
        """
        caster_stats = caster.get_current_stats()
        target_stats = target.get_current_stats()

        hit_chance = caster_stats.accuracy - target_stats.evasion
        hit_chance = max(0.05, min(0.95, hit_chance))  # 限制在5%-95%之間

        return random.random() > hit_chance

    def _prepare_effect_context(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        battle_context: 'Battle',
        source_skill_instance: Optional['SkillInstance'] = None,
        custom_vars_from_source: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        準備效果公式上下文

        Args:
            caster: 施法者
            target: 目標
            battle_context: 戰鬥上下文
            source_skill_instance: 來源技能實例
            custom_vars_from_source: 自定義變量

        Returns:
            上下文變量字典
        """
        context_vars = {
            # 施法者屬性
            "caster_current_hp": caster.current_hp,
            "caster_current_hp_percent": caster.get_hp_percent(),
            "caster_current_mp": caster.current_mp,
            "caster_current_mp_percent": caster.get_mp_percent(),
            "caster_level": caster.rpg_level,
            "caster_star_level": caster.star_level,

            # 目標屬性
            "target_current_hp": target.current_hp,
            "target_current_hp_percent": target.get_hp_percent(),
            "target_current_mp": target.current_mp,
            "target_current_mp_percent": target.get_mp_percent(),
            "target_level": target.rpg_level,
            "target_star_level": target.star_level,

            # 戰鬥上下文
            "current_turn": battle_context.current_turn,
            "alive_allies": len(battle_context.get_alive_combatants(
                player_side=caster.is_player
            )),
            "alive_enemies": len(battle_context.get_alive_combatants(
                player_side=not caster.is_player
            )),
        }

        # 添加施法者當前屬性
        caster_stats = caster.get_current_stats()
        for attr_name in ["patk", "pdef", "matk", "mdef", "spd",
                         "crit_rate", "crit_dmg_multiplier", "accuracy", "evasion"]:
            context_vars[f"caster_stat_{attr_name}"] = getattr(caster_stats, attr_name, 0)

        # 添加目標當前屬性
        target_stats = target.get_current_stats()
        for attr_name in ["patk", "pdef", "matk", "mdef", "spd",
                         "crit_rate", "crit_dmg_multiplier", "accuracy", "evasion"]:
            context_vars[f"target_stat_{attr_name}"] = getattr(target_stats, attr_name, 0)

        # 添加技能相關信息
        if source_skill_instance:
            context_vars.update({
                "skill_level": source_skill_instance.current_level,
                "skill_id": source_skill_instance.skill_id
            })

        # 添加自定義變量
        if custom_vars_from_source:
            context_vars.update(custom_vars_from_source)

        return context_vars
