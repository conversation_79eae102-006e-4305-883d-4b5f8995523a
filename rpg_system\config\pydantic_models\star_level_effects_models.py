"""
Star Level Effects Pydantic Models
Based on RPG_02_Configuration_Files.md
"""
from typing import List, Optional, Dict
from pydantic import BaseModel
from .active_skills_models import EffectDefinition

# -------------------- StarLevelEffectDetail Model --------------------
class StarLevelEffectDetail(BaseModel):
    """星級效果詳細信息"""
    additional_stats_flat: Optional[Dict[str, float]] = None
    additional_stats_percent: Optional[Dict[str, float]] = None
    unlock_passive_skill_slot: Optional[bool] = None
    apply_self_effect_on_battle_start: Optional[List[EffectDefinition]] = None

# -------------------- StarLevelEffectsConfig Model --------------------
StarLevelEffectsConfig = Dict[str, StarLevelEffectDetail]  # 鍵為培養星級

# -------------------- Main Model --------------------
AllStarLevelEffectsConfig = Dict[str, StarLevelEffectsConfig]  # 鍵為 star_level_effects_key
