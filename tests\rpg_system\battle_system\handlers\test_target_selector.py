"""
目標選擇器單元測試
"""
import unittest
from unittest.mock import Mock, MagicMock

from rpg_system.battle_system.handlers.target_selector import (
    TargetSelector, TargetSelectorError
)


class TestTargetSelector(unittest.TestCase):
    """TargetSelector 測試類"""
    
    def setUp(self):
        """測試前設置"""
        self.selector = TargetSelector()
        self.mock_formula_evaluator = Mock()
        self.mock_config_loader = <PERSON>ck()
        self.mock_battle_context = Mock()
        
        # 創建模擬戰鬥者
        self.caster = Mock()
        self.caster.combatant_id = "caster_001"
        self.caster.is_player = True
        self.caster.is_alive = True
        self.caster.current_hp = 100
        self.caster.get_hp_percent.return_value = 1.0
        self.caster.current_mp = 50
        self.caster.get_mp_percent.return_value = 0.5
        self.caster.rpg_level = 10
        self.caster.star_level = 3
        
        # 創建模擬目標
        self.target1 = Mock()
        self.target1.combatant_id = "target_001"
        self.target1.is_player = False
        self.target1.is_alive = True
        self.target1.current_hp = 80
        self.target1.get_hp_percent.return_value = 0.8
        self.target1.current_mp = 30
        self.target1.get_mp_percent.return_value = 0.6
        self.target1.rpg_level = 8
        self.target1.star_level = 2
        
        self.target2 = Mock()
        self.target2.combatant_id = "target_002"
        self.target2.is_player = False
        self.target2.is_alive = True
        self.target2.current_hp = 60
        self.target2.get_hp_percent.return_value = 0.6
        self.target2.current_mp = 20
        self.target2.get_mp_percent.return_value = 0.4
        self.target2.rpg_level = 6
        self.target2.star_level = 1
        
        # 設置模擬屬性
        mock_stats = Mock()
        mock_stats.patk = 100
        mock_stats.pdef = 50
        mock_stats.matk = 80
        mock_stats.mdef = 40
        mock_stats.spd = 70
        mock_stats.crit_rate = 0.1
        mock_stats.crit_dmg_multiplier = 1.5
        mock_stats.accuracy = 0.95
        mock_stats.evasion = 0.05
        
        self.caster.get_current_stats.return_value = mock_stats
        self.target1.get_current_stats.return_value = mock_stats
        self.target2.get_current_stats.return_value = mock_stats
    
    def test_base_pool_enemies(self):
        """測試敵方目標池"""
        target_logic = {"base_pool": "ENEMIES"}
        
        self.mock_battle_context.get_alive_combatants.return_value = [
            self.target1, self.target2
        ]
        
        result = self.selector.select_targets(
            self.caster, target_logic, self.mock_battle_context,
            self.mock_formula_evaluator, self.mock_config_loader
        )
        
        self.assertEqual(len(result), 2)
        self.assertIn(self.target1, result)
        self.assertIn(self.target2, result)
        
        # 驗證調用參數
        self.mock_battle_context.get_alive_combatants.assert_called_with(
            player_side=False
        )
    
    def test_base_pool_allies(self):
        """測試友方目標池"""
        target_logic = {"base_pool": "ALLIES"}
        
        ally = Mock()
        ally.combatant_id = "ally_001"
        ally.is_player = True
        
        self.mock_battle_context.get_alive_combatants.return_value = [
            self.caster, ally
        ]
        
        result = self.selector.select_targets(
            self.caster, target_logic, self.mock_battle_context,
            self.mock_formula_evaluator, self.mock_config_loader
        )
        
        self.assertEqual(len(result), 2)
        self.mock_battle_context.get_alive_combatants.assert_called_with(
            player_side=True
        )
    
    def test_base_pool_self(self):
        """測試自身目標池"""
        target_logic = {"base_pool": "SELF"}
        
        result = self.selector.select_targets(
            self.caster, target_logic, self.mock_battle_context,
            self.mock_formula_evaluator, self.mock_config_loader
        )
        
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0], self.caster)
    
    def test_base_pool_all_alive(self):
        """測試所有存活單位目標池"""
        target_logic = {"base_pool": "ALL_ALIVE"}
        
        all_combatants = [self.caster, self.target1, self.target2]
        self.mock_battle_context.get_alive_combatants.return_value = all_combatants
        
        result = self.selector.select_targets(
            self.caster, target_logic, self.mock_battle_context,
            self.mock_formula_evaluator, self.mock_config_loader
        )
        
        self.assertEqual(len(result), 3)
        self.mock_battle_context.get_alive_combatants.assert_called_with()
    
    def test_condition_filtering(self):
        """測試條件過濾"""
        target_logic = {
            "base_pool": "ENEMIES",
            "conditions": [
                {"formula": "target_current_hp_percent < 0.7"}
            ]
        }
        
        self.mock_battle_context.get_alive_combatants.return_value = [
            self.target1, self.target2
        ]
        
        # 設置公式評估結果
        def evaluate_side_effect(formula, context):
            if "target_current_hp_percent < 0.7" in formula:
                # target1: 0.8 < 0.7 = False
                # target2: 0.6 < 0.7 = True
                return context["target_current_hp_percent"] < 0.7
            return True
        
        self.mock_formula_evaluator.evaluate.side_effect = evaluate_side_effect
        
        result = self.selector.select_targets(
            self.caster, target_logic, self.mock_battle_context,
            self.mock_formula_evaluator, self.mock_config_loader
        )
        
        # 只有target2滿足條件
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0], self.target2)
    
    def test_sorting_by_hp(self):
        """測試按血量排序"""
        target_logic = {
            "base_pool": "ENEMIES",
            "sort_by": "current_hp",
            "sort_order": "ASC"
        }
        
        self.mock_battle_context.get_alive_combatants.return_value = [
            self.target1, self.target2
        ]
        
        result = self.selector.select_targets(
            self.caster, target_logic, self.mock_battle_context,
            self.mock_formula_evaluator, self.mock_config_loader
        )
        
        # 應該按血量升序排列：target2(60) < target1(80)
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0], self.target2)
        self.assertEqual(result[1], self.target1)
    
    def test_sorting_desc(self):
        """測試降序排序"""
        target_logic = {
            "base_pool": "ENEMIES",
            "sort_by": "current_hp",
            "sort_order": "DESC"
        }
        
        self.mock_battle_context.get_alive_combatants.return_value = [
            self.target1, self.target2
        ]
        
        result = self.selector.select_targets(
            self.caster, target_logic, self.mock_battle_context,
            self.mock_formula_evaluator, self.mock_config_loader
        )
        
        # 應該按血量降序排列：target1(80) > target2(60)
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0], self.target1)
        self.assertEqual(result[1], self.target2)
    
    def test_count_logic_all(self):
        """測試選擇所有目標"""
        target_logic = {
            "base_pool": "ENEMIES",
            "count_logic": "ALL"
        }
        
        self.mock_battle_context.get_alive_combatants.return_value = [
            self.target1, self.target2
        ]
        
        result = self.selector.select_targets(
            self.caster, target_logic, self.mock_battle_context,
            self.mock_formula_evaluator, self.mock_config_loader
        )
        
        self.assertEqual(len(result), 2)
    
    def test_count_logic_formula(self):
        """測試公式計算目標數量"""
        target_logic = {
            "base_pool": "ENEMIES",
            "count_logic": "FORMULA",
            "count_formula": "1"
        }
        
        self.mock_battle_context.get_alive_combatants.return_value = [
            self.target1, self.target2
        ]
        self.mock_formula_evaluator.evaluate.return_value = 1
        
        result = self.selector.select_targets(
            self.caster, target_logic, self.mock_battle_context,
            self.mock_formula_evaluator, self.mock_config_loader
        )
        
        self.assertEqual(len(result), 1)
    
    def test_selection_strategy_first_n(self):
        """測試選擇前N個目標"""
        target_logic = {
            "base_pool": "ENEMIES",
            "count": 1,
            "selection_strategy": "FIRST_N"
        }
        
        self.mock_battle_context.get_alive_combatants.return_value = [
            self.target1, self.target2
        ]
        
        result = self.selector.select_targets(
            self.caster, target_logic, self.mock_battle_context,
            self.mock_formula_evaluator, self.mock_config_loader
        )
        
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0], self.target1)
    
    def test_selection_strategy_last_n(self):
        """測試選擇後N個目標"""
        target_logic = {
            "base_pool": "ENEMIES",
            "count": 1,
            "selection_strategy": "LAST_N"
        }
        
        self.mock_battle_context.get_alive_combatants.return_value = [
            self.target1, self.target2
        ]
        
        result = self.selector.select_targets(
            self.caster, target_logic, self.mock_battle_context,
            self.mock_formula_evaluator, self.mock_config_loader
        )
        
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0], self.target2)
    
    def test_empty_target_pool(self):
        """測試空目標池"""
        target_logic = {"base_pool": "ENEMIES"}
        
        self.mock_battle_context.get_alive_combatants.return_value = []
        
        result = self.selector.select_targets(
            self.caster, target_logic, self.mock_battle_context,
            self.mock_formula_evaluator, self.mock_config_loader
        )
        
        self.assertEqual(len(result), 0)
    
    def test_formula_evaluation_error(self):
        """測試公式評估錯誤"""
        target_logic = {
            "base_pool": "ENEMIES",
            "conditions": [
                {"formula": "invalid_formula"}
            ]
        }
        
        self.mock_battle_context.get_alive_combatants.return_value = [self.target1]
        self.mock_formula_evaluator.evaluate.side_effect = Exception("公式錯誤")
        
        result = self.selector.select_targets(
            self.caster, target_logic, self.mock_battle_context,
            self.mock_formula_evaluator, self.mock_config_loader
        )
        
        # 公式錯誤時應該過濾掉該目標
        self.assertEqual(len(result), 0)


if __name__ == '__main__':
    unittest.main()
