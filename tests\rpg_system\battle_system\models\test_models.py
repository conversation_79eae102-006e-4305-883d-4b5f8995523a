"""
核心領域模型單元測試
"""
import unittest
from unittest.mock import Mock, MagicMock
from datetime import datetime

from rpg_system.battle_system.models import (
    SkillType, BattleStatus, SkillInstance, Combatant, 
    CombatantStats, BattleLogEntry, Battle
)


class TestSkillInstance(unittest.TestCase):
    """SkillInstance 測試類"""
    
    def test_skill_instance_creation(self):
        """測試技能實例創建"""
        skill = SkillInstance("fireball", SkillType.ACTIVE, 5, 2)
        
        self.assertEqual(skill.skill_id, "fireball")
        self.assertEqual(skill.skill_type, SkillType.ACTIVE)
        self.assertEqual(skill.current_level, 5)
        self.assertEqual(skill.current_cooldown, 2)
    
    def test_tick_cooldown(self):
        """測試冷卻減少"""
        skill = SkillInstance("fireball", SkillType.ACTIVE, 5, 3)
        
        skill.tick_cooldown()
        self.assertEqual(skill.current_cooldown, 2)
        
        skill.tick_cooldown()
        self.assertEqual(skill.current_cooldown, 1)
        
        skill.tick_cooldown()
        self.assertEqual(skill.current_cooldown, 0)
        
        # 冷卻不會變成負數
        skill.tick_cooldown()
        self.assertEqual(skill.current_cooldown, 0)
    
    def test_skill_instance_repr(self):
        """測試字符串表示"""
        skill = SkillInstance("heal", SkillType.PASSIVE, 3, 0)
        repr_str = repr(skill)
        
        self.assertIn("heal", repr_str)
        self.assertIn("PASSIVE", repr_str)
        self.assertIn("level=3", repr_str)
        self.assertIn("cooldown=0", repr_str)


class TestCombatant(unittest.TestCase):
    """Combatant 測試類"""
    
    def setUp(self):
        """測試前設置"""
        self.stats = CombatantStats(
            hp=100, max_mp=50, mp_regen_per_turn=5,
            patk=80, pdef=40, matk=60, mdef=30,
            spd=70, crit_rate=0.1, crit_dmg_multiplier=1.5,
            accuracy=0.95, evasion=0.05
        )
        
        self.combatant = Combatant(
            combatant_id="player_1",
            name="Test Hero",
            stats=self.stats,
            is_player=True,
            card_id="card_001",
            star_level=3,
            rpg_level=10
        )
    
    def test_combatant_creation(self):
        """測試戰鬥者創建"""
        self.assertEqual(self.combatant.combatant_id, "player_1")
        self.assertEqual(self.combatant.name, "Test Hero")
        self.assertTrue(self.combatant.is_player)
        self.assertEqual(self.combatant.card_id, "card_001")
        self.assertEqual(self.combatant.star_level, 3)
        self.assertEqual(self.combatant.rpg_level, 10)
        self.assertEqual(self.combatant.current_hp, 100)
        self.assertEqual(self.combatant.current_mp, 0)
        self.assertTrue(self.combatant.is_alive)
    
    def test_hp_percent(self):
        """測試血量百分比計算"""
        self.assertEqual(self.combatant.get_hp_percent(), 1.0)
        
        self.combatant.current_hp = 50
        self.assertEqual(self.combatant.get_hp_percent(), 0.5)
        
        self.combatant.current_hp = 0
        self.assertEqual(self.combatant.get_hp_percent(), 0.0)
    
    def test_take_damage(self):
        """測試受到傷害"""
        # 正常傷害
        actual_damage = self.combatant.take_damage(30)
        self.assertEqual(actual_damage, 30)
        self.assertEqual(self.combatant.current_hp, 70)
        self.assertTrue(self.combatant.is_alive)
        
        # 致命傷害
        actual_damage = self.combatant.take_damage(80)
        self.assertEqual(actual_damage, 70)  # 只能扣除剩餘血量
        self.assertEqual(self.combatant.current_hp, 0)
        self.assertFalse(self.combatant.is_alive)
        
        # 對死亡單位造成傷害
        actual_damage = self.combatant.take_damage(10)
        self.assertEqual(actual_damage, 0)
    
    def test_heal(self):
        """測試治療"""
        # 先受傷
        self.combatant.take_damage(40)
        self.assertEqual(self.combatant.current_hp, 60)
        
        # 正常治療
        actual_heal = self.combatant.heal(20)
        self.assertEqual(actual_heal, 20)
        self.assertEqual(self.combatant.current_hp, 80)
        
        # 過量治療
        actual_heal = self.combatant.heal(30)
        self.assertEqual(actual_heal, 20)  # 只能恢復到滿血
        self.assertEqual(self.combatant.current_hp, 100)
        
        # 對滿血單位治療
        actual_heal = self.combatant.heal(10)
        self.assertEqual(actual_heal, 0)
    
    def test_mp_management(self):
        """測試MP管理"""
        # 恢復MP
        actual_restore = self.combatant.restore_mp(30)
        self.assertEqual(actual_restore, 30)
        self.assertEqual(self.combatant.current_mp, 30)
        
        # 消耗MP
        success = self.combatant.consume_mp(20)
        self.assertTrue(success)
        self.assertEqual(self.combatant.current_mp, 10)
        
        # MP不足
        success = self.combatant.consume_mp(20)
        self.assertFalse(success)
        self.assertEqual(self.combatant.current_mp, 10)
        
        # MP自然恢復
        self.combatant.regenerate_mp()
        self.assertEqual(self.combatant.current_mp, 15)  # 10 + 5
    
    def test_add_skills(self):
        """測試添加技能"""
        # 添加普攻
        primary_attack = SkillInstance("basic_attack", SkillType.PRIMARY_ATTACK, 1)
        self.combatant.add_skill(primary_attack)
        self.assertEqual(self.combatant.primary_attack, primary_attack)
        
        # 添加主動技能
        active_skill = SkillInstance("fireball", SkillType.ACTIVE, 3)
        self.combatant.add_skill(active_skill)
        self.assertIn(active_skill, self.combatant.active_skills)
        
        # 添加被動技能
        passive_skill = SkillInstance("strength", SkillType.PASSIVE, 2)
        self.combatant.add_skill(passive_skill)
        self.assertIn(passive_skill, self.combatant.passive_skills)
        
        # 添加天賦被動
        innate_passive = SkillInstance("fire_mastery", SkillType.INNATE_PASSIVE, 1)
        self.combatant.add_skill(innate_passive)
        self.assertEqual(self.combatant.innate_passive, innate_passive)


class TestBattleLogEntry(unittest.TestCase):
    """BattleLogEntry 測試類"""
    
    def test_create_skill_use(self):
        """測試創建技能使用日誌"""
        entry = BattleLogEntry.create_skill_use(
            turn_number=1,
            actor_id="player_1",
            skill_id="fireball",
            target_ids=["monster_1", "monster_2"],
            mp_consumed=20
        )
        
        self.assertEqual(entry.turn_number, 1)
        self.assertEqual(entry.event_type, "SKILL_USE")
        self.assertEqual(entry.actor_id, "player_1")
        self.assertEqual(entry.skill_id, "fireball")
        self.assertEqual(entry.target_ids, ["monster_1", "monster_2"])
        self.assertEqual(entry.mp_consumed, 20)
        self.assertEqual(entry.damage_dealt, 0)
        self.assertEqual(entry.healing_done, 0)
    
    def test_create_damage(self):
        """測試創建傷害日誌"""
        entry = BattleLogEntry.create_damage(
            turn_number=2,
            actor_id="player_1",
            target_id="monster_1",
            damage=50,
            skill_id="fireball"
        )
        
        self.assertEqual(entry.turn_number, 2)
        self.assertEqual(entry.event_type, "DAMAGE")
        self.assertEqual(entry.actor_id, "player_1")
        self.assertEqual(entry.target_ids, ["monster_1"])
        self.assertEqual(entry.damage_dealt, 50)
        self.assertEqual(entry.skill_id, "fireball")
    
    def test_to_dict(self):
        """測試轉換為字典"""
        entry = BattleLogEntry.create_healing(
            turn_number=3,
            actor_id="player_2",
            target_id="player_1",
            healing=30
        )
        
        entry_dict = entry.to_dict()
        
        self.assertEqual(entry_dict["turn_number"], 3)
        self.assertEqual(entry_dict["event_type"], "HEALING")
        self.assertEqual(entry_dict["actor_id"], "player_2")
        self.assertEqual(entry_dict["target_ids"], ["player_1"])
        self.assertEqual(entry_dict["healing_done"], 30)
        self.assertIn("timestamp", entry_dict)


class TestBattle(unittest.TestCase):
    """Battle 測試類"""
    
    def setUp(self):
        """測試前設置"""
        self.battle = Battle(
            battle_id="test_battle_001",
            user_id=12345,
            floor_number=1,
            monster_group_id="group_001"
        )
        
        # 創建測試戰鬥者
        player_stats = CombatantStats(
            hp=100, max_mp=50, mp_regen_per_turn=5,
            patk=80, pdef=40, matk=60, mdef=30,
            spd=70, crit_rate=0.1, crit_dmg_multiplier=1.5,
            accuracy=0.95, evasion=0.05
        )
        
        monster_stats = CombatantStats(
            hp=80, max_mp=30, mp_regen_per_turn=3,
            patk=60, pdef=30, matk=40, mdef=20,
            spd=50, crit_rate=0.05, crit_dmg_multiplier=1.3,
            accuracy=0.9, evasion=0.1
        )
        
        self.player = Combatant("player_1", "Hero", player_stats, True)
        self.monster = Combatant("monster_1", "Goblin", monster_stats, False)
    
    def test_battle_creation(self):
        """測試戰鬥創建"""
        self.assertEqual(self.battle.battle_id, "test_battle_001")
        self.assertEqual(self.battle.user_id, 12345)
        self.assertEqual(self.battle.floor_number, 1)
        self.assertEqual(self.battle.monster_group_id, "group_001")
        self.assertEqual(self.battle.status, BattleStatus.PENDING)
        self.assertEqual(self.battle.current_turn, 0)
    
    def test_add_combatants(self):
        """測試添加戰鬥者"""
        self.battle.add_player_combatant(self.player)
        self.battle.add_monster_combatant(self.monster)
        
        self.assertEqual(len(self.battle.player_combatants), 1)
        self.assertEqual(len(self.battle.monster_combatants), 1)
        self.assertEqual(self.battle.player_combatants[0], self.player)
        self.assertEqual(self.battle.monster_combatants[0], self.monster)
        self.assertTrue(self.player.is_player)
        self.assertFalse(self.monster.is_player)
    
    def test_get_combatant_by_id(self):
        """測試根據ID獲取戰鬥者"""
        self.battle.add_player_combatant(self.player)
        self.battle.add_monster_combatant(self.monster)
        
        found_player = self.battle.get_combatant_by_id("player_1")
        found_monster = self.battle.get_combatant_by_id("monster_1")
        not_found = self.battle.get_combatant_by_id("nonexistent")
        
        self.assertEqual(found_player, self.player)
        self.assertEqual(found_monster, self.monster)
        self.assertIsNone(not_found)
    
    def test_turn_order_calculation(self):
        """測試行動順序計算"""
        # 添加多個戰鬥者，速度不同
        fast_player = Combatant("fast", "Fast Hero", 
                               CombatantStats(100, 50, 5, 80, 40, 60, 30, 
                                            90, 0.1, 1.5, 0.95, 0.05), True)
        slow_monster = Combatant("slow", "Slow Goblin",
                               CombatantStats(80, 30, 3, 60, 30, 40, 20,
                                            30, 0.05, 1.3, 0.9, 0.1), False)
        
        self.battle.add_player_combatant(fast_player)
        self.battle.add_monster_combatant(slow_monster)
        
        self.battle.calculate_turn_order()
        
        # 速度快的應該先行動
        self.assertEqual(len(self.battle.turn_order), 2)
        self.assertEqual(self.battle.turn_order[0], fast_player)  # 速度90
        self.assertEqual(self.battle.turn_order[1], slow_monster)  # 速度30
    
    def test_battle_start(self):
        """測試戰鬥開始"""
        self.battle.add_player_combatant(self.player)
        self.battle.add_monster_combatant(self.monster)
        
        self.battle.start_battle()
        
        self.assertEqual(self.battle.status, BattleStatus.IN_PROGRESS)
        self.assertIsNotNone(self.battle.started_at)
        self.assertEqual(self.battle.current_turn, 1)
        self.assertTrue(len(self.battle.turn_order) > 0)
        self.assertTrue(len(self.battle.battle_log) > 0)
    
    def test_battle_end_conditions(self):
        """測試戰鬥結束條件"""
        self.battle.add_player_combatant(self.player)
        self.battle.add_monster_combatant(self.monster)
        self.battle.start_battle()
        
        # 玩家全滅
        self.player.take_damage(1000)
        self.assertTrue(self.battle.check_battle_end())
        self.assertEqual(self.battle.status, BattleStatus.MONSTER_WIN)
        self.assertEqual(self.battle.winner_side, "monster")
        
        # 重置戰鬥
        self.battle.status = BattleStatus.IN_PROGRESS
        self.player.current_hp = 100
        self.player.is_alive = True
        
        # 怪物全滅
        self.monster.take_damage(1000)
        self.assertTrue(self.battle.check_battle_end())
        self.assertEqual(self.battle.status, BattleStatus.PLAYER_WIN)
        self.assertEqual(self.battle.winner_side, "player")
    
    def test_battle_summary(self):
        """測試戰鬥摘要"""
        self.battle.add_player_combatant(self.player)
        self.battle.add_monster_combatant(self.monster)
        
        summary = self.battle.get_battle_summary()
        
        self.assertEqual(summary["battle_id"], "test_battle_001")
        self.assertEqual(summary["user_id"], 12345)
        self.assertEqual(summary["floor_number"], 1)
        self.assertEqual(summary["status"], "PENDING")
        self.assertEqual(len(summary["player_combatants"]), 1)
        self.assertEqual(len(summary["monster_combatants"]), 1)
        self.assertIn("created_at", summary)


if __name__ == '__main__':
    unittest.main()
