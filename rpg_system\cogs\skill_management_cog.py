"""
技能管理 Cog

處理卡牌技能裝備、全局技能學習/升級等命令
"""
from typing import Dict, Any, Optional, Literal, TYPE_CHECKING
import discord
from discord.ext import commands
from discord import app_commands
import logging

if TYPE_CHECKING:
    from rpg_system.services.player_card_management_service import PlayerCardManagementService
    from rpg_system.services.player_skill_management_service import PlayerSkillManagementService
    from rpg_system.config.loader import ConfigLoader

logger = logging.getLogger(__name__)


class SkillManagementCog(commands.Cog):
    """
    技能管理 Cog

    處理卡牌技能裝備、全局技能學習/升級等命令
    """

    def __init__(
        self,
        bot: commands.Bot,
        player_card_management_service: 'PlayerCardManagementService',
        player_skill_management_service: 'PlayerSkillManagementService',
        config_loader: 'ConfigLoader'
    ):
        """
        初始化技能管理 Cog

        Args:
            bot: Discord Bot 實例
            player_card_management_service: 玩家卡牌管理服務
            player_skill_management_service: 玩家技能管理服務
            config_loader: 配置加載器
        """
        self.bot = bot
        self.player_card_management_service = player_card_management_service
        self.player_skill_management_service = player_skill_management_service
        self.config_loader = config_loader

    @app_commands.command(name="card_equip_active", description="為卡牌裝備主動技能")
    @app_commands.describe(
        collection_id="卡牌收藏ID",
        skill_id="技能ID",
        slot="技能槽位（0-2）"
    )
    async def card_equip_active(
        self,
        interaction: discord.Interaction,
        collection_id: int,
        skill_id: str,
        slot: int
    ):
        """為卡牌裝備主動技能"""
        try:
            user_id = interaction.user.id

            # 延遲響應
            await interaction.response.defer()

            # 驗證槽位範圍
            if slot < 0 or slot > 2:
                await interaction.followup.send("槽位必須在 0-2 之間", ephemeral=True)
                return

            # 裝備技能
            success, message = await self.player_card_management_service.equip_active_skill(
                user_id, collection_id, skill_id, slot
            )

            if success:
                embed = discord.Embed(
                    title="✅ 技能裝備成功",
                    description=message,
                    color=discord.Color.green()
                )
            else:
                embed = discord.Embed(
                    title="❌ 技能裝備失敗",
                    description=message,
                    color=discord.Color.red()
                )

            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"裝備主動技能失敗: {e}")
            await interaction.followup.send(f"裝備主動技能失敗: {str(e)}", ephemeral=True)

    @app_commands.command(name="card_unequip_active", description="卸下卡牌主動技能")
    @app_commands.describe(
        collection_id="卡牌收藏ID",
        slot="技能槽位（0-2）"
    )
    async def card_unequip_active(
        self,
        interaction: discord.Interaction,
        collection_id: int,
        slot: int
    ):
        """卸下卡牌主動技能"""
        try:
            user_id = interaction.user.id

            # 延遲響應
            await interaction.response.defer()

            # 驗證槽位範圍
            if slot < 0 or slot > 2:
                await interaction.followup.send("槽位必須在 0-2 之間", ephemeral=True)
                return

            # 卸下技能
            success, message = await self.player_card_management_service.unequip_active_skill(
                user_id, collection_id, slot
            )

            if success:
                embed = discord.Embed(
                    title="✅ 技能卸下成功",
                    description=message,
                    color=discord.Color.green()
                )
            else:
                embed = discord.Embed(
                    title="❌ 技能卸下失敗",
                    description=message,
                    color=discord.Color.red()
                )

            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"卸下主動技能失敗: {e}")
            await interaction.followup.send(f"卸下主動技能失敗: {str(e)}", ephemeral=True)

    @app_commands.command(name="card_equip_passive", description="為卡牌裝備被動技能")
    @app_commands.describe(
        collection_id="卡牌收藏ID",
        skill_id="技能ID",
        skill_level="技能等級",
        slot_key="槽位鍵（如 slot_0, slot_1）"
    )
    async def card_equip_passive(
        self,
        interaction: discord.Interaction,
        collection_id: int,
        skill_id: str,
        skill_level: int,
        slot_key: str
    ):
        """為卡牌裝備被動技能"""
        try:
            user_id = interaction.user.id

            # 延遲響應
            await interaction.response.defer()

            # 裝備被動技能
            success, message = await self.player_card_management_service.equip_passive_skill(
                user_id, collection_id, skill_id, skill_level, slot_key
            )

            if success:
                embed = discord.Embed(
                    title="✅ 被動技能裝備成功",
                    description=message,
                    color=discord.Color.green()
                )
            else:
                embed = discord.Embed(
                    title="❌ 被動技能裝備失敗",
                    description=message,
                    color=discord.Color.red()
                )

            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"裝備被動技能失敗: {e}")
            await interaction.followup.send(f"裝備被動技能失敗: {str(e)}", ephemeral=True)

    @app_commands.command(name="card_unequip_passive", description="卸下卡牌被動技能")
    @app_commands.describe(
        collection_id="卡牌收藏ID",
        slot_key="槽位鍵（如 slot_0, slot_1）"
    )
    async def card_unequip_passive(
        self,
        interaction: discord.Interaction,
        collection_id: int,
        slot_key: str
    ):
        """卸下卡牌被動技能"""
        try:
            user_id = interaction.user.id

            # 延遲響應
            await interaction.response.defer()

            # 卸下被動技能
            success, message = await self.player_card_management_service.unequip_passive_skill(
                user_id, collection_id, slot_key
            )

            if success:
                embed = discord.Embed(
                    title="✅ 被動技能卸下成功",
                    description=message,
                    color=discord.Color.green()
                )
            else:
                embed = discord.Embed(
                    title="❌ 被動技能卸下失敗",
                    description=message,
                    color=discord.Color.red()
                )

            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"卸下被動技能失敗: {e}")
            await interaction.followup.send(f"卸下被動技能失敗: {str(e)}", ephemeral=True)

    @app_commands.command(name="card_skills_show", description="顯示卡牌技能配置")
    @app_commands.describe(collection_id="卡牌收藏ID")
    async def card_skills_show(self, interaction: discord.Interaction, collection_id: int):
        """顯示卡牌技能配置"""
        try:
            user_id = interaction.user.id

            # 延遲響應
            await interaction.response.defer()

            # 獲取卡牌詳細信息
            card_details = await self.player_card_management_service.get_card_details_for_display(
                user_id, collection_id
            )

            if not card_details:
                await interaction.followup.send("找不到指定的卡牌或無權限查看", ephemeral=True)
                return

            # 創建詳細信息 Embed
            embed = discord.Embed(
                title=f"🃏 {card_details['card_name']} 技能配置",
                color=discord.Color.blue()
            )

            # 基本信息
            embed.add_field(
                name="📊 基本信息",
                value=f"等級: {card_details['rpg_level']}\n"
                      f"經驗: {card_details['rpg_xp']}\n"
                      f"星級: {card_details['star_level']}",
                inline=True
            )

            # 主動技能
            active_skills = card_details['equipped_active_skills']
            active_skill_text = ""
            for i, skill_id in enumerate(active_skills):
                if skill_id:
                    active_skill_text += f"槽位 {i}: {skill_id}\n"
                else:
                    active_skill_text += f"槽位 {i}: 空\n"

            embed.add_field(
                name="⚔️ 主動技能",
                value=active_skill_text if active_skill_text else "無裝備技能",
                inline=True
            )

            # 被動技能
            passive_skills = card_details['equipped_passive_skills']
            passive_skill_text = ""
            for slot_key, skill_data in passive_skills.items():
                if skill_data:
                    skill_id = skill_data.get('skill_id', 'Unknown')
                    level = skill_data.get('level', 1)
                    passive_skill_text += f"{slot_key}: {skill_id} (Lv.{level})\n"
                else:
                    passive_skill_text += f"{slot_key}: 空\n"

            embed.add_field(
                name="🛡️ 被動技能",
                value=passive_skill_text if passive_skill_text else "無裝備技能",
                inline=False
            )

            # 天賦技能
            if card_details.get('innate_passive_skill_id'):
                embed.add_field(
                    name="✨ 天賦技能",
                    value=card_details['innate_passive_skill_id'],
                    inline=True
                )

            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"顯示卡牌技能失敗: {e}")
            await interaction.followup.send(f"顯示卡牌技能失敗: {str(e)}", ephemeral=True)

    @app_commands.command(name="gskill_learn", description="學習全局技能")
    @app_commands.describe(
        skill_id="技能ID",
        skill_type="技能類型"
    )
    @app_commands.choices(skill_type=[
        app_commands.Choice(name="主動技能", value="ACTIVE"),
        app_commands.Choice(name="被動技能", value="PASSIVE")
    ])
    async def gskill_learn(
        self,
        interaction: discord.Interaction,
        skill_id: str,
        skill_type: Literal["ACTIVE", "PASSIVE"]
    ):
        """學習全局技能"""
        try:
            user_id = interaction.user.id

            # 延遲響應
            await interaction.response.defer()

            # 學習技能
            success, message = await self.player_skill_management_service.learn_global_skill(
                user_id, skill_id, skill_type
            )

            if success:
                embed = discord.Embed(
                    title="✅ 技能學習成功",
                    description=message,
                    color=discord.Color.green()
                )
            else:
                embed = discord.Embed(
                    title="❌ 技能學習失敗",
                    description=message,
                    color=discord.Color.red()
                )

            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"學習全局技能失敗: {e}")
            await interaction.followup.send(f"學習全局技能失敗: {str(e)}", ephemeral=True)

    @app_commands.command(name="gskill_upgrade", description="升級全局技能")
    @app_commands.describe(
        skill_id="技能ID",
        skill_type="技能類型"
    )
    @app_commands.choices(skill_type=[
        app_commands.Choice(name="主動技能", value="ACTIVE"),
        app_commands.Choice(name="被動技能", value="PASSIVE")
    ])
    async def gskill_upgrade(
        self,
        interaction: discord.Interaction,
        skill_id: str,
        skill_type: Literal["ACTIVE", "PASSIVE"]
    ):
        """升級全局技能"""
        try:
            user_id = interaction.user.id

            # 延遲響應
            await interaction.response.defer()

            # 升級技能
            success, message = await self.player_skill_management_service.upgrade_global_skill(
                user_id, skill_id, skill_type
            )

            if success:
                embed = discord.Embed(
                    title="✅ 技能升級成功",
                    description=message,
                    color=discord.Color.green()
                )
            else:
                embed = discord.Embed(
                    title="❌ 技能升級失敗",
                    description=message,
                    color=discord.Color.red()
                )

            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"升級全局技能失敗: {e}")
            await interaction.followup.send(f"升級全局技能失敗: {str(e)}", ephemeral=True)

    @app_commands.command(name="gskill_list", description="查看已學習的全局技能")
    @app_commands.describe(skill_type="技能類型過濾（可選）")
    @app_commands.choices(skill_type=[
        app_commands.Choice(name="主動技能", value="ACTIVE"),
        app_commands.Choice(name="被動技能", value="PASSIVE"),
        app_commands.Choice(name="全部", value="ALL")
    ])
    async def gskill_list(
        self,
        interaction: discord.Interaction,
        skill_type: Optional[str] = None
    ):
        """查看已學習的全局技能"""
        try:
            user_id = interaction.user.id

            # 延遲響應
            await interaction.response.defer()

            # 處理技能類型過濾
            filter_type = None
            if skill_type and skill_type != "ALL":
                filter_type = skill_type

            # 獲取技能列表
            learned_skills = await self.player_skill_management_service.get_player_learned_skills(
                user_id, filter_type
            )

            if not learned_skills:
                await interaction.followup.send("你還沒有學習任何全局技能", ephemeral=True)
                return

            # 創建技能列表 Embed
            embed = discord.Embed(
                title="📚 已學習的全局技能",
                color=discord.Color.blue()
            )

            # 分類顯示技能
            active_skills = [s for s in learned_skills if s['skill_type'] == 'ACTIVE']
            passive_skills = [s for s in learned_skills if s['skill_type'] == 'PASSIVE']

            if active_skills:
                active_text = ""
                for skill in active_skills:
                    skill_name = skill['skill_name']
                    level = skill['current_level']
                    max_level = skill['max_level']
                    current_xp = skill['current_xp']
                    xp_to_next = skill.get('xp_to_next_level', 'MAX')

                    active_text += f"**{skill_name}** (Lv.{level}/{max_level})\n"
                    if xp_to_next != 'MAX' and xp_to_next is not None:
                        active_text += f"  XP: {current_xp}/{xp_to_next}\n"
                    else:
                        active_text += f"  XP: {current_xp} (已滿級)\n"

                embed.add_field(
                    name="⚔️ 主動技能",
                    value=active_text,
                    inline=False
                )

            if passive_skills:
                passive_text = ""
                for skill in passive_skills:
                    skill_name = skill['skill_name']
                    level = skill['current_level']
                    max_level = skill['max_level']
                    current_xp = skill['current_xp']
                    xp_to_next = skill.get('xp_to_next_level', 'MAX')

                    passive_text += f"**{skill_name}** (Lv.{level}/{max_level})\n"
                    if xp_to_next != 'MAX' and xp_to_next is not None:
                        passive_text += f"  XP: {current_xp}/{xp_to_next}\n"
                    else:
                        passive_text += f"  XP: {current_xp} (已滿級)\n"

                embed.add_field(
                    name="🛡️ 被動技能",
                    value=passive_text,
                    inline=False
                )

            embed.set_footer(text=f"總共學習了 {len(learned_skills)} 個技能")

            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"查看全局技能列表失敗: {e}")
            await interaction.followup.send(f"查看全局技能列表失敗: {str(e)}", ephemeral=True)


async def setup(bot: commands.Bot):
    """設置 Cog"""
    # 注意：這裡需要從主應用程序傳入依賴
    # 實際使用時需要根據你的依賴注入方式調整
    pass
