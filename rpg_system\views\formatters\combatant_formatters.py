"""
戰鬥單位格式化工具

提供戰鬥單位相關的文本格式化功能
"""
from typing import Dict, Any, Optional, TYPE_CHECKING
import logging

if TYPE_CHECKING:
    from rpg_system.core.combatant import Combatant
    from rpg_system.config.loader import ConfigLoader

logger = logging.getLogger(__name__)


def format_combatant_status_short(
    combatant: 'Combatant', 
    config_loader: 'ConfigLoader'
) -> str:
    """
    生成戰鬥單位狀態的簡短文本
    
    Args:
        combatant: 戰鬥單位
        config_loader: 配置加載器
        
    Returns:
        格式化的狀態字符串
    """
    try:
        # 獲取基本信息
        name = getattr(combatant, 'display_name', 'Unknown')
        current_hp = getattr(combatant, 'current_hp', 0)
        max_hp = getattr(combatant, 'max_hp', 1)
        current_mp = getattr(combatant, 'current_mp', 0)
        max_mp = getattr(combatant, 'max_mp', 0)
        
        # 構建狀態字符串
        status = f"**{name}** HP: {current_hp}/{max_hp}"
        
        if max_mp > 0:
            status += f" MP: {current_mp}/{max_mp}"
        
        # 添加狀態效果圖標
        status_effects = getattr(combatant, 'status_effects', {})
        if status_effects:
            icons = []
            for effect_id, effect_data in status_effects.items():
                if effect_data and effect_data.get('remaining_turns', 0) > 0:
                    icon = get_status_effect_icon(effect_id)
                    if icon:
                        icons.append(icon)
            
            if icons:
                status += f" {' '.join(icons)}"
        
        return status
        
    except Exception as e:
        logger.error(f"格式化戰鬥單位狀態失敗: {e}")
        return f"**{getattr(combatant, 'display_name', 'Unknown')}** (狀態錯誤)"


def format_combatant_status_detailed(
    combatant: 'Combatant', 
    config_loader: 'ConfigLoader'
) -> str:
    """
    生成戰鬥單位狀態的詳細文本
    
    Args:
        combatant: 戰鬥單位
        config_loader: 配置加載器
        
    Returns:
        格式化的詳細狀態字符串
    """
    try:
        # 獲取基本信息
        name = getattr(combatant, 'display_name', 'Unknown')
        current_hp = getattr(combatant, 'current_hp', 0)
        max_hp = getattr(combatant, 'max_hp', 1)
        current_mp = getattr(combatant, 'current_mp', 0)
        max_mp = getattr(combatant, 'max_mp', 0)
        
        # 獲取屬性
        attack = getattr(combatant, 'attack', 0)
        defense = getattr(combatant, 'defense', 0)
        speed = getattr(combatant, 'speed', 0)
        
        # 構建詳細狀態
        status_lines = [
            f"**{name}**",
            f"HP: {current_hp}/{max_hp}",
        ]
        
        if max_mp > 0:
            status_lines.append(f"MP: {current_mp}/{max_mp}")
        
        status_lines.extend([
            f"攻擊: {attack}",
            f"防禦: {defense}",
            f"速度: {speed}"
        ])
        
        # 添加狀態效果詳情
        status_effects = getattr(combatant, 'status_effects', {})
        if status_effects:
            effect_lines = []
            for effect_id, effect_data in status_effects.items():
                if effect_data and effect_data.get('remaining_turns', 0) > 0:
                    remaining = effect_data['remaining_turns']
                    icon = get_status_effect_icon(effect_id)
                    effect_name = get_status_effect_name(effect_id)
                    effect_lines.append(f"{icon} {effect_name} ({remaining}回合)")
            
            if effect_lines:
                status_lines.append("狀態效果:")
                status_lines.extend(effect_lines)
        
        return "\n".join(status_lines)
        
    except Exception as e:
        logger.error(f"格式化戰鬥單位詳細狀態失敗: {e}")
        return f"**{getattr(combatant, 'display_name', 'Unknown')}** (詳細狀態錯誤)"


def get_status_effect_icon(effect_id: str) -> str:
    """
    獲取狀態效果圖標
    
    Args:
        effect_id: 狀態效果ID
        
    Returns:
        對應的 Emoji 圖標
    """
    icon_map = {
        "BURN": "🔥",
        "POISON": "☠️",
        "FREEZE": "🧊",
        "STUN": "💫",
        "SHIELD": "🛡️",
        "REGEN": "💚",
        "ATTACK_UP": "⚔️",
        "DEFENSE_UP": "🛡️",
        "SPEED_UP": "💨",
        "ATTACK_DOWN": "🔻",
        "DEFENSE_DOWN": "📉",
        "SPEED_DOWN": "🐌"
    }
    
    return icon_map.get(effect_id, "🔸")


def get_status_effect_name(effect_id: str) -> str:
    """
    獲取狀態效果名稱
    
    Args:
        effect_id: 狀態效果ID
        
    Returns:
        狀態效果的中文名稱
    """
    name_map = {
        "BURN": "燃燒",
        "POISON": "中毒",
        "FREEZE": "冰凍",
        "STUN": "暈眩",
        "SHIELD": "護盾",
        "REGEN": "回復",
        "ATTACK_UP": "攻擊提升",
        "DEFENSE_UP": "防禦提升",
        "SPEED_UP": "速度提升",
        "ATTACK_DOWN": "攻擊降低",
        "DEFENSE_DOWN": "防禦降低",
        "SPEED_DOWN": "速度降低"
    }
    
    return name_map.get(effect_id, effect_id)


def format_hp_bar(current_hp: int, max_hp: int, bar_length: int = 10) -> str:
    """
    生成 HP 血條
    
    Args:
        current_hp: 當前 HP
        max_hp: 最大 HP
        bar_length: 血條長度
        
    Returns:
        血條字符串
    """
    try:
        if max_hp <= 0:
            return "❌" * bar_length
        
        ratio = max(0, min(1, current_hp / max_hp))
        filled_length = int(ratio * bar_length)
        
        if ratio >= 0.7:
            filled_char = "🟢"
        elif ratio >= 0.3:
            filled_char = "🟡"
        else:
            filled_char = "🔴"
        
        empty_char = "⚫"
        
        bar = filled_char * filled_length + empty_char * (bar_length - filled_length)
        return bar
        
    except Exception as e:
        logger.error(f"生成HP血條失敗: {e}")
        return "❓" * bar_length


def format_team_status_summary(team: list, config_loader: 'ConfigLoader') -> str:
    """
    格式化隊伍狀態摘要
    
    Args:
        team: 隊伍成員列表
        config_loader: 配置加載器
        
    Returns:
        隊伍狀態摘要字符串
    """
    try:
        if not team:
            return "無隊伍成員"
        
        alive_count = 0
        total_count = len(team)
        
        for member in team:
            if member and getattr(member, 'current_hp', 0) > 0:
                alive_count += 1
        
        status = f"存活: {alive_count}/{total_count}"
        
        if alive_count == 0:
            status += " ☠️"
        elif alive_count == total_count:
            status += " ✅"
        else:
            status += " ⚠️"
        
        return status
        
    except Exception as e:
        logger.error(f"格式化隊伍狀態摘要失敗: {e}")
        return "隊伍狀態錯誤"
