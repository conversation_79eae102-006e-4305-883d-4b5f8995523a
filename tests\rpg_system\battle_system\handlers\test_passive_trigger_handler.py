"""
被動觸發處理器單元測試
"""
import unittest
from unittest.mock import Mock, MagicMock, patch

from rpg_system.battle_system.handlers.passive_trigger_handler import (
    PassiveTriggerHandler, PassiveTriggerHandlerError
)
from rpg_system.battle_system.events import EVENT_ON_DEAL_DAMAGE, EVENT_ON_TAKE_DAMAGE


class TestPassiveTriggerHandler(unittest.TestCase):
    """PassiveTriggerHandler 測試類"""
    
    def setUp(self):
        """測試前設置"""
        self.mock_effect_applier = Mock()
        self.mock_formula_evaluator = Mock()
        self.mock_config_loader = Mock()
        
        self.handler = PassiveTriggerHandler(
            self.mock_effect_applier,
            self.mock_formula_evaluator,
            self.mock_config_loader
        )
        
        # 創建模擬戰鬥者
        self.caster = Mock()
        self.caster.combatant_id = "caster_001"
        self.caster.is_player = True
        self.caster.current_hp = 100
        self.caster.get_hp_percent.return_value = 1.0
        self.caster.current_mp = 50
        self.caster.get_mp_percent.return_value = 0.5
        self.caster.rpg_level = 10
        self.caster.star_level = 3
        
        self.target = Mock()
        self.target.combatant_id = "target_001"
        self.target.is_player = False
        self.target.current_hp = 80
        self.target.get_hp_percent.return_value = 0.8
        self.target.current_mp = 30
        self.target.get_mp_percent.return_value = 0.6
        self.target.rpg_level = 8
        self.target.star_level = 2
        
        # 設置模擬屬性
        mock_stats = Mock()
        mock_stats.patk = 100
        mock_stats.pdef = 50
        mock_stats.matk = 80
        mock_stats.mdef = 40
        mock_stats.spd = 70
        mock_stats.crit_rate = 0.1
        mock_stats.crit_dmg_multiplier = 1.5
        mock_stats.accuracy = 0.95
        mock_stats.evasion = 0.05
        
        self.caster.get_current_stats.return_value = mock_stats
        self.target.get_current_stats.return_value = mock_stats
        
        # 創建模擬戰鬥上下文
        self.mock_battle = Mock()
        self.mock_battle.get_alive_combatants.return_value = [self.caster, self.target]
        
        # 創建模擬被動技能
        self.mock_passive_skill = Mock()
        self.mock_passive_skill.skill_id = "passive_001"
        self.mock_passive_skill.current_level = 1
    
    def test_reset_battle_triggers(self):
        """測試重置戰鬥觸發記錄"""
        # 添加一些觸發記錄
        self.handler._triggered_once_per_battle[("test", "skill", 0)] = True
        
        # 重置
        self.handler.reset_battle_triggers()
        
        # 驗證記錄被清空
        self.assertEqual(len(self.handler._triggered_once_per_battle), 0)
    
    def test_get_combatant_passives(self):
        """測試獲取戰鬥者被動技能"""
        # 設置戰鬥者的被動技能
        passive1 = Mock()
        passive2 = Mock()
        self.caster.common_passives = [passive1]
        self.caster.innate_passive_skill = passive2
        
        # 獲取被動技能
        passives = self.handler._get_combatant_passives(self.caster)
        
        # 驗證結果
        self.assertEqual(len(passives), 2)
        self.assertIn(passive1, passives)
        self.assertIn(passive2, passives)
    
    def test_get_combatant_passives_empty(self):
        """測試獲取空被動技能"""
        # 設置戰鬥者沒有被動技能
        self.caster.common_passives = None
        self.caster.innate_passive_skill = None
        
        # 獲取被動技能
        passives = self.handler._get_combatant_passives(self.caster)
        
        # 驗證結果
        self.assertEqual(len(passives), 0)
    
    def test_get_effects_for_current_level(self):
        """測試獲取當前等級的效果塊"""
        # 測試按等級分組的效果
        passive_config = {
            "effects_by_level": {
                "1": [{"type": "DAMAGE"}],
                "2": [{"type": "HEAL"}]
            }
        }
        
        effects = self.handler._get_effects_for_current_level(passive_config, 1)
        self.assertEqual(len(effects), 1)
        self.assertEqual(effects[0]["type"], "DAMAGE")
        
        # 測試默認效果
        passive_config = {
            "effects": [{"type": "BUFF"}]
        }
        
        effects = self.handler._get_effects_for_current_level(passive_config, 1)
        self.assertEqual(len(effects), 1)
        self.assertEqual(effects[0]["type"], "BUFF")
    
    def test_check_trigger_source_self(self):
        """測試觸發源檢查 - 自己"""
        trigger_condition = {"source": "SELF"}
        event_data = {"target": self.caster}
        
        result = self.handler._check_trigger_source(
            trigger_condition, self.caster, event_data
        )
        
        self.assertTrue(result)
    
    def test_check_trigger_source_ally(self):
        """測試觸發源檢查 - 盟友"""
        ally = Mock()
        ally.is_player = True  # 與caster同陣營
        
        trigger_condition = {"source": "ALLY"}
        event_data = {"target": ally}
        
        result = self.handler._check_trigger_source(
            trigger_condition, self.caster, event_data
        )
        
        self.assertTrue(result)
    
    def test_check_trigger_source_enemy(self):
        """測試觸發源檢查 - 敵人"""
        trigger_condition = {"source": "ENEMY"}
        event_data = {"target": self.target}  # target是敵人
        
        result = self.handler._check_trigger_source(
            trigger_condition, self.caster, event_data
        )
        
        self.assertTrue(result)
    
    def test_check_trigger_source_any(self):
        """測試觸發源檢查 - 任何"""
        trigger_condition = {"source": "ANY"}
        event_data = {"target": self.target}
        
        result = self.handler._check_trigger_source(
            trigger_condition, self.caster, event_data
        )
        
        self.assertTrue(result)
    
    def test_check_sub_type_damage_type(self):
        """測試子類型檢查 - 傷害類型"""
        trigger_condition = {"sub_type": "PHYSICAL"}
        event_data = {"damage_type": "PHYSICAL"}
        
        result = self.handler._check_sub_type(trigger_condition, event_data)
        
        self.assertTrue(result)
        
        # 測試不匹配的情況
        event_data = {"damage_type": "MAGICAL"}
        result = self.handler._check_sub_type(trigger_condition, event_data)
        
        self.assertFalse(result)
    
    def test_check_sub_type_skill_tags(self):
        """測試子類型檢查 - 技能標籤"""
        trigger_condition = {"sub_type": "FIRE"}
        event_data = {"skill_tags": ["FIRE", "MAGIC"]}
        
        result = self.handler._check_sub_type(trigger_condition, event_data)
        
        self.assertTrue(result)
        
        # 測試不匹配的情況
        event_data = {"skill_tags": ["ICE", "MAGIC"]}
        result = self.handler._check_sub_type(trigger_condition, event_data)
        
        self.assertFalse(result)
    
    def test_check_trigger_chance(self):
        """測試觸發機率檢查"""
        trigger_condition = {"chance_formula": "0.5"}
        
        # 設置公式評估結果
        self.mock_formula_evaluator.evaluate.return_value = 0.5
        
        # 測試觸發（隨機數 < 0.5）
        with patch('random.random', return_value=0.3):
            result = self.handler._check_trigger_chance(
                trigger_condition, self.caster, self.mock_passive_skill, {}
            )
            self.assertTrue(result)
        
        # 測試不觸發（隨機數 >= 0.5）
        with patch('random.random', return_value=0.7):
            result = self.handler._check_trigger_chance(
                trigger_condition, self.caster, self.mock_passive_skill, {}
            )
            self.assertFalse(result)
    
    def test_check_additional_conditions(self):
        """測試額外條件檢查"""
        trigger_condition = {
            "additional_conditions": [
                {"formula": "caster_current_hp > 50"},
                {"formula": "target_current_hp < 100"}
            ]
        }
        
        # 設置公式評估結果（都為True）
        self.mock_formula_evaluator.evaluate.side_effect = [True, True]
        
        result = self.handler._check_additional_conditions(
            trigger_condition, self.caster, self.mock_passive_skill, {"target": self.target}
        )
        
        self.assertTrue(result)
        
        # 測試有條件不滿足的情況
        self.mock_formula_evaluator.evaluate.side_effect = [True, False]
        
        result = self.handler._check_additional_conditions(
            trigger_condition, self.caster, self.mock_passive_skill, {"target": self.target}
        )
        
        self.assertFalse(result)
    
    def test_check_once_per_battle(self):
        """測試一次性觸發檢查"""
        effect_block = {"trigger_once_per_battle": True}
        
        # 第一次檢查應該通過
        result = self.handler._check_once_per_battle(
            self.caster, self.mock_passive_skill, 0, effect_block
        )
        self.assertTrue(result)
        
        # 標記為已觸發
        trigger_key = (self.caster.combatant_id, self.mock_passive_skill.skill_id, 0)
        self.handler._triggered_once_per_battle[trigger_key] = True
        
        # 第二次檢查應該失敗
        result = self.handler._check_once_per_battle(
            self.caster, self.mock_passive_skill, 0, effect_block
        )
        self.assertFalse(result)
    
    def test_prepare_context_vars(self):
        """測試準備上下文變量"""
        event_data = {"target": self.target, "damage_amount": 100}
        
        context_vars = self.handler._prepare_context_vars(
            self.caster, self.mock_passive_skill, event_data
        )
        
        # 驗證施法者信息
        self.assertEqual(context_vars["caster"], self.caster)
        self.assertEqual(context_vars["caster_current_hp"], 100)
        self.assertEqual(context_vars["caster_level"], 10)
        
        # 驗證被動技能信息
        self.assertEqual(context_vars["passive_skill"], self.mock_passive_skill)
        self.assertEqual(context_vars["skill_level"], 1)
        
        # 驗證目標信息
        self.assertEqual(context_vars["target_current_hp"], 80)
        self.assertEqual(context_vars["target_level"], 8)
        
        # 驗證事件數據
        self.assertEqual(context_vars["damage_amount"], 100)
    
    def test_get_default_passive_targets(self):
        """測試獲取默認被動目標"""
        effect_block = {"default_target_from_event_data": "target"}
        event_data = {"target": self.target}
        
        targets = self.handler._get_default_passive_targets(
            self.caster, effect_block, event_data
        )
        
        self.assertEqual(len(targets), 1)
        self.assertEqual(targets[0], self.target)
        
        # 測試沒有指定目標時默認為自己
        effect_block = {}
        event_data = {}
        
        targets = self.handler._get_default_passive_targets(
            self.caster, effect_block, event_data
        )
        
        self.assertEqual(len(targets), 1)
        self.assertEqual(targets[0], self.caster)


if __name__ == '__main__':
    unittest.main()
