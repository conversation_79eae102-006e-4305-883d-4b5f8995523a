"""
Monsters Pydantic Models
Based on RPG_02_Configuration_Files.md
"""
from typing import List, Dict
from pydantic import BaseModel, Field

# -------------------- EquippedMonsterPassive Model --------------------
class EquippedMonsterPassive(BaseModel):
    """怪物裝備的被動技能"""
    skill_id: str
    level: int = Field(..., ge=1)

# -------------------- MonsterConfig Model --------------------
class MonsterConfig(BaseModel):
    """怪物配置"""
    name: str
    hp: int = Field(..., ge=1)
    max_mp: int = Field(..., ge=0)
    mp_regen_per_turn: int = Field(..., ge=0)
    patk: int = Field(..., ge=0)
    pdef: int = Field(..., ge=0)
    matk: int = Field(..., ge=0)
    mdef: int = Field(..., ge=0)
    spd: int = Field(..., ge=0)
    crit_rate: float = Field(..., ge=0, le=1)
    crit_dmg_multiplier: float = Field(..., ge=1)
    accuracy: float = Field(..., ge=0, le=1)
    evasion: float = Field(..., ge=0, le=1)
    primary_attack_skill_id: str
    active_skill_order: List[str]
    equipped_passives: List[EquippedMonsterPassive]

# -------------------- Main Model --------------------
AllMonstersConfig = Dict[str, MonsterConfig]
