"""
戰鬥 Cog

處理與 PVE 戰鬥相關的 Discord 命令
"""
from typing import Dict, Any, Optional, TYPE_CHECKING
import discord
from discord.ext import commands
from discord import app_commands
import logging

if TYPE_CHECKING:
    from rpg_system.services.battle_coordinator_service import BattleCoordinatorService
    from rpg_system.config.loader import ConfigLoader
    from rpg_system.core.battle import Battle

from rpg_system.views.embeds.battle_embeds import BattleEmbedGenerator

logger = logging.getLogger(__name__)


class BattleView(discord.ui.View):
    """
    戰鬥交互視圖

    處理戰鬥中的按鈕交互
    """

    def __init__(
        self,
        battle_instance: 'Battle',
        battle_cog: 'BattleCog',
        user_id: int,
        timeout: float = 300.0
    ):
        """
        初始化戰鬥視圖

        Args:
            battle_instance: 戰鬥實例
            battle_cog: 戰鬥 Cog 實例
            user_id: 用戶ID
            timeout: 超時時間（秒）
        """
        super().__init__(timeout=timeout)
        self.battle = battle_instance
        self.battle_cog = battle_cog
        self.user_id = user_id
        self.selected_skill_id: Optional[str] = None
        self.phase = "skill_selection"  # "skill_selection" 或 "target_selection"
        self.message_log = []

        # 初始化技能按鈕
        self._setup_skill_buttons()

    def _setup_skill_buttons(self):
        """設置技能選擇按鈕"""
        try:
            # 清除現有按鈕
            self.clear_items()

            # 獲取當前行動者
            current_actor = getattr(self.battle, 'current_actor', None)
            if not current_actor:
                return

            # 檢查是否是玩家回合
            player_team = getattr(self.battle, 'player_team', [])
            if current_actor not in player_team:
                # AI 回合，不顯示按鈕
                return

            # 獲取可用技能
            available_skills = getattr(current_actor, 'available_skills', [])
            current_mp = getattr(current_actor, 'current_mp', 0)

            for i, skill in enumerate(available_skills[:5]):  # 最多5個技能按鈕
                skill_id = getattr(skill, 'skill_id', f'skill_{i}')
                skill_name = getattr(skill, 'name', skill_id)
                mp_cost = getattr(skill, 'mp_cost', 0)
                cooldown = getattr(skill, 'current_cooldown', 0)

                # 檢查技能是否可用
                can_use = current_mp >= mp_cost and cooldown <= 0

                # 創建按鈕
                button = discord.ui.Button(
                    label=skill_name,
                    custom_id=f"skill_{skill_id}",
                    style=discord.ButtonStyle.primary if can_use else discord.ButtonStyle.secondary,
                    disabled=not can_use,
                    row=i // 5  # 每行最多5個按鈕
                )

                # 設置回調
                async def skill_callback(interaction: discord.Interaction, skill_id=skill_id):
                    await self._handle_skill_selection(interaction, skill_id)

                button.callback = skill_callback
                self.add_item(button)

            # 添加放棄戰鬥按鈕
            surrender_button = discord.ui.Button(
                label="放棄戰鬥",
                custom_id="surrender",
                style=discord.ButtonStyle.danger,
                row=4
            )
            surrender_button.callback = self._handle_surrender
            self.add_item(surrender_button)

        except Exception as e:
            logger.error(f"設置技能按鈕失敗: {e}")

    async def _handle_skill_selection(self, interaction: discord.Interaction, skill_id: str):
        """處理技能選擇"""
        try:
            # 檢查用戶權限
            if interaction.user.id != self.user_id:
                await interaction.response.send_message("這不是你的戰鬥！", ephemeral=True)
                return

            # 延遲響應
            await interaction.response.defer()

            self.selected_skill_id = skill_id

            # 檢查技能是否需要選擇目標
            # 簡化處理：假設所有技能都需要選擇目標
            self.phase = "target_selection"
            self._setup_target_buttons()

            # 生成目標選擇 Embed
            current_actor = getattr(self.battle, 'current_actor', None)
            if current_actor:
                embed = self.battle_cog.embed_generator.generate_battle_target_selection_embed(
                    self.battle, current_actor, skill_id
                )
                await interaction.edit_original_response(embed=embed, view=self)

        except Exception as e:
            logger.error(f"處理技能選擇失敗: {e}")
            await interaction.followup.send("技能選擇失敗，請重試", ephemeral=True)

    def _setup_target_buttons(self):
        """設置目標選擇按鈕"""
        try:
            # 清除現有按鈕
            self.clear_items()

            # 獲取敵方隊伍作為目標
            enemy_team = getattr(self.battle, 'enemy_team', [])

            for i, target in enumerate(enemy_team):
                if target and getattr(target, 'current_hp', 0) > 0:
                    target_name = getattr(target, 'display_name', f'敵人{i+1}')
                    target_id = getattr(target, 'instance_id', f'enemy_{i}')

                    # 創建目標按鈕
                    button = discord.ui.Button(
                        label=target_name,
                        custom_id=f"target_{target_id}",
                        style=discord.ButtonStyle.success,
                        row=i // 5
                    )

                    # 設置回調
                    async def target_callback(interaction: discord.Interaction, target_id=target_id):
                        await self._handle_target_selection(interaction, target_id)

                    button.callback = target_callback
                    self.add_item(button)

            # 添加返回按鈕
            back_button = discord.ui.Button(
                label="返回技能選擇",
                custom_id="back_to_skills",
                style=discord.ButtonStyle.secondary,
                row=4
            )
            back_button.callback = self._handle_back_to_skills
            self.add_item(back_button)

        except Exception as e:
            logger.error(f"設置目標按鈕失敗: {e}")

    async def _handle_target_selection(self, interaction: discord.Interaction, target_id: str):
        """處理目標選擇"""
        try:
            # 檢查用戶權限
            if interaction.user.id != self.user_id:
                await interaction.response.send_message("這不是你的戰鬥！", ephemeral=True)
                return

            # 延遲響應
            await interaction.response.defer()

            if not self.selected_skill_id:
                await interaction.followup.send("請先選擇技能", ephemeral=True)
                return

            # 處理玩家行動
            action_result = await self.battle_cog.battle_coordinator_service.process_player_action(
                self.battle, self.selected_skill_id, target_id
            )

            if action_result:
                # 添加行動結果到日誌
                self.message_log.append(f"玩家使用了 {self.selected_skill_id}")

                # 推進戰鬥回合（處理AI回合）
                turn_result = await self.battle_cog.battle_coordinator_service.advance_battle_turn(self.battle)

                # 檢查戰鬥是否結束
                battle_status = getattr(self.battle, 'status', 'ONGOING')
                if battle_status != 'ONGOING':
                    # 戰鬥結束
                    await self._handle_battle_end(interaction)
                    return

                # 更新戰鬥狀態顯示
                embed = self.battle_cog.embed_generator.generate_battle_state_embed(
                    self.battle, self.message_log
                )

                # 重置為技能選擇階段
                self.phase = "skill_selection"
                self.selected_skill_id = None
                self._setup_skill_buttons()

                await interaction.edit_original_response(embed=embed, view=self)
            else:
                await interaction.followup.send("行動執行失敗，請重試", ephemeral=True)

        except Exception as e:
            logger.error(f"處理目標選擇失敗: {e}")
            await interaction.followup.send("目標選擇失敗，請重試", ephemeral=True)

    async def _handle_back_to_skills(self, interaction: discord.Interaction):
        """處理返回技能選擇"""
        try:
            # 檢查用戶權限
            if interaction.user.id != self.user_id:
                await interaction.response.send_message("這不是你的戰鬥！", ephemeral=True)
                return

            # 延遲響應
            await interaction.response.defer()

            # 重置狀態
            self.phase = "skill_selection"
            self.selected_skill_id = None
            self._setup_skill_buttons()

            # 生成技能選擇 Embed
            current_actor = getattr(self.battle, 'current_actor', None)
            if current_actor:
                embed = self.battle_cog.embed_generator.generate_battle_action_selection_embed(
                    self.battle, current_actor
                )
                await interaction.edit_original_response(embed=embed, view=self)

        except Exception as e:
            logger.error(f"處理返回技能選擇失敗: {e}")
            await interaction.followup.send("返回失敗，請重試", ephemeral=True)

    async def _handle_surrender(self, interaction: discord.Interaction):
        """處理放棄戰鬥"""
        try:
            # 檢查用戶權限
            if interaction.user.id != self.user_id:
                await interaction.response.send_message("這不是你的戰鬥！", ephemeral=True)
                return

            # 延遲響應
            await interaction.response.defer()

            # 設置戰鬥狀態為失敗
            setattr(self.battle, 'status', 'DEFEAT')

            # 處理戰鬥結束
            await self._handle_battle_end(interaction)

        except Exception as e:
            logger.error(f"處理放棄戰鬥失敗: {e}")
            await interaction.followup.send("放棄戰鬥失敗，請重試", ephemeral=True)

    async def _handle_battle_end(self, interaction: discord.Interaction):
        """處理戰鬥結束"""
        try:
            # 獲取戰鬥結果
            battle_results = await self.battle_cog.battle_coordinator_service.finalize_battle(self.battle)

            # 生成戰鬥結束 Embed
            embed = self.battle_cog.embed_generator.generate_battle_end_embed(
                self.battle, battle_results
            )

            # 清除所有按鈕
            self.clear_items()

            # 更新消息
            await interaction.edit_original_response(embed=embed, view=self)

            # 從活動戰鬥中移除
            if self.user_id in self.battle_cog.active_battles:
                del self.battle_cog.active_battles[self.user_id]

        except Exception as e:
            logger.error(f"處理戰鬥結束失敗: {e}")
            await interaction.followup.send("戰鬥結束處理失敗", ephemeral=True)

    async def on_timeout(self):
        """處理超時"""
        try:
            # 清除所有按鈕
            self.clear_items()

            # 從活動戰鬥中移除
            if self.user_id in self.battle_cog.active_battles:
                del self.battle_cog.active_battles[self.user_id]

        except Exception as e:
            logger.error(f"處理戰鬥超時失敗: {e}")


class BattleCog(commands.Cog):
    """
    戰鬥 Cog

    處理與 PVE 戰鬥相關的 Discord 命令
    """

    def __init__(
        self,
        bot: commands.Bot,
        battle_coordinator_service: 'BattleCoordinatorService',
        config_loader: 'ConfigLoader'
    ):
        """
        初始化戰鬥 Cog

        Args:
            bot: Discord Bot 實例
            battle_coordinator_service: 戰鬥協調服務
            config_loader: 配置加載器
        """
        self.bot = bot
        self.battle_coordinator_service = battle_coordinator_service
        self.config_loader = config_loader
        self.embed_generator = BattleEmbedGenerator(config_loader)

        # 存儲活動的戰鬥會話
        self.active_battles: Dict[int, BattleView] = {}

    @app_commands.command(name="pve_start", description="開始 PVE 戰鬥")
    @app_commands.describe(floor_id="樓層ID（可選，默認為當前樓層）")
    async def pve_start(self, interaction: discord.Interaction, floor_id: Optional[str] = None):
        """開始 PVE 戰鬥"""
        try:
            user_id = interaction.user.id

            # 檢查是否已有活動戰鬥
            if user_id in self.active_battles:
                await interaction.response.send_message(
                    "你已經在戰鬥中了！請先完成當前戰鬥。",
                    ephemeral=True
                )
                return

            # 延遲響應
            await interaction.response.defer()

            # 準備戰鬥
            battle_instance = await self.battle_coordinator_service.prepare_pve_battle(
                user_id, floor_id
            )

            if not battle_instance:
                await interaction.followup.send("戰鬥準備失敗，請檢查你的隊伍配置和樓層狀態。", ephemeral=True)
                return

            # 開始戰鬥
            start_result = await self.battle_coordinator_service.start_battle(battle_instance)
            if not start_result:
                await interaction.followup.send("戰鬥開始失敗，請重試。", ephemeral=True)
                return

            # 創建戰鬥視圖
            battle_view = BattleView(battle_instance, self, user_id)
            self.active_battles[user_id] = battle_view

            # 生成初始戰鬥狀態 Embed
            embed = self.embed_generator.generate_battle_state_embed(battle_instance)

            # 發送戰鬥消息
            await interaction.followup.send(embed=embed, view=battle_view)

        except Exception as e:
            logger.error(f"PVE 戰鬥開始失敗: {e}")
            await interaction.followup.send(f"戰鬥開始失敗: {str(e)}", ephemeral=True)

    @app_commands.command(name="pve_status", description="查看 PVE 進度狀態")
    async def pve_status(self, interaction: discord.Interaction):
        """查看 PVE 進度狀態"""
        try:
            user_id = interaction.user.id

            # 延遲響應
            await interaction.response.defer()

            # 獲取用戶進度
            user_progress = await self.battle_coordinator_service.get_user_pve_progress(user_id)

            if not user_progress:
                await interaction.followup.send("無法獲取你的 PVE 進度，請重試。", ephemeral=True)
                return

            # 生成狀態 Embed
            embed = self.embed_generator.generate_pve_status_embed(user_progress)

            # 發送狀態消息
            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"獲取 PVE 狀態失敗: {e}")
            await interaction.followup.send(f"獲取 PVE 狀態失敗: {str(e)}", ephemeral=True)

    @app_commands.command(name="pve_stop", description="停止當前戰鬥")
    async def pve_stop(self, interaction: discord.Interaction):
        """停止當前戰鬥"""
        try:
            user_id = interaction.user.id

            # 檢查是否有活動戰鬥
            if user_id not in self.active_battles:
                await interaction.response.send_message(
                    "你目前沒有進行中的戰鬥。",
                    ephemeral=True
                )
                return

            # 移除活動戰鬥
            battle_view = self.active_battles[user_id]
            del self.active_battles[user_id]

            # 清除視圖按鈕
            battle_view.clear_items()

            await interaction.response.send_message(
                "戰鬥已停止。",
                ephemeral=True
            )

        except Exception as e:
            logger.error(f"停止戰鬥失敗: {e}")
            await interaction.response.send_message(f"停止戰鬥失敗: {str(e)}", ephemeral=True)


async def setup(bot: commands.Bot):
    """設置 Cog"""
    # 注意：這裡需要從主應用程序傳入依賴
    # 實際使用時需要根據你的依賴注入方式調整
    pass
