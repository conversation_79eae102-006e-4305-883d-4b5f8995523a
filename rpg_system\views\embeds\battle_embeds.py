"""
戰鬥 Embed 生成器

負責根據戰鬥狀態生成美觀的 Discord Embeds
"""
from typing import Dict, Any, Optional, List, TYPE_CHECKING
import discord
import logging

if TYPE_CHECKING:
    from rpg_system.core.battle import Battle
    from rpg_system.core.combatant import Combatant
    from rpg_system.config.loader import ConfigLoader

logger = logging.getLogger(__name__)


class BattleEmbedGenerator:
    """
    戰鬥 Embed 生成器

    負責根據戰鬥狀態生成美觀的 Discord Embeds
    """

    def __init__(self, config_loader: 'ConfigLoader'):
        """
        初始化戰鬥 Embed 生成器

        Args:
            config_loader: 配置加載器
        """
        self.config_loader = config_loader

        # 狀態效果 Emoji 映射
        self.status_emojis = {
            "BURN": "🔥",
            "POISON": "☠️",
            "FREEZE": "🧊",
            "STUN": "💫",
            "SHIELD": "🛡️",
            "REGEN": "💚",
            "ATTACK_UP": "⚔️",
            "DEFENSE_UP": "🛡️",
            "SPEED_UP": "💨",
            "ATTACK_DOWN": "🔻",
            "DEFENSE_DOWN": "📉",
            "SPEED_DOWN": "🐌"
        }

        # HP/MP 條 Emoji
        self.hp_emojis = {
            "full": "🟢",
            "high": "🟡",
            "medium": "🟠",
            "low": "🔴",
            "empty": "⚫"
        }

        self.mp_emojis = {
            "full": "🔵",
            "high": "🟦",
            "medium": "🟪",
            "low": "🟣",
            "empty": "⚫"
        }

    def _get_hp_emoji(self, current_hp: int, max_hp: int) -> str:
        """獲取 HP 狀態 Emoji"""
        if current_hp <= 0:
            return self.hp_emojis["empty"]

        ratio = current_hp / max_hp
        if ratio >= 0.8:
            return self.hp_emojis["full"]
        elif ratio >= 0.6:
            return self.hp_emojis["high"]
        elif ratio >= 0.3:
            return self.hp_emojis["medium"]
        else:
            return self.hp_emojis["low"]

    def _get_mp_emoji(self, current_mp: int, max_mp: int) -> str:
        """獲取 MP 狀態 Emoji"""
        if max_mp == 0:
            return ""

        if current_mp <= 0:
            return self.mp_emojis["empty"]

        ratio = current_mp / max_mp
        if ratio >= 0.8:
            return self.mp_emojis["full"]
        elif ratio >= 0.6:
            return self.mp_emojis["high"]
        elif ratio >= 0.3:
            return self.mp_emojis["medium"]
        else:
            return self.mp_emojis["low"]

    def _format_combatant_status(self, combatant: 'Combatant') -> str:
        """
        格式化戰鬥單位狀態

        Args:
            combatant: 戰鬥單位

        Returns:
            格式化的狀態字符串
        """
        try:
            # 獲取名稱
            name = getattr(combatant, 'display_name', 'Unknown')

            # 獲取 HP/MP
            current_hp = getattr(combatant, 'current_hp', 0)
            max_hp = getattr(combatant, 'max_hp', 1)
            current_mp = getattr(combatant, 'current_mp', 0)
            max_mp = getattr(combatant, 'max_mp', 0)

            # 生成狀態條
            hp_emoji = self._get_hp_emoji(current_hp, max_hp)
            mp_emoji = self._get_mp_emoji(current_mp, max_mp) if max_mp > 0 else ""

            # 獲取狀態效果
            status_effects = getattr(combatant, 'status_effects', {})
            status_icons = ""
            for effect_id, effect_data in status_effects.items():
                if effect_data and effect_data.get('remaining_turns', 0) > 0:
                    emoji = self.status_emojis.get(effect_id, "🔸")
                    status_icons += emoji

            # 構建狀態字符串
            status_line = f"{hp_emoji} **{name}**"
            status_line += f" HP: {current_hp}/{max_hp}"

            if max_mp > 0:
                status_line += f" {mp_emoji} MP: {current_mp}/{max_mp}"

            if status_icons:
                status_line += f" {status_icons}"

            return status_line

        except Exception as e:
            logger.error(f"格式化戰鬥單位狀態失敗: {e}")
            return f"**{getattr(combatant, 'display_name', 'Unknown')}** (狀態錯誤)"

    def generate_battle_state_embed(
        self,
        battle: 'Battle',
        message_log: Optional[List[str]] = None
    ) -> discord.Embed:
        """
        生成戰鬥狀態 Embed

        Args:
            battle: 戰鬥實例
            message_log: 戰鬥日誌消息列表

        Returns:
            Discord Embed 對象
        """
        try:
            # 獲取戰鬥基本信息
            turn_number = getattr(battle, 'turn_number', 1)
            battle_phase = getattr(battle, 'phase', 'UNKNOWN')

            # 創建 Embed
            embed = discord.Embed(
                title="⚔️ PVE 戰鬥",
                color=discord.Color.blue(),
                timestamp=discord.utils.utcnow()
            )

            # 添加回合信息
            embed.add_field(
                name="📊 戰鬥狀態",
                value=f"回合: {turn_number}\n階段: {battle_phase}",
                inline=True
            )

            # 添加玩家隊伍狀態
            player_team = getattr(battle, 'player_team', [])
            if player_team:
                player_status_lines = []
                for combatant in player_team:
                    if combatant:  # 確保戰鬥單位存在
                        status_line = self._format_combatant_status(combatant)
                        player_status_lines.append(status_line)

                embed.add_field(
                    name="👥 玩家隊伍",
                    value="\n".join(player_status_lines) if player_status_lines else "無隊伍成員",
                    inline=False
                )

            # 添加敵方隊伍狀態
            enemy_team = getattr(battle, 'enemy_team', [])
            if enemy_team:
                enemy_status_lines = []
                for combatant in enemy_team:
                    if combatant:  # 確保戰鬥單位存在
                        status_line = self._format_combatant_status(combatant)
                        enemy_status_lines.append(status_line)

                embed.add_field(
                    name="👹 敵方隊伍",
                    value="\n".join(enemy_status_lines) if enemy_status_lines else "無敵方單位",
                    inline=False
                )

            # 添加當前行動者信息
            current_actor = getattr(battle, 'current_actor', None)
            if current_actor:
                actor_name = getattr(current_actor, 'display_name', 'Unknown')
                embed.add_field(
                    name="🎯 當前行動者",
                    value=f"**{actor_name}** 的回合",
                    inline=True
                )

            # 添加戰鬥日誌
            if message_log:
                # 只顯示最近的幾條消息
                recent_messages = message_log[-3:] if len(message_log) > 3 else message_log
                log_text = "\n".join(recent_messages)
                if len(log_text) > 1024:  # Discord field 限制
                    log_text = log_text[:1021] + "..."

                embed.add_field(
                    name="📜 戰鬥日誌",
                    value=log_text,
                    inline=False
                )

            # 添加腳註
            embed.set_footer(text="選擇技能來進行行動")

            return embed

        except Exception as e:
            logger.error(f"生成戰鬥狀態 Embed 失敗: {e}")
            # 返回錯誤 Embed
            error_embed = discord.Embed(
                title="❌ 戰鬥狀態錯誤",
                description="無法顯示戰鬥狀態，請重試",
                color=discord.Color.red()
            )
            return error_embed

    def generate_battle_action_selection_embed(
        self,
        battle: 'Battle',
        acting_combatant: 'Combatant'
    ) -> discord.Embed:
        """
        生成技能選擇 Embed

        Args:
            battle: 戰鬥實例
            acting_combatant: 當前行動的戰鬥單位

        Returns:
            Discord Embed 對象
        """
        try:
            actor_name = getattr(acting_combatant, 'display_name', 'Unknown')

            embed = discord.Embed(
                title=f"🎯 {actor_name} 的回合",
                description="請選擇要使用的技能",
                color=discord.Color.green()
            )

            # 獲取可用技能
            available_skills = getattr(acting_combatant, 'available_skills', [])
            if available_skills:
                skill_descriptions = []
                for skill in available_skills:
                    skill_id = getattr(skill, 'skill_id', 'unknown')
                    skill_name = getattr(skill, 'name', skill_id)
                    mp_cost = getattr(skill, 'mp_cost', 0)
                    cooldown = getattr(skill, 'current_cooldown', 0)

                    # 檢查是否可用
                    current_mp = getattr(acting_combatant, 'current_mp', 0)
                    can_use = current_mp >= mp_cost and cooldown <= 0

                    status_icon = "✅" if can_use else "❌"
                    skill_desc = f"{status_icon} **{skill_name}**"

                    if mp_cost > 0:
                        skill_desc += f" (MP: {mp_cost})"

                    if cooldown > 0:
                        skill_desc += f" (冷卻: {cooldown}回合)"

                    skill_descriptions.append(skill_desc)

                embed.add_field(
                    name="🔮 可用技能",
                    value="\n".join(skill_descriptions),
                    inline=False
                )

            # 顯示當前狀態
            current_hp = getattr(acting_combatant, 'current_hp', 0)
            max_hp = getattr(acting_combatant, 'max_hp', 1)
            current_mp = getattr(acting_combatant, 'current_mp', 0)
            max_mp = getattr(acting_combatant, 'max_mp', 0)

            status_text = f"HP: {current_hp}/{max_hp}"
            if max_mp > 0:
                status_text += f"\nMP: {current_mp}/{max_mp}"

            embed.add_field(
                name="💪 當前狀態",
                value=status_text,
                inline=True
            )

            embed.set_footer(text="點擊下方按鈕選擇技能")

            return embed

        except Exception as e:
            logger.error(f"生成技能選擇 Embed 失敗: {e}")
            error_embed = discord.Embed(
                title="❌ 技能選擇錯誤",
                description="無法顯示技能選擇，請重試",
                color=discord.Color.red()
            )
            return error_embed

    def generate_battle_target_selection_embed(
        self,
        battle: 'Battle',
        acting_combatant: 'Combatant',
        skill_id: str
    ) -> discord.Embed:
        """
        生成目標選擇 Embed

        Args:
            battle: 戰鬥實例
            acting_combatant: 當前行動的戰鬥單位
            skill_id: 選擇的技能ID

        Returns:
            Discord Embed 對象
        """
        try:
            actor_name = getattr(acting_combatant, 'display_name', 'Unknown')

            # 獲取技能配置
            skill_config = None
            available_skills = getattr(acting_combatant, 'available_skills', [])
            for skill in available_skills:
                if getattr(skill, 'skill_id', '') == skill_id:
                    skill_config = skill
                    break

            skill_name = getattr(skill_config, 'name', skill_id) if skill_config else skill_id

            embed = discord.Embed(
                title=f"🎯 選擇目標",
                description=f"**{actor_name}** 使用 **{skill_name}**\n請選擇目標",
                color=discord.Color.orange()
            )

            # 顯示可能的目標
            # 這裡需要根據技能的目標類型來決定顯示哪些目標
            player_team = getattr(battle, 'player_team', [])
            enemy_team = getattr(battle, 'enemy_team', [])

            # 簡化處理：顯示所有存活的敵方單位作為可能目標
            if enemy_team:
                target_lines = []
                for i, target in enumerate(enemy_team):
                    if target and getattr(target, 'current_hp', 0) > 0:
                        target_name = getattr(target, 'display_name', f'敵人{i+1}')
                        target_hp = getattr(target, 'current_hp', 0)
                        target_max_hp = getattr(target, 'max_hp', 1)
                        target_lines.append(f"**{target_name}** (HP: {target_hp}/{target_max_hp})")

                if target_lines:
                    embed.add_field(
                        name="🎯 可選目標",
                        value="\n".join(target_lines),
                        inline=False
                    )

            embed.set_footer(text="點擊下方按鈕選擇目標")

            return embed

        except Exception as e:
            logger.error(f"生成目標選擇 Embed 失敗: {e}")
            error_embed = discord.Embed(
                title="❌ 目標選擇錯誤",
                description="無法顯示目標選擇，請重試",
                color=discord.Color.red()
            )
            return error_embed

    def generate_battle_end_embed(
        self,
        battle: 'Battle',
        battle_results: Dict[str, Any]
    ) -> discord.Embed:
        """
        生成戰鬥結束 Embed

        Args:
            battle: 戰鬥實例
            battle_results: 戰鬥結果數據

        Returns:
            Discord Embed 對象
        """
        try:
            # 獲取戰鬥結果
            is_victory = battle_results.get('is_victory', False)
            rewards = battle_results.get('rewards', [])
            progress_updates = battle_results.get('progress_updates', {})

            # 根據結果設置顏色和標題
            if is_victory:
                embed = discord.Embed(
                    title="🎉 戰鬥勝利！",
                    description="恭喜！你成功擊敗了敵人！",
                    color=discord.Color.gold()
                )
            else:
                embed = discord.Embed(
                    title="💀 戰鬥失敗",
                    description="很遺憾，你的隊伍被擊敗了...",
                    color=discord.Color.red()
                )

            # 添加戰鬥統計
            turn_number = getattr(battle, 'turn_number', 1)
            embed.add_field(
                name="📊 戰鬥統計",
                value=f"總回合數: {turn_number}",
                inline=True
            )

            # 如果勝利，顯示獎勵
            if is_victory and rewards:
                reward_lines = []
                for reward in rewards:
                    reward_type = reward.get('type', 'unknown')
                    reward_amount = reward.get('amount', 0)
                    reward_item_id = reward.get('item_id', '')

                    if reward_type == 'xp':
                        reward_lines.append(f"🌟 經驗值: +{reward_amount}")
                    elif reward_type == 'gold':
                        reward_lines.append(f"💰 金幣: +{reward_amount}")
                    elif reward_type == 'item':
                        reward_lines.append(f"🎁 道具: {reward_item_id} x{reward_amount}")
                    else:
                        reward_lines.append(f"🎁 {reward_type}: +{reward_amount}")

                if reward_lines:
                    embed.add_field(
                        name="🎁 獲得獎勵",
                        value="\n".join(reward_lines),
                        inline=False
                    )

            # 顯示進度更新
            if progress_updates:
                progress_lines = []

                if 'floor_progress' in progress_updates:
                    floor_data = progress_updates['floor_progress']
                    current_floor = floor_data.get('current_floor', 1)
                    wins = floor_data.get('wins', 0)
                    progress_lines.append(f"🏢 當前樓層: {current_floor}")
                    progress_lines.append(f"🏆 勝利次數: {wins}")

                if 'level_ups' in progress_updates:
                    level_ups = progress_updates['level_ups']
                    for card_info in level_ups:
                        card_name = card_info.get('card_name', 'Unknown')
                        new_level = card_info.get('new_level', 1)
                        progress_lines.append(f"⬆️ {card_name} 升級到 Lv.{new_level}")

                if progress_lines:
                    embed.add_field(
                        name="📈 進度更新",
                        value="\n".join(progress_lines),
                        inline=False
                    )

            # 添加腳註
            if is_victory:
                embed.set_footer(text="繼續挑戰更高的樓層吧！")
            else:
                embed.set_footer(text="重新整備後再次挑戰吧！")

            return embed

        except Exception as e:
            logger.error(f"生成戰鬥結束 Embed 失敗: {e}")
            error_embed = discord.Embed(
                title="❌ 戰鬥結果錯誤",
                description="無法顯示戰鬥結果，請重試",
                color=discord.Color.red()
            )
            return error_embed

    def generate_pve_status_embed(self, user_progress: Dict[str, Any]) -> discord.Embed:
        """
        生成 PVE 狀態 Embed

        Args:
            user_progress: 用戶進度數據

        Returns:
            Discord Embed 對象
        """
        try:
            embed = discord.Embed(
                title="🏢 PVE 進度狀態",
                color=discord.Color.blue()
            )

            current_floor = user_progress.get('current_floor_unlocked', 1)
            max_floor_cleared = user_progress.get('max_floor_cleared', 0)
            current_wins = user_progress.get('current_floor_wins', 0)

            embed.add_field(
                name="📊 進度信息",
                value=f"當前樓層: {current_floor}\n最高通關: {max_floor_cleared}\n當前勝利: {current_wins}",
                inline=True
            )

            # 顯示隊伍編成
            team_formation = user_progress.get('current_team_formation', [])
            if team_formation:
                team_info = f"隊伍成員數: {len([x for x in team_formation if x is not None])}/3"
                embed.add_field(
                    name="👥 當前隊伍",
                    value=team_info,
                    inline=True
                )

            embed.set_footer(text="使用 /pve_start 開始戰鬥")

            return embed

        except Exception as e:
            logger.error(f"生成 PVE 狀態 Embed 失敗: {e}")
            error_embed = discord.Embed(
                title="❌ PVE 狀態錯誤",
                description="無法顯示 PVE 狀態，請重試",
                color=discord.Color.red()
            )
            return error_embed
