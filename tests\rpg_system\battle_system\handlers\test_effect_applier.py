"""
效果應用器單元測試
"""
import unittest
from unittest.mock import Mock, MagicMock, patch

from rpg_system.battle_system.handlers.effect_applier import (
    EffectApplier, EffectApplierError
)


class TestEffectApplier(unittest.TestCase):
    """EffectApplier 測試類"""
    
    def setUp(self):
        """測試前設置"""
        self.mock_formula_evaluator = Mock()
        self.mock_target_selector = Mock()
        self.mock_config_loader = Mock()
        
        self.applier = EffectApplier(
            self.mock_formula_evaluator,
            self.mock_target_selector,
            self.mock_config_loader
        )
        
        # 創建模擬戰鬥者
        self.caster = Mock()
        self.caster.combatant_id = "caster_001"
        self.caster.is_player = True
        self.caster.current_hp = 100
        self.caster.get_hp_percent.return_value = 1.0
        self.caster.current_mp = 50
        self.caster.get_mp_percent.return_value = 0.5
        self.caster.rpg_level = 10
        self.caster.star_level = 3
        
        self.target = Mock()
        self.target.combatant_id = "target_001"
        self.target.is_player = False
        self.target.current_hp = 80
        self.target.get_hp_percent.return_value = 0.8
        self.target.current_mp = 30
        self.target.get_mp_percent.return_value = 0.6
        self.target.rpg_level = 8
        self.target.star_level = 2
        
        # 設置模擬屬性
        mock_caster_stats = Mock()
        mock_caster_stats.patk = 100
        mock_caster_stats.pdef = 50
        mock_caster_stats.matk = 80
        mock_caster_stats.mdef = 40
        mock_caster_stats.spd = 70
        mock_caster_stats.crit_rate = 0.1
        mock_caster_stats.crit_dmg_multiplier = 1.5
        mock_caster_stats.accuracy = 0.95
        mock_caster_stats.evasion = 0.05
        
        mock_target_stats = Mock()
        mock_target_stats.patk = 60
        mock_target_stats.pdef = 30
        mock_target_stats.matk = 40
        mock_target_stats.mdef = 20
        mock_target_stats.spd = 50
        mock_target_stats.crit_rate = 0.05
        mock_target_stats.crit_dmg_multiplier = 1.2
        mock_target_stats.accuracy = 0.9
        mock_target_stats.evasion = 0.1
        
        self.caster.get_current_stats.return_value = mock_caster_stats
        self.target.get_current_stats.return_value = mock_target_stats
        
        # 創建模擬戰鬥上下文
        self.mock_battle_context = Mock()
        self.mock_battle_context.current_turn = 1
        self.mock_battle_context.get_alive_combatants.return_value = [self.target]
        
        # 創建模擬技能實例
        self.mock_skill_instance = Mock()
        self.mock_skill_instance.skill_id = "fireball"
        self.mock_skill_instance.current_level = 5
    
    def test_apply_damage_effect(self):
        """測試傷害效果應用"""
        effect_def = {
            "type": "DAMAGE",
            "value_formula": "caster_stat_matk * 1.5",
            "damage_type": "MAGICAL"
        }
        
        # 設置公式評估結果
        self.mock_formula_evaluator.evaluate.return_value = 120  # 80 * 1.5
        
        # 設置目標受傷
        self.target.take_damage.return_value = 100
        
        with patch('random.random', return_value=0.5):  # 不暴擊
            log_entries = self.applier._apply_single_effect_to_target(
                self.caster, self.target, effect_def, self.mock_battle_context,
                [], self.mock_skill_instance
            )
        
        # 驗證結果
        self.assertEqual(len(log_entries), 1)
        self.assertEqual(log_entries[0].event_type, "DAMAGE")
        self.assertEqual(log_entries[0].damage_dealt, 100)
        self.target.take_damage.assert_called_once()
    
    def test_apply_heal_effect(self):
        """測試治療效果應用"""
        effect_def = {
            "type": "HEAL",
            "value_formula": "caster_stat_matk * 0.8"
        }
        
        # 設置公式評估結果
        self.mock_formula_evaluator.evaluate.return_value = 64  # 80 * 0.8
        
        # 設置目標治療
        self.target.heal.return_value = 64
        
        log_entries = self.applier._apply_single_effect_to_target(
            self.caster, self.target, effect_def, self.mock_battle_context,
            [], self.mock_skill_instance
        )
        
        # 驗證結果
        self.assertEqual(len(log_entries), 1)
        self.assertEqual(log_entries[0].event_type, "HEALING")
        self.assertEqual(log_entries[0].healing_done, 64)
        self.target.heal.assert_called_once_with(64)
    
    def test_apply_mp_gain_effect(self):
        """測試MP恢復效果應用"""
        effect_def = {
            "type": "GAIN_MP",
            "value_formula": "20"
        }
        
        # 設置公式評估結果
        self.mock_formula_evaluator.evaluate.return_value = 20
        
        # 設置MP恢復
        self.target.restore_mp.return_value = 20
        
        log_entries = self.applier._apply_single_effect_to_target(
            self.caster, self.target, effect_def, self.mock_battle_context,
            [], self.mock_skill_instance
        )
        
        # 驗證結果
        self.assertEqual(len(log_entries), 1)
        self.assertEqual(log_entries[0].event_type, "MP_GAIN")
        self.assertEqual(log_entries[0].mp_consumed, -20)  # 負值表示恢復
        self.target.restore_mp.assert_called_once_with(20)
    
    def test_apply_mp_loss_effect(self):
        """測試MP消耗效果應用"""
        effect_def = {
            "type": "LOSE_MP",
            "value_formula": "15"
        }
        
        # 設置公式評估結果
        self.mock_formula_evaluator.evaluate.return_value = 15
        
        # 設置目標當前MP
        self.target.current_mp = 30
        
        log_entries = self.applier._apply_single_effect_to_target(
            self.caster, self.target, effect_def, self.mock_battle_context,
            [], self.mock_skill_instance
        )
        
        # 驗證結果
        self.assertEqual(len(log_entries), 1)
        self.assertEqual(log_entries[0].event_type, "MP_LOSS")
        self.assertEqual(log_entries[0].mp_consumed, 15)
        self.assertEqual(self.target.current_mp, 15)  # 30 - 15
    
    def test_effect_condition_check_pass(self):
        """測試效果條件檢查通過"""
        effect_def = {
            "type": "HEAL",
            "value_formula": "50",
            "conditions_to_apply": [
                {"formula": "target_current_hp_percent < 0.9"}
            ]
        }
        
        # 設置條件評估結果為True
        self.mock_formula_evaluator.evaluate.side_effect = [True, 50]
        self.target.heal.return_value = 50
        
        log_entries = self.applier._apply_single_effect_to_target(
            self.caster, self.target, effect_def, self.mock_battle_context,
            [], self.mock_skill_instance
        )
        
        # 條件通過，應該有日誌
        self.assertEqual(len(log_entries), 1)
        self.target.heal.assert_called_once()
    
    def test_effect_condition_check_fail(self):
        """測試效果條件檢查失敗"""
        effect_def = {
            "type": "HEAL",
            "value_formula": "50",
            "conditions_to_apply": [
                {"formula": "target_current_hp_percent < 0.5"}
            ]
        }
        
        # 設置條件評估結果為False
        self.mock_formula_evaluator.evaluate.return_value = False
        
        log_entries = self.applier._apply_single_effect_to_target(
            self.caster, self.target, effect_def, self.mock_battle_context,
            [], self.mock_skill_instance
        )
        
        # 條件不通過，應該沒有日誌
        self.assertEqual(len(log_entries), 0)
        self.target.heal.assert_not_called()
    
    def test_damage_calculation_with_crit(self):
        """測試暴擊傷害計算"""
        effect_def = {
            "type": "DAMAGE",
            "value_formula": "100",
            "damage_type": "PHYSICAL"
        }
        
        self.mock_formula_evaluator.evaluate.return_value = 100
        self.target.take_damage.return_value = 120  # 暴擊後的傷害
        
        with patch('random.random', return_value=0.05):  # 暴擊（< 0.1）
            log_entries = self.applier._apply_single_effect_to_target(
                self.caster, self.target, effect_def, self.mock_battle_context,
                [], self.mock_skill_instance
            )
        
        # 驗證暴擊標記
        self.assertEqual(len(log_entries), 1)
        self.assertTrue(log_entries[0].additional_data["was_crit"])
    
    def test_damage_miss(self):
        """測試攻擊未命中"""
        effect_def = {
            "type": "DAMAGE",
            "value_formula": "100",
            "damage_type": "PHYSICAL"
        }
        
        self.mock_formula_evaluator.evaluate.return_value = 100
        
        # 模擬未命中（命中率95% - 閃避率10% = 85%，隨機數90%未命中）
        with patch('random.random', side_effect=[0.5, 0.9]):  # 不暴擊，未命中
            log_entries = self.applier._apply_single_effect_to_target(
                self.caster, self.target, effect_def, self.mock_battle_context,
                [], self.mock_skill_instance
            )
        
        # 驗證未命中
        self.assertEqual(len(log_entries), 1)
        self.assertEqual(log_entries[0].damage_dealt, 0)
        self.assertTrue(log_entries[0].additional_data["was_miss"])
        self.target.take_damage.assert_not_called()
    
    def test_defense_reduction_calculation(self):
        """測試防禦減免計算"""
        # 物理傷害
        physical_reduction = self.applier._calculate_defense_reduction(
            self.target.get_current_stats(), "PHYSICAL"
        )
        expected_physical = 30 / (30 + 100)  # pdef / (pdef + 100)
        self.assertAlmostEqual(physical_reduction, expected_physical, places=3)
        
        # 魔法傷害
        magical_reduction = self.applier._calculate_defense_reduction(
            self.target.get_current_stats(), "MAGICAL"
        )
        expected_magical = 20 / (20 + 100)  # mdef / (mdef + 100)
        self.assertAlmostEqual(magical_reduction, expected_magical, places=3)
        
        # 其他傷害類型
        other_reduction = self.applier._calculate_defense_reduction(
            self.target.get_current_stats(), "TRUE"
        )
        self.assertEqual(other_reduction, 0.0)
    
    def test_apply_skill_effects(self):
        """測試技能效果應用"""
        # 設置技能定義
        skill_definition = {
            "effect_definitions": [
                {
                    "type": "DAMAGE",
                    "value_formula": "100",
                    "damage_type": "MAGICAL"
                }
            ],
            "tags": ["FIRE", "MAGIC"]
        }
        
        self.mock_skill_instance.get_definition.return_value = skill_definition
        self.mock_formula_evaluator.evaluate.return_value = 100
        self.target.take_damage.return_value = 80
        
        with patch('random.random', return_value=0.5):  # 不暴擊，命中
            log_entries = self.applier.apply_skill_effects(
                self.caster, [self.target], self.mock_skill_instance,
                self.mock_battle_context
            )
        
        # 驗證結果
        self.assertEqual(len(log_entries), 1)
        self.assertEqual(log_entries[0].event_type, "DAMAGE")
        self.mock_skill_instance.get_definition.assert_called_once()
    
    def test_target_override(self):
        """測試效果目標覆蓋"""
        effect_definitions = [
            {
                "type": "HEAL",
                "value_formula": "50",
                "target_override": {
                    "base_pool": "ALLIES"
                }
            }
        ]

        ally = Mock()
        ally.combatant_id = "ally_001"
        ally.heal.return_value = 50
        ally.get_hp_percent.return_value = 0.7
        ally.get_mp_percent.return_value = 0.5
        ally.rpg_level = 10
        ally.star_level = 2
        ally.current_hp = 70
        ally.current_mp = 25

        # 設置ally的屬性mock
        ally_stats = Mock()
        ally_stats.patk = 60
        ally_stats.pdef = 30
        ally_stats.matk = 40
        ally_stats.mdef = 20
        ally_stats.spd = 50
        ally_stats.crit_rate = 0.05
        ally_stats.crit_dmg_multiplier = 1.2
        ally_stats.accuracy = 0.9
        ally_stats.evasion = 0.1
        ally.get_current_stats.return_value = ally_stats

        # 設置目標選擇器返回友方目標
        self.mock_target_selector.select_targets.return_value = [ally]
        self.mock_formula_evaluator.evaluate.return_value = 50

        # 使用apply_effect_definitions來測試目標覆蓋
        log_entries = self.applier.apply_effect_definitions(
            self.caster, [self.target], effect_definitions,
            self.mock_battle_context, [], self.mock_skill_instance
        )

        # 驗證目標選擇器被調用
        self.mock_target_selector.select_targets.assert_called_once()
        self.assertEqual(len(log_entries), 1)
        ally.heal.assert_called_once_with(50)
    
    def test_unknown_effect_type(self):
        """測試未知效果類型"""
        effect_def = {
            "type": "UNKNOWN_EFFECT",
            "value_formula": "100"
        }
        
        log_entries = self.applier._apply_single_effect_to_target(
            self.caster, self.target, effect_def, self.mock_battle_context,
            [], self.mock_skill_instance
        )
        
        # 未知效果類型應該不產生日誌
        self.assertEqual(len(log_entries), 0)


if __name__ == '__main__':
    unittest.main()
