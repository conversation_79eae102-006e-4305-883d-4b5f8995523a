"""
Floors Pydantic Models
Based on RPG_02_Configuration_Files.md
"""
from typing import List, Dict
from pydantic import BaseModel, Field

# -------------------- FloorConfig Model --------------------
class FloorConfig(BaseModel):
    """樓層配置"""
    name: str
    wins_required_to_advance: int = Field(..., ge=1)
    entry_cost_oil: int = Field(..., ge=0)
    first_clear_rewards_key: str
    repeatable_rewards_per_win_key: str
    possible_encounters: List[str]  # 指向 monster_groups.json 的鍵

# -------------------- Main Model --------------------
AllFloorsConfig = Dict[str, FloorConfig]  # 鍵為 floor_number 字符串
