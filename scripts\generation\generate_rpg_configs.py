"""
RPG 配置數據生成腳本

根據 Pydantic 模型生成初始的 RPG 配置 JSON 文件
"""
import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, Any, Type, List
from pydantic import BaseModel, ValidationError

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 導入 Pydantic 模型
from rpg_system.config.pydantic_models.effect_templates_models import AllEffectTemplatesConfig
from rpg_system.config.pydantic_models.status_effects_models import AllStatusEffectsConfig
from rpg_system.config.pydantic_models.active_skills_models import AllActiveSkillsConfig, ActiveSkillConfig, ActiveSkillEffectLevel, EffectDefinition
from rpg_system.config.pydantic_models.passive_skills_models import AllPassiveSkillsConfig
from rpg_system.config.pydantic_models.innate_passive_skills_models import AllInnatePassiveSkillsConfig
from rpg_system.config.pydantic_models.cards_models import AllCardsConfig
from rpg_system.config.pydantic_models.star_level_effects_models import AllStarLevelEffectsConfig
from rpg_system.config.pydantic_models.monsters_models import AllMonstersConfig
from rpg_system.config.pydantic_models.reward_packages_models import AllRewardPackagesConfig
from rpg_system.config.pydantic_models.monster_groups_models import AllMonsterGroupsConfig
from rpg_system.config.pydantic_models.floors_models import AllFloorsConfig


def save_config_to_json(
    data: Dict[str, Any], 
    model_class: Type[BaseModel], 
    file_path: str,
    validate: bool = True
) -> bool:
    """
    保存配置數據到 JSON 文件
    
    Args:
        data: 要保存的數據字典
        model_class: 對應的 Pydantic 模型類
        file_path: 輸出文件路徑
        validate: 是否進行 Pydantic 驗證
        
    Returns:
        保存是否成功
    """
    try:
        # 確保輸出目錄存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        if validate:
            # 驗證數據結構
            if hasattr(model_class, 'model_validate'):
                # Pydantic v2
                validated_data = model_class.model_validate(data)
                json_str = validated_data.model_dump_json(indent=2)
            else:
                # Pydantic v1
                validated_data = model_class(**data)
                json_str = validated_data.json(indent=2)
        else:
            # 直接序列化，不驗證
            json_str = json.dumps(data, indent=2, ensure_ascii=False)
        
        # 寫入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(json_str)
        
        print(f"✅ 成功生成: {file_path}")
        return True
        
    except ValidationError as e:
        print(f"❌ 驗證失敗 {file_path}: {e}")
        return False
    except Exception as e:
        print(f"❌ 保存失敗 {file_path}: {e}")
        return False


def generate_effect_templates_config(output_dir: str) -> bool:
    """生成效果模板配置"""
    print("🔧 生成效果模板配置...")
    
    effect_templates = {
        "PHYSICAL_DAMAGE_100": {
            "effect_type": "DAMAGE",
            "damage_type": "PHYSICAL",
            "base_power_multiplier": 1.0,
            "flat_damage_add": 100.0
        },
        "PHYSICAL_DAMAGE_150": {
            "effect_type": "DAMAGE",
            "damage_type": "PHYSICAL",
            "base_power_multiplier": 1.0,
            "flat_damage_add": 150.0
        },
        "MAGICAL_DAMAGE_120": {
            "effect_type": "DAMAGE",
            "damage_type": "MAGICAL",
            "base_power_multiplier": 1.0,
            "flat_damage_add": 120.0
        },
        "HEAL_50": {
            "effect_type": "HEAL",
            "heal_type": "HP",
            "value": 50.0
        },
        "HEAL_100": {
            "effect_type": "HEAL",
            "heal_type": "HP",
            "value": 100.0
        },
        "APPLY_BURN_3_TURNS": {
            "effect_type": "APPLY_STATUS_EFFECT",
            "status_effect_id": "BURN",
            "duration_turns": 3,
            "chance": 1.0
        },
        "APPLY_POISON_2_TURNS": {
            "effect_type": "APPLY_STATUS_EFFECT",
            "status_effect_id": "POISON",
            "duration_turns": 2,
            "chance": 1.0
        },
        "APPLY_STUN_1_TURN": {
            "effect_type": "APPLY_STATUS_EFFECT",
            "status_effect_id": "STUN",
            "duration_turns": 1,
            "chance": 0.3
        }
    }
    
    file_path = os.path.join(output_dir, "effect_templates.json")
    return save_config_to_json(effect_templates, AllEffectTemplatesConfig, file_path, validate=False)


def generate_status_effects_config(output_dir: str) -> bool:
    """生成狀態效果配置"""
    print("🔧 生成狀態效果配置...")
    
    status_effects = {
        "BURN": {
            "name": "燃燒",
            "description": "每回合造成火焰傷害",
            "is_debuff": True,
            "can_stack": True,
            "max_stacks": 5,
            "per_tick_effects": [
                {
                    "effect_type": "DAMAGE",
                    "damage_type": "FIRE",
                    "flat_damage_add": 20.0
                }
            ]
        },
        "POISON": {
            "name": "中毒",
            "description": "每回合造成毒素傷害",
            "is_debuff": True,
            "can_stack": True,
            "max_stacks": 3,
            "per_tick_effects": [
                {
                    "effect_type": "DAMAGE",
                    "damage_type": "POISON",
                    "flat_damage_add": 15.0
                }
            ]
        },
        "STUN": {
            "name": "暈眩",
            "description": "無法行動",
            "is_debuff": True,
            "can_stack": False,
            "prevents_action": True
        },
        "REGEN": {
            "name": "回復",
            "description": "每回合恢復生命值",
            "is_debuff": False,
            "can_stack": True,
            "max_stacks": 3,
            "per_tick_effects": [
                {
                    "effect_type": "HEAL",
                    "heal_type": "HP",
                    "value": 25.0
                }
            ]
        },
        "ATTACK_UP": {
            "name": "攻擊提升",
            "description": "提升攻擊力",
            "is_debuff": False,
            "can_stack": True,
            "max_stacks": 3,
            "stat_modifications": [
                {
                    "stat_name": "attack",
                    "modification_type": "PERCENTAGE_ADD",
                    "value_formula": "0.2"
                }
            ]
        },
        "DEFENSE_UP": {
            "name": "防禦提升",
            "description": "提升防禦力",
            "is_debuff": False,
            "can_stack": True,
            "max_stacks": 3,
            "stat_modifications": [
                {
                    "stat_name": "defense",
                    "modification_type": "PERCENTAGE_ADD",
                    "value_formula": "0.2"
                }
            ]
        }
    }
    
    file_path = os.path.join(output_dir, "status_effects.json")
    return save_config_to_json(status_effects, AllStatusEffectsConfig, file_path, validate=False)


def generate_active_skills_config(output_dir: str) -> bool:
    """生成主動技能配置"""
    print("🔧 生成主動技能配置...")
    
    active_skills = {
        "BASIC_ATTACK": {
            "name": "普通攻擊",
            "description_template": "對目標造成物理傷害",
            "skill_rarity": 1,
            "max_level": 5,
            "target_type": "SINGLE_ENEMY",
            "effects_by_level": {
                "1": {
                    "mp_cost": 0,
                    "cooldown_turns": 0,
                    "effect_definitions": [
                        {
                            "effect_type": "DAMAGE",
                            "damage_type": "PHYSICAL",
                            "base_power_multiplier": 1.0,
                            "flat_damage_add": 50.0
                        }
                    ]
                },
                "2": {
                    "mp_cost": 0,
                    "cooldown_turns": 0,
                    "effect_definitions": [
                        {
                            "effect_type": "DAMAGE",
                            "damage_type": "PHYSICAL",
                            "base_power_multiplier": 1.0,
                            "flat_damage_add": 60.0
                        }
                    ]
                },
                "3": {
                    "mp_cost": 0,
                    "cooldown_turns": 0,
                    "effect_definitions": [
                        {
                            "effect_type": "DAMAGE",
                            "damage_type": "PHYSICAL",
                            "base_power_multiplier": 1.0,
                            "flat_damage_add": 70.0
                        }
                    ]
                },
                "4": {
                    "mp_cost": 0,
                    "cooldown_turns": 0,
                    "effect_definitions": [
                        {
                            "effect_type": "DAMAGE",
                            "damage_type": "PHYSICAL",
                            "base_power_multiplier": 1.0,
                            "flat_damage_add": 80.0
                        }
                    ]
                },
                "5": {
                    "mp_cost": 0,
                    "cooldown_turns": 0,
                    "effect_definitions": [
                        {
                            "effect_type": "DAMAGE",
                            "damage_type": "PHYSICAL",
                            "base_power_multiplier": 1.0,
                            "flat_damage_add": 100.0
                        }
                    ]
                }
            }
        }
    }
    
    # 添加更多技能
    active_skills.update({
        "POWER_STRIKE": {
            "name": "強力一擊",
            "description_template": "消耗MP對目標造成大量物理傷害",
            "skill_rarity": 2,
            "max_level": 3,
            "target_type": "SINGLE_ENEMY",
            "effects_by_level": {
                "1": {
                    "mp_cost": 15,
                    "cooldown_turns": 2,
                    "effect_definitions": [
                        {
                            "effect_type": "DAMAGE",
                            "damage_type": "PHYSICAL",
                            "base_power_multiplier": 1.5,
                            "flat_damage_add": 80.0
                        }
                    ]
                },
                "2": {
                    "mp_cost": 15,
                    "cooldown_turns": 2,
                    "effect_definitions": [
                        {
                            "effect_type": "DAMAGE",
                            "damage_type": "PHYSICAL",
                            "base_power_multiplier": 1.6,
                            "flat_damage_add": 100.0
                        }
                    ]
                },
                "3": {
                    "mp_cost": 15,
                    "cooldown_turns": 2,
                    "effect_definitions": [
                        {
                            "effect_type": "DAMAGE",
                            "damage_type": "PHYSICAL",
                            "base_power_multiplier": 1.8,
                            "flat_damage_add": 120.0
                        }
                    ]
                }
            }
        },
        "HEAL": {
            "name": "治療術",
            "description_template": "恢復目標的生命值",
            "skill_rarity": 2,
            "max_level": 3,
            "target_type": "SINGLE_ALLY",
            "effects_by_level": {
                "1": {
                    "mp_cost": 10,
                    "cooldown_turns": 0,
                    "effect_definitions": [
                        {
                            "effect_type": "HEAL",
                            "heal_type": "HP",
                            "value": 80.0
                        }
                    ]
                },
                "2": {
                    "mp_cost": 10,
                    "cooldown_turns": 0,
                    "effect_definitions": [
                        {
                            "effect_type": "HEAL",
                            "heal_type": "HP",
                            "value": 120.0
                        }
                    ]
                },
                "3": {
                    "mp_cost": 10,
                    "cooldown_turns": 0,
                    "effect_definitions": [
                        {
                            "effect_type": "HEAL",
                            "heal_type": "HP",
                            "value": 160.0
                        }
                    ]
                }
            }
        },
        "FIREBALL": {
            "name": "火球術",
            "description_template": "發射火球對目標造成魔法傷害，有機會造成燃燒",
            "skill_rarity": 3,
            "max_level": 3,
            "target_type": "SINGLE_ENEMY",
            "effects_by_level": {
                "1": {
                    "mp_cost": 20,
                    "cooldown_turns": 1,
                    "effect_definitions": [
                        {
                            "effect_type": "DAMAGE",
                            "damage_type": "MAGICAL",
                            "base_power_multiplier": 1.2,
                            "flat_damage_add": 90.0
                        },
                        {
                            "effect_type": "APPLY_STATUS_EFFECT",
                            "status_effect_id": "BURN",
                            "duration_turns": 2,
                            "chance": 0.3
                        }
                    ]
                },
                "2": {
                    "mp_cost": 20,
                    "cooldown_turns": 1,
                    "effect_definitions": [
                        {
                            "effect_type": "DAMAGE",
                            "damage_type": "MAGICAL",
                            "base_power_multiplier": 1.3,
                            "flat_damage_add": 110.0
                        },
                        {
                            "effect_type": "APPLY_STATUS_EFFECT",
                            "status_effect_id": "BURN",
                            "duration_turns": 3,
                            "chance": 0.4
                        }
                    ]
                },
                "3": {
                    "mp_cost": 20,
                    "cooldown_turns": 1,
                    "effect_definitions": [
                        {
                            "effect_type": "DAMAGE",
                            "damage_type": "MAGICAL",
                            "base_power_multiplier": 1.5,
                            "flat_damage_add": 130.0
                        },
                        {
                            "effect_type": "APPLY_STATUS_EFFECT",
                            "status_effect_id": "BURN",
                            "duration_turns": 3,
                            "chance": 0.5
                        }
                    ]
                }
            }
        }
    })

    file_path = os.path.join(output_dir, "active_skills.json")
    return save_config_to_json(active_skills, AllActiveSkillsConfig, file_path, validate=False)


def generate_passive_skills_config(output_dir: str) -> bool:
    """生成被動技能配置"""
    print("🔧 生成被動技能配置...")

    passive_skills = {
        "ATTACK_BOOST_I": {
            "name": "攻擊強化 I",
            "description_template": "提升基礎攻擊力",
            "skill_rarity": 2,
            "max_level": 3,
            "effects_by_level": {
                "1": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "attack",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.1"
                                }
                            ]
                        }
                    ]
                },
                "2": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "attack",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.15"
                                }
                            ]
                        }
                    ]
                },
                "3": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "attack",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.2"
                                }
                            ]
                        }
                    ]
                }
            }
        },
        "HP_BOOST_I": {
            "name": "生命強化 I",
            "description_template": "提升基礎生命值",
            "skill_rarity": 2,
            "max_level": 3,
            "effects_by_level": {
                "1": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "max_hp",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.15"
                                }
                            ]
                        }
                    ]
                },
                "2": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "max_hp",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.25"
                                }
                            ]
                        }
                    ]
                },
                "3": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "max_hp",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.35"
                                }
                            ]
                        }
                    ]
                }
            }
        },
        "CRITICAL_STRIKE": {
            "name": "暴擊",
            "description_template": "提升暴擊率和暴擊傷害",
            "skill_rarity": 3,
            "max_level": 3,
            "effects_by_level": {
                "1": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "crit_rate",
                                    "modification_type": "FLAT_ADD",
                                    "value_formula": "0.05"
                                },
                                {
                                    "stat_name": "crit_damage",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.2"
                                }
                            ]
                        }
                    ]
                },
                "2": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "crit_rate",
                                    "modification_type": "FLAT_ADD",
                                    "value_formula": "0.08"
                                },
                                {
                                    "stat_name": "crit_damage",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.3"
                                }
                            ]
                        }
                    ]
                },
                "3": {
                    "trigger_condition": "ALWAYS_ACTIVE",
                    "effect_definitions": [
                        {
                            "effect_type": "STAT_MODIFICATION",
                            "modifications": [
                                {
                                    "stat_name": "crit_rate",
                                    "modification_type": "FLAT_ADD",
                                    "value_formula": "0.12"
                                },
                                {
                                    "stat_name": "crit_damage",
                                    "modification_type": "PERCENTAGE_ADD",
                                    "value_formula": "0.4"
                                }
                            ]
                        }
                    ]
                }
            }
        }
    }

    file_path = os.path.join(output_dir, "passive_skills.json")
    return save_config_to_json(passive_skills, AllPassiveSkillsConfig, file_path, validate=False)


def main():
    """主函數"""
    parser = argparse.ArgumentParser(description="生成 RPG 系統配置文件")
    parser.add_argument(
        "--output-dir",
        default="rpg_system/config/generated",
        help="輸出目錄路徑 (默認: rpg_system/config/generated)"
    )
    parser.add_argument(
        "--config-types",
        nargs="+",
        choices=[
            "effect_templates", "status_effects", "active_skills", "passive_skills",
            "innate_passive_skills", "cards", "monsters", "reward_packages",
            "monster_groups", "floors", "star_level_effects", "all"
        ],
        default=["all"],
        help="要生成的配置類型 (默認: all)"
    )
    parser.add_argument(
        "--validate",
        action="store_true",
        help="是否進行 Pydantic 驗證 (可能會因為模型複雜度而失敗)"
    )

    args = parser.parse_args()

    # 確保輸出目錄存在
    output_dir = args.output_dir
    os.makedirs(output_dir, exist_ok=True)

    print(f"🚀 開始生成 RPG 配置文件到: {output_dir}")
    print(f"📋 配置類型: {args.config_types}")
    print(f"✅ Pydantic 驗證: {'啟用' if args.validate else '禁用'}")
    print("-" * 50)

    # 導入其他部分的生成函數
    try:
        from scripts.generation.generate_rpg_configs_part2 import (
            generate_innate_passive_skills_config,
            generate_cards_config,
            generate_monsters_config
        )
        from scripts.generation.generate_rpg_configs_part3 import (
            generate_reward_packages_config,
            generate_monster_groups_config,
            generate_floors_config,
            generate_star_level_effects_config
        )
    except ImportError as e:
        print(f"❌ 導入生成函數失敗: {e}")
        return 1

    # 配置生成函數映射
    generators = {
        "effect_templates": generate_effect_templates_config,
        "status_effects": generate_status_effects_config,
        "active_skills": generate_active_skills_config,
        "passive_skills": generate_passive_skills_config,
        "innate_passive_skills": generate_innate_passive_skills_config,
        "cards": generate_cards_config,
        "monsters": generate_monsters_config,
        "reward_packages": generate_reward_packages_config,
        "monster_groups": generate_monster_groups_config,
        "floors": generate_floors_config,
        "star_level_effects": generate_star_level_effects_config
    }

    # 確定要生成的配置類型
    if "all" in args.config_types:
        config_types = list(generators.keys())
    else:
        config_types = args.config_types

    # 生成配置文件
    success_count = 0
    total_count = len(config_types)

    for config_type in config_types:
        if config_type in generators:
            try:
                success = generators[config_type](output_dir)
                if success:
                    success_count += 1
                else:
                    print(f"❌ 生成 {config_type} 失敗")
            except Exception as e:
                print(f"❌ 生成 {config_type} 時發生異常: {e}")
        else:
            print(f"⚠️ 未知的配置類型: {config_type}")

    print("-" * 50)
    print(f"🎉 生成完成！成功: {success_count}/{total_count}")

    if success_count == total_count:
        print("✅ 所有配置文件生成成功！")
        return 0
    else:
        print("⚠️ 部分配置文件生成失敗，請檢查錯誤信息")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
