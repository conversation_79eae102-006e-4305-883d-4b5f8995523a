"""
目標選擇器服務
"""
from typing import List, Dict, Any, TYPE_CHECKING
import logging
import random

if TYPE_CHECKING:
    from rpg_system.battle_system.models.combatant import Combatant
    from rpg_system.battle_system.models.battle import Battle
    from rpg_system.formula_engine.evaluator import FormulaEvaluator
    from rpg_system.config.loader import ConfigLoader

logger = logging.getLogger(__name__)


class TargetSelectorError(Exception):
    """目標選擇器異常"""
    pass


class TargetSelector:
    """
    目標選擇器服務
    
    負責根據目標邏輯配置選擇戰鬥目標
    """
    
    def __init__(self):
        """初始化目標選擇器"""
        pass
    
    def select_targets(
        self,
        caster: 'Combatant',
        target_logic_detail: Dict[str, Any],
        battle_context: 'Battle',
        formula_evaluator: 'FormulaEvaluator',
        config_loader: 'ConfigLoader'
    ) -> List['Combatant']:
        """
        選擇目標
        
        Args:
            caster: 施法者
            target_logic_detail: 目標邏輯配置
            battle_context: 戰鬥上下文
            formula_evaluator: 公式求值器
            config_loader: 配置加載器
            
        Returns:
            符合條件的目標列表
            
        Raises:
            TargetSelectorError: 當目標選擇失敗時
        """
        try:
            logger.debug(
                "選擇目標: 施法者=%s, 目標邏輯=%s",
                caster.combatant_id, target_logic_detail
            )
            
            # 步驟1: 確定基礎目標池
            potential_targets = self._get_base_target_pool(
                caster, target_logic_detail, battle_context
            )
            
            # 步驟2: 應用條件過濾
            filtered_targets = self._apply_target_conditions(
                caster, potential_targets, target_logic_detail,
                formula_evaluator, battle_context
            )
            
            # 步驟3: 應用排序邏輯
            sorted_targets = self._apply_sorting(
                filtered_targets, target_logic_detail
            )
            
            # 步驟4: 選擇數量和策略
            final_targets = self._apply_selection_strategy(
                caster, sorted_targets, target_logic_detail,
                formula_evaluator, battle_context
            )
            
            logger.debug(
                "目標選擇完成: 最終目標=%s",
                [t.combatant_id for t in final_targets]
            )
            
            return final_targets
            
        except Exception as e:
            logger.error(
                "目標選擇失敗: 施法者=%s, 錯誤=%s",
                caster.combatant_id, str(e)
            )
            raise TargetSelectorError(
                f"目標選擇失敗: {str(e)}"
            ) from e
    
    def _get_base_target_pool(
        self,
        caster: 'Combatant',
        target_logic_detail: Dict[str, Any],
        battle_context: 'Battle'
    ) -> List['Combatant']:
        """
        獲取基礎目標池
        
        Args:
            caster: 施法者
            target_logic_detail: 目標邏輯配置
            battle_context: 戰鬥上下文
            
        Returns:
            基礎目標池
        """
        base_pool = target_logic_detail.get("base_pool", "ENEMIES")
        
        if base_pool == "ENEMIES":
            # 獲取敵方存活單位
            return battle_context.get_alive_combatants(
                player_side=not caster.is_player
            )
        elif base_pool == "ALLIES":
            # 獲取友方存活單位
            return battle_context.get_alive_combatants(
                player_side=caster.is_player
            )
        elif base_pool == "SELF":
            # 只有自己
            return [caster] if caster.is_alive else []
        elif base_pool == "ALL_ALIVE":
            # 所有存活單位
            return battle_context.get_alive_combatants()
        else:
            logger.warning(f"未知的基礎目標池: {base_pool}")
            return []
    
    def _apply_target_conditions(
        self,
        caster: 'Combatant',
        potential_targets: List['Combatant'],
        target_logic_detail: Dict[str, Any],
        formula_evaluator: 'FormulaEvaluator',
        battle_context: 'Battle'
    ) -> List['Combatant']:
        """
        應用目標條件過濾
        
        Args:
            caster: 施法者
            potential_targets: 潛在目標列表
            target_logic_detail: 目標邏輯配置
            formula_evaluator: 公式求值器
            battle_context: 戰鬥上下文
            
        Returns:
            過濾後的目標列表
        """
        conditions = target_logic_detail.get("conditions", [])
        
        if not conditions:
            return potential_targets
        
        filtered_targets = []
        
        for target_candidate in potential_targets:
            passes_all_conditions = True
            
            for condition in conditions:
                try:
                    # 準備公式上下文
                    context_vars = self._prepare_formula_context(
                        caster, target_candidate, battle_context
                    )
                    
                    # 評估條件公式
                    condition_formula = condition.get("formula", "")
                    if condition_formula:
                        condition_met = formula_evaluator.evaluate(
                            condition_formula, context_vars
                        )
                        
                        if not condition_met:
                            passes_all_conditions = False
                            break
                    
                except Exception as e:
                    logger.warning(
                        "條件評估失敗: 公式=%s, 錯誤=%s",
                        condition.get("formula", ""), str(e)
                    )
                    passes_all_conditions = False
                    break
            
            if passes_all_conditions:
                filtered_targets.append(target_candidate)
        
        return filtered_targets
    
    def _apply_sorting(
        self,
        targets: List['Combatant'],
        target_logic_detail: Dict[str, Any]
    ) -> List['Combatant']:
        """
        應用排序邏輯
        
        Args:
            targets: 目標列表
            target_logic_detail: 目標邏輯配置
            
        Returns:
            排序後的目標列表
        """
        sort_by = target_logic_detail.get("sort_by")
        sort_order = target_logic_detail.get("sort_order", "ASC")
        
        if not sort_by or not targets:
            return targets
        
        try:
            reverse_sort = (sort_order == "DESC")
            
            # 定義排序鍵函數
            def get_sort_key(combatant: 'Combatant'):
                if sort_by == "current_hp":
                    return combatant.current_hp
                elif sort_by == "current_hp_percent":
                    return combatant.get_hp_percent()
                elif sort_by == "current_mp":
                    return combatant.current_mp
                elif sort_by == "current_mp_percent":
                    return combatant.get_mp_percent()
                else:
                    # 嘗試從當前屬性中獲取
                    current_stats = combatant.get_current_stats()
                    return getattr(current_stats, sort_by, 0)
            
            sorted_targets = sorted(targets, key=get_sort_key, reverse=reverse_sort)
            return sorted_targets
            
        except Exception as e:
            logger.warning(
                "排序失敗: sort_by=%s, 錯誤=%s",
                sort_by, str(e)
            )
            return targets
    
    def _apply_selection_strategy(
        self,
        caster: 'Combatant',
        sorted_targets: List['Combatant'],
        target_logic_detail: Dict[str, Any],
        formula_evaluator: 'FormulaEvaluator',
        battle_context: 'Battle'
    ) -> List['Combatant']:
        """
        應用選擇策略
        
        Args:
            caster: 施法者
            sorted_targets: 排序後的目標列表
            target_logic_detail: 目標邏輯配置
            formula_evaluator: 公式求值器
            battle_context: 戰鬥上下文
            
        Returns:
            最終選擇的目標列表
        """
        if not sorted_targets:
            return []
        
        # 確定選擇數量
        num_to_select = self._calculate_target_count(
            caster, target_logic_detail, formula_evaluator, battle_context
        )
        
        num_to_select = max(0, min(num_to_select, len(sorted_targets)))
        
        if num_to_select == 0:
            return []
        
        # 應用選擇策略
        selection_strategy = target_logic_detail.get("selection_strategy", "FIRST_N")
        
        if selection_strategy == "FIRST_N":
            return sorted_targets[:num_to_select]
        elif selection_strategy == "LAST_N":
            return sorted_targets[-num_to_select:]
        elif selection_strategy == "RANDOM_N":
            return random.sample(sorted_targets, num_to_select)
        else:
            logger.warning(f"未知的選擇策略: {selection_strategy}")
            return sorted_targets[:num_to_select]
    
    def _calculate_target_count(
        self,
        caster: 'Combatant',
        target_logic_detail: Dict[str, Any],
        formula_evaluator: 'FormulaEvaluator',
        battle_context: 'Battle'
    ) -> int:
        """
        計算目標數量

        Args:
            caster: 施法者
            target_logic_detail: 目標邏輯配置
            formula_evaluator: 公式求值器
            battle_context: 戰鬥上下文

        Returns:
            目標數量
        """
        count_logic = target_logic_detail.get("count_logic")

        if count_logic == "ALL":
            return 999999  # 表示所有目標
        elif count_logic == "FORMULA":
            count_formula = target_logic_detail.get("count_formula", "1")
            try:
                context_vars = self._prepare_formula_context(
                    caster, None, battle_context
                )
                result = formula_evaluator.evaluate(count_formula, context_vars)
                return int(result)
            except Exception as e:
                logger.warning(
                    "目標數量公式評估失敗: 公式=%s, 錯誤=%s",
                    count_formula, str(e)
                )
                return 1
        elif count_logic == "FIXED":
            # 明確指定FIXED時，使用count字段
            return target_logic_detail.get("count", 1)
        else:
            # 沒有指定count_logic時，默認選擇所有目標
            return target_logic_detail.get("count", 999999)
    
    def _prepare_formula_context(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        battle_context: 'Battle'
    ) -> Dict[str, Any]:
        """
        準備公式上下文變量
        
        Args:
            caster: 施法者
            target: 目標（可能為None）
            battle_context: 戰鬥上下文
            
        Returns:
            公式上下文字典
        """
        context_vars = {
            # 施法者屬性
            "caster_current_hp": caster.current_hp,
            "caster_current_hp_percent": caster.get_hp_percent(),
            "caster_current_mp": caster.current_mp,
            "caster_current_mp_percent": caster.get_mp_percent(),
            "caster_level": caster.rpg_level,
            "caster_star_level": caster.star_level,
            
            # 戰鬥上下文
            "current_turn": battle_context.current_turn,
            "alive_allies": len(battle_context.get_alive_combatants(
                player_side=caster.is_player
            )),
            "alive_enemies": len(battle_context.get_alive_combatants(
                player_side=not caster.is_player
            )),
        }
        
        # 添加施法者當前屬性
        caster_stats = caster.get_current_stats()
        for attr_name in ["patk", "pdef", "matk", "mdef", "spd", 
                         "crit_rate", "crit_dmg_multiplier", "accuracy", "evasion"]:
            context_vars[f"caster_stat_{attr_name}"] = getattr(caster_stats, attr_name, 0)
        
        # 如果有目標，添加目標屬性
        if target:
            context_vars.update({
                "target_current_hp": target.current_hp,
                "target_current_hp_percent": target.get_hp_percent(),
                "target_current_mp": target.current_mp,
                "target_current_mp_percent": target.get_mp_percent(),
                "target_level": target.rpg_level,
                "target_star_level": target.star_level,
            })
            
            # 添加目標當前屬性
            target_stats = target.get_current_stats()
            for attr_name in ["patk", "pdef", "matk", "mdef", "spd",
                             "crit_rate", "crit_dmg_multiplier", "accuracy", "evasion"]:
                context_vars[f"target_stat_{attr_name}"] = getattr(target_stats, attr_name, 0)
        
        return context_vars
