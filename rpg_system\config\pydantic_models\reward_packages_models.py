"""
Reward Packages Pydantic Models
Based on RPG_02_Configuration_Files.md
"""
from typing import List, Optional, Dict
from pydantic import BaseModel, Field

# -------------------- RewardItem Model --------------------
class RewardItem(BaseModel):
    """獎勵項目"""
    type: str  # e.g., "CURRENCY", "ITEM", "CARD_XP", "GLOBAL_SKILL_XP"
    id: Optional[str] = None  # e.g., currency_id, item_id, skill_id
    quantity: int = Field(..., ge=0)
    quantity_formula: Optional[str] = None

# -------------------- RewardPackageConfig Model --------------------
RewardPackageConfig = List[RewardItem]

# -------------------- Main Model --------------------
AllRewardPackagesConfig = Dict[str, RewardPackageConfig]
