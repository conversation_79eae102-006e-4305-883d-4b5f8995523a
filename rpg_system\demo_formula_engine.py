#!/usr/bin/env python3
"""
RPG公式引擎演示腳本
展示FormulaEvaluator的各種功能
"""
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

from rpg_system.formula_engine.evaluator import FormulaEvaluator, FormulaEvaluationError

def demo_basic_arithmetic():
    """演示基本算術運算"""
    print("=== 基本算術運算演示 ===")
    evaluator = FormulaEvaluator()
    
    formulas = [
        "2 + 3",
        "10 - 4", 
        "3 * 4",
        "15 / 3",
        "2 ** 3",
        "(2 + 3) * 4",
        "2 + 3 * 4 - 1"
    ]
    
    for formula in formulas:
        try:
            result = evaluator.evaluate(formula, {})
            print(f"{formula:15} = {result}")
        except FormulaEvaluationError as e:
            print(f"{formula:15} = 錯誤: {e}")
    print()

def demo_rpg_formulas():
    """演示RPG相關公式"""
    print("=== RPG公式演示 ===")
    evaluator = FormulaEvaluator()
    
    # 模擬戰鬥上下文
    context = {
        "caster_stat_patk": 100,
        "caster_stat_matk": 80,
        "caster_stat_spd": 90,
        "target_stat_pdef": 50,
        "target_stat_mdef": 40,
        "target_stat_spd": 70,
        "target_current_hp_percent": 0.3,
        "skill_level": 5,
        "star_level": 3,
        "caster_level": 25
    }
    
    rpg_formulas = [
        # 基礎傷害計算
        ("基礎物理傷害", "caster_stat_patk * (1 + skill_level * 0.1)"),
        
        # 防禦減傷
        ("防禦減傷係數", "target_stat_pdef / (target_stat_pdef + 100)"),
        
        # 暴擊機率
        ("暴擊機率", "clamp(0.05 + star_level * 0.02, 0.0, 0.95)"),
        
        # 條件傷害加成
        ("低血量加成", "if_func(target_current_hp_percent < 0.5, 0.3, 0)"),
        
        # 速度差調整
        ("速度差加成", "max(0, (caster_stat_spd - target_stat_spd) * 0.002)"),
        
        # 複雜公式：最終傷害
        ("最終傷害", """
            caster_stat_patk * (1 + skill_level * 0.1) * 
            (1 - target_stat_pdef / (target_stat_pdef + 100)) * 
            (1 + if_func(target_current_hp_percent < 0.5, 0.3, 0)) *
            (1 + max(0, (caster_stat_spd - target_stat_spd) * 0.002))
        """.replace('\n', '').replace(' ', '')),
    ]
    
    for name, formula in rpg_formulas:
        try:
            result = evaluator.evaluate(formula, context)
            print(f"{name:12}: {result:.3f}")
        except FormulaEvaluationError as e:
            print(f"{name:12}: 錯誤: {e}")
    print()

def demo_math_functions():
    """演示數學函數"""
    print("=== 數學函數演示 ===")
    evaluator = FormulaEvaluator()
    
    math_formulas = [
        "min(10, 5, 8)",
        "max(10, 5, 8)", 
        "abs(-5)",
        "floor(3.7)",
        "ceil(3.2)",
        "round(3.6)",
        "sqrt(16)",
        "pow(2, 3)",
        "clamp(15, 0, 10)",
        "clamp(-5, 0, 10)",
        "clamp(5, 0, 10)"
    ]
    
    for formula in math_formulas:
        try:
            result = evaluator.evaluate(formula, {})
            print(f"{formula:20} = {result}")
        except FormulaEvaluationError as e:
            print(f"{formula:20} = 錯誤: {e}")
    print()

def demo_conditional_logic():
    """演示條件邏輯"""
    print("=== 條件邏輯演示 ===")
    evaluator = FormulaEvaluator()
    
    context = {
        "hp_percent": 0.2,
        "level": 10,
        "is_boss": 1,
        "damage": 150
    }
    
    conditional_formulas = [
        ("血量檢查", "hp_percent < 0.3"),
        ("等級檢查", "level >= 10"),
        ("Boss檢查", "is_boss == 1"),
        ("條件加成", "if_func(hp_percent < 0.3, 0.5, 0.1)"),
        ("複合條件", "if_func(hp_percent < 0.3 and is_boss == 1, damage * 2, damage)"),
        ("多重條件", "if_func(level >= 10, if_func(is_boss == 1, 100, 50), 25)")
    ]
    
    for name, formula in conditional_formulas:
        try:
            result = evaluator.evaluate(formula, context)
            print(f"{name:12}: {result}")
        except FormulaEvaluationError as e:
            print(f"{name:12}: 錯誤: {e}")
    print()

def demo_error_handling():
    """演示錯誤處理"""
    print("=== 錯誤處理演示 ===")
    evaluator = FormulaEvaluator()
    
    error_formulas = [
        "1 / 0",  # 除零錯誤
        "sqrt(-1)",  # 負數開方
        "undefined_var + 1",  # 未定義變量
        "2 +",  # 語法錯誤
        "(2 + 3",  # 括號不匹配
        "random()",  # 不安全函數
    ]
    
    for formula in error_formulas:
        try:
            result = evaluator.evaluate(formula, {})
            print(f"{formula:20} = {result}")
        except FormulaEvaluationError as e:
            print(f"{formula:20} = 錯誤: {str(e)[:50]}...")
    print()

def main():
    """主函數"""
    print("RPG公式引擎演示")
    print("=" * 50)
    print()
    
    demo_basic_arithmetic()
    demo_math_functions()
    demo_conditional_logic()
    demo_rpg_formulas()
    demo_error_handling()
    
    print("演示完成！")

if __name__ == "__main__":
    main()
