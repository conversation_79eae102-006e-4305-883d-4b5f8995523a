"""
戰鬥事件系統定義

定義戰鬥中可能發生的各種事件類型和對應的數據結構
"""
from typing import TypedDict, Optional, List, Dict, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from rpg_system.battle_system.models.battle import Battle
    from rpg_system.battle_system.models.combatant import Combatant
    from rpg_system.battle_system.models.skill_instance import SkillInstance
    from rpg_system.battle_system.models.status_effect_instance import StatusEffectInstance

# 事件類型常量
EVENT_ON_BATTLE_START = "ON_BATTLE_START"
EVENT_ON_BATTLE_END = "ON_BATTLE_END"
EVENT_ON_TURN_START = "ON_TURN_START"
EVENT_ON_TURN_END = "ON_TURN_END"
EVENT_ON_BEFORE_SKILL_USE = "ON_BEFORE_SKILL_USE"
EVENT_ON_AFTER_SKILL_USE = "ON_AFTER_SKILL_USE"
EVENT_ON_DEAL_DAMAGE = "ON_DEAL_DAMAGE"
EVENT_ON_TAKE_DAMAGE = "ON_TAKE_DAMAGE"
EVENT_ON_HEAL_DEALT = "ON_HEAL_DEALT"
EVENT_ON_HEAL_RECEIVED = "ON_HEAL_RECEIVED"
EVENT_ON_MISS = "ON_MISS"
EVENT_ON_CRITICAL_HIT = "ON_CRITICAL_HIT"
EVENT_ON_KILL = "ON_KILL"
EVENT_ON_DEATH = "ON_DEATH"
EVENT_ON_STATUS_EFFECT_APPLIED = "ON_STATUS_EFFECT_APPLIED"
EVENT_ON_STATUS_EFFECT_REMOVED = "ON_STATUS_EFFECT_REMOVED"
EVENT_ON_STATUS_EFFECT_EXPIRED = "ON_STATUS_EFFECT_EXPIRED"
EVENT_ON_STATUS_EFFECT_TICK = "ON_STATUS_EFFECT_TICK"
EVENT_ON_ALLY_DEATH = "ON_ALLY_DEATH"
EVENT_ON_ENEMY_DEATH = "ON_ENEMY_DEATH"
EVENT_ON_COMBATANT_ADDED_TO_BATTLE = "ON_COMBATANT_ADDED_TO_BATTLE"
EVENT_ON_HP_CHANGE = "ON_HP_CHANGE"
EVENT_ON_MP_CHANGE = "ON_MP_CHANGE"

# 所有事件類型的集合
ALL_EVENT_TYPES = {
    EVENT_ON_BATTLE_START,
    EVENT_ON_BATTLE_END,
    EVENT_ON_TURN_START,
    EVENT_ON_TURN_END,
    EVENT_ON_BEFORE_SKILL_USE,
    EVENT_ON_AFTER_SKILL_USE,
    EVENT_ON_DEAL_DAMAGE,
    EVENT_ON_TAKE_DAMAGE,
    EVENT_ON_HEAL_DEALT,
    EVENT_ON_HEAL_RECEIVED,
    EVENT_ON_MISS,
    EVENT_ON_CRITICAL_HIT,
    EVENT_ON_KILL,
    EVENT_ON_DEATH,
    EVENT_ON_STATUS_EFFECT_APPLIED,
    EVENT_ON_STATUS_EFFECT_REMOVED,
    EVENT_ON_STATUS_EFFECT_EXPIRED,
    EVENT_ON_STATUS_EFFECT_TICK,
    EVENT_ON_ALLY_DEATH,
    EVENT_ON_ENEMY_DEATH,
    EVENT_ON_COMBATANT_ADDED_TO_BATTLE,
    EVENT_ON_HP_CHANGE,
    EVENT_ON_MP_CHANGE,
}


# 事件數據結構定義
class EventOnBattleStartData(TypedDict):
    """戰鬥開始事件數據"""
    battle_context: 'Battle'


class EventOnBattleEndData(TypedDict):
    """戰鬥結束事件數據"""
    battle_context: 'Battle'
    winner_side: str  # "PLAYER" or "MONSTER"
    battle_duration: float  # 戰鬥持續時間（秒）


class EventOnTurnStartData(TypedDict):
    """回合開始事件數據"""
    battle_context: 'Battle'
    current_combatant: 'Combatant'
    turn_number: int


class EventOnTurnEndData(TypedDict):
    """回合結束事件數據"""
    battle_context: 'Battle'
    current_combatant: 'Combatant'
    turn_number: int


class EventOnBeforeSkillUseData(TypedDict):
    """技能使用前事件數據"""
    battle_context: 'Battle'
    caster: 'Combatant'
    skill_instance: 'SkillInstance'
    intended_targets: List['Combatant']


class EventOnAfterSkillUseData(TypedDict):
    """技能使用後事件數據"""
    battle_context: 'Battle'
    caster: 'Combatant'
    skill_instance: 'SkillInstance'
    actual_targets: List['Combatant']
    mp_consumed: int


class EventOnDealDamageData(TypedDict):
    """造成傷害事件數據"""
    battle_context: 'Battle'
    caster: 'Combatant'
    target: 'Combatant'
    skill_instance: Optional['SkillInstance']  # None if damage from status effect tick etc.
    status_effect_instance: Optional['StatusEffectInstance']  # None if damage from skill etc.
    damage_amount: float
    damage_type: str  # "PHYSICAL", "MAGICAL", "TRUE"
    is_crit: bool
    is_miss: bool  # Should be false for ON_DEAL_DAMAGE
    source_element: Optional[str]  # 元素類型
    skill_tags: Optional[List[str]]  # 技能標籤


class EventOnTakeDamageData(TypedDict):
    """受到傷害事件數據"""
    battle_context: 'Battle'
    caster: 'Combatant'
    target: 'Combatant'
    skill_instance: Optional['SkillInstance']
    status_effect_instance: Optional['StatusEffectInstance']
    damage_amount: float
    damage_type: str
    is_crit: bool
    is_miss: bool  # Should be false for ON_TAKE_DAMAGE
    source_element: Optional[str]
    skill_tags: Optional[List[str]]


class EventOnHealDealtData(TypedDict):
    """造成治療事件數據"""
    battle_context: 'Battle'
    caster: 'Combatant'
    target: 'Combatant'
    skill_instance: Optional['SkillInstance']
    status_effect_instance: Optional['StatusEffectInstance']
    heal_amount: float
    skill_tags: Optional[List[str]]


class EventOnHealReceivedData(TypedDict):
    """受到治療事件數據"""
    battle_context: 'Battle'
    caster: 'Combatant'
    target: 'Combatant'
    skill_instance: Optional['SkillInstance']
    status_effect_instance: Optional['StatusEffectInstance']
    heal_amount: float
    skill_tags: Optional[List[str]]


class EventOnMissData(TypedDict):
    """攻擊未命中事件數據"""
    battle_context: 'Battle'
    caster: 'Combatant'
    target: 'Combatant'
    skill_instance: Optional['SkillInstance']
    damage_type: str
    skill_tags: Optional[List[str]]


class EventOnCriticalHitData(TypedDict):
    """暴擊事件數據"""
    battle_context: 'Battle'
    caster: 'Combatant'
    target: 'Combatant'
    skill_instance: Optional['SkillInstance']
    damage_amount: float
    damage_type: str
    crit_multiplier: float
    skill_tags: Optional[List[str]]


class EventOnKillData(TypedDict):
    """擊殺事件數據（給擊殺者）"""
    battle_context: 'Battle'
    killer: 'Combatant'
    victim: 'Combatant'
    skill_instance: Optional['SkillInstance']
    status_effect_instance: Optional['StatusEffectInstance']
    final_damage: float


class EventOnDeathData(TypedDict):
    """死亡事件數據（給死亡者）"""
    battle_context: 'Battle'
    killer: Optional['Combatant']  # 可能因狀態效果死亡
    victim: 'Combatant'
    skill_instance: Optional['SkillInstance']
    status_effect_instance: Optional['StatusEffectInstance']
    final_damage: float


class EventOnStatusEffectAppliedData(TypedDict):
    """狀態效果應用事件數據"""
    battle_context: 'Battle'
    caster: 'Combatant'
    target: 'Combatant'
    status_effect_instance: 'StatusEffectInstance'
    skill_instance: Optional['SkillInstance']


class EventOnStatusEffectRemovedData(TypedDict):
    """狀態效果移除事件數據"""
    battle_context: 'Battle'
    target: 'Combatant'
    status_effect_instance: 'StatusEffectInstance'
    removal_reason: str  # "DISPELLED", "EXPIRED", "REPLACED", "MANUAL"


class EventOnStatusEffectExpiredData(TypedDict):
    """狀態效果過期事件數據"""
    battle_context: 'Battle'
    target: 'Combatant'
    status_effect_instance: 'StatusEffectInstance'


class EventOnStatusEffectTickData(TypedDict):
    """狀態效果Tick事件數據"""
    battle_context: 'Battle'
    target: 'Combatant'
    status_effect_instance: 'StatusEffectInstance'
    tick_number: int


class EventOnAllyDeathData(TypedDict):
    """盟友死亡事件數據"""
    battle_context: 'Battle'
    observer: 'Combatant'  # 觀察者（盟友）
    victim: 'Combatant'    # 死亡的盟友
    killer: Optional['Combatant']


class EventOnEnemyDeathData(TypedDict):
    """敵人死亡事件數據"""
    battle_context: 'Battle'
    observer: 'Combatant'  # 觀察者（敵方）
    victim: 'Combatant'    # 死亡的敵人
    killer: Optional['Combatant']


class EventOnCombatantAddedToBattleData(TypedDict):
    """戰鬥者加入戰鬥事件數據"""
    battle_context: 'Battle'
    new_combatant: 'Combatant'
    summoner: Optional['Combatant']  # 召喚者，如果是召喚物的話


class EventOnHpChangeData(TypedDict):
    """HP變化事件數據"""
    battle_context: 'Battle'
    combatant: 'Combatant'
    old_hp: int
    new_hp: int
    change_amount: int  # 正數為增加，負數為減少
    change_source: str  # "DAMAGE", "HEAL", "STATUS_EFFECT", "OTHER"


class EventOnMpChangeData(TypedDict):
    """MP變化事件數據"""
    battle_context: 'Battle'
    combatant: 'Combatant'
    old_mp: int
    new_mp: int
    change_amount: int  # 正數為增加，負數為減少
    change_source: str  # "SKILL_COST", "RESTORE", "STATUS_EFFECT", "OTHER"


# 事件數據類型映射
EVENT_DATA_TYPES = {
    EVENT_ON_BATTLE_START: EventOnBattleStartData,
    EVENT_ON_BATTLE_END: EventOnBattleEndData,
    EVENT_ON_TURN_START: EventOnTurnStartData,
    EVENT_ON_TURN_END: EventOnTurnEndData,
    EVENT_ON_BEFORE_SKILL_USE: EventOnBeforeSkillUseData,
    EVENT_ON_AFTER_SKILL_USE: EventOnAfterSkillUseData,
    EVENT_ON_DEAL_DAMAGE: EventOnDealDamageData,
    EVENT_ON_TAKE_DAMAGE: EventOnTakeDamageData,
    EVENT_ON_HEAL_DEALT: EventOnHealDealtData,
    EVENT_ON_HEAL_RECEIVED: EventOnHealReceivedData,
    EVENT_ON_MISS: EventOnMissData,
    EVENT_ON_CRITICAL_HIT: EventOnCriticalHitData,
    EVENT_ON_KILL: EventOnKillData,
    EVENT_ON_DEATH: EventOnDeathData,
    EVENT_ON_STATUS_EFFECT_APPLIED: EventOnStatusEffectAppliedData,
    EVENT_ON_STATUS_EFFECT_REMOVED: EventOnStatusEffectRemovedData,
    EVENT_ON_STATUS_EFFECT_EXPIRED: EventOnStatusEffectExpiredData,
    EVENT_ON_STATUS_EFFECT_TICK: EventOnStatusEffectTickData,
    EVENT_ON_ALLY_DEATH: EventOnAllyDeathData,
    EVENT_ON_ENEMY_DEATH: EventOnEnemyDeathData,
    EVENT_ON_COMBATANT_ADDED_TO_BATTLE: EventOnCombatantAddedToBattleData,
    EVENT_ON_HP_CHANGE: EventOnHpChangeData,
    EVENT_ON_MP_CHANGE: EventOnMpChangeData,
}


def validate_event_data(event_type: str, event_data: Dict[str, Any]) -> bool:
    """
    驗證事件數據是否符合對應事件類型的結構
    
    Args:
        event_type: 事件類型
        event_data: 事件數據
        
    Returns:
        是否有效
    """
    if event_type not in EVENT_DATA_TYPES:
        return False
    
    expected_type = EVENT_DATA_TYPES[event_type]
    required_keys = expected_type.__annotations__.keys()
    
    # 檢查必需的鍵是否都存在
    for key in required_keys:
        if key not in event_data:
            return False
    
    return True
