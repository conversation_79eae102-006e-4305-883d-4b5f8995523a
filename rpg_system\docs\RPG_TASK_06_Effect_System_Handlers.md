# RPG 任務 06：效果系統處理器

本文檔詳細列出了 RPG 系統中效果系統核心處理器的實現任務，包括效果應用器、傷害處理器、狀態效果處理器和目標選擇器。

**參考設計文檔：**
*   `RPG_04_Domain_Model_Battle_System.md` (4. `EffectApplier`, 5. `TargetSelector`)
*   `RPG_02_Configuration_Files.md` (各種效果定義 `EffectDefinition`，狀態效果 `StatusEffectConfig`)
*   `RPG_05_Core_Runtime_Logic.md` (對這些處理器如何協作的間接提示)
*   `RPG_09_Formula_Engine_Details.md` (效果中的公式求值)
*   `RPG_10_Event_System_Details.md` (效果應用可能觸發的事件)

**目標位置：** 主要在 `rpg_system/battle_system/handlers/` 目錄下。

## 1. TargetSelector 服務

在 `rpg_system/battle_system/handlers/target_selector.py` 中定義 `TargetSelector` 類。

- [x] **定義 `TargetSelector` 類：**
    - [ ] `__init__(self)`: (可能需要 `ConfigLoader` 或 `FormulaEvaluator` 的注入，視實現細節而定)
    - [ ] **主要方法：** `select_targets(self, caster: 'Combatant', target_logic_detail: 'TargetLogicDetail', battle_context: 'Battle', formula_evaluator: 'FormulaEvaluator', config_loader: 'ConfigLoader') -> List['Combatant']`
        - [ ] **參數說明：**
            - `caster`: 施法者 `Combatant` 實例。
            - `target_logic_detail`: 來自技能配置 (`active_skills.json`) 的 `TargetLogicDetail` Pydantic 模型實例。
            - `battle_context`: `Battle` 實例，用於獲取戰場上的所有戰鬥單位。
            - `formula_evaluator`: `FormulaEvaluator` 實例，用於計算目標邏輯中的條件公式。
            - `config_loader`: `ConfigLoader` 實例。
        - [ ] **返回值：** 符合目標邏輯的 `Combatant` 實例列表。
        - [ ] **實現邏輯 (基於 `TargetLogicDetail` 的字段)：**
            - [ ] **1. 確定基礎目標池 (`base_pool`)：**
                - [ ] `if target_logic_detail.base_pool == "ENEMIES":`
                    - [ ] `potential_targets = battle_context.get_all_alive_enemies_of(caster)`
                - [ ] `elif target_logic_detail.base_pool == "ALLIES":`
                    - [ ] `potential_targets = battle_context.get_all_alive_allies_of(caster)`
                - [ ] `elif target_logic_detail.base_pool == "SELF":`
                    - [ ] `potential_targets = [caster] if caster.is_alive() else []`
                - [ ] `elif target_logic_detail.base_pool == "ALL_ALIVE":`
                    - [ ] `potential_targets = battle_context.get_all_alive_combatants()`
                - [ ] `else:` (拋出錯誤或返回空列表)
            - [ ] **2. 應用目標條件過濾 (`target_logic_detail.conditions`)：**
                - [ ] `if target_logic_detail.conditions:`
                    - [ ] `filtered_targets = []`
                    - [ ] `for target_candidate in potential_targets:`
                        - [ ] `passes_all_conditions = True`
                        - [ ] `for condition in target_logic_detail.conditions:`
                            - [ ] `context_vars = {"caster": caster, "target": target_candidate, ...}` (準備公式上下文)
                            - [ ] `condition_met = formula_evaluator.evaluate(condition.formula, context_vars)`
                            - [ ] `if not condition_met: passes_all_conditions = False; break`
                        - [ ] `if passes_all_conditions: filtered_targets.append(target_candidate)`
                    - [ ] `potential_targets = filtered_targets`
            - [ ] **3. 應用排序邏輯 (`target_logic_detail.sort_by`, `target_logic_detail.sort_order`)：**
                - [ ] `if target_logic_detail.sort_by and potential_targets:`
                    - [ ] `reverse_sort = (target_logic_detail.sort_order == "DESC")`
                    - [ ] `sort_key_attr = target_logic_detail.sort_by` (e.g., "current_hp", "patk")
                    - [ ] `potential_targets.sort(key=lambda t: t.get_stat(sort_key_attr) if hasattr(t, 'get_stat') else getattr(t, sort_key_attr, 0), reverse=reverse_sort)`
            - [ ] **4. 選擇數量 (`target_logic_detail.count_logic`, `target_logic_detail.count_formula`)：**
                - [ ] `num_to_select = 1` (默認)
                - [ ] `if target_logic_detail.count_logic == "ALL":`
                    - [ ] `num_to_select = len(potential_targets)`
                - [ ] `elif target_logic_detail.count_logic == "FORMULA":`
                    - [ ] `context_vars = {"caster": caster, ...}`
                    - [ ] `num_to_select = int(formula_evaluator.evaluate(target_logic_detail.count_formula, context_vars))`
                - [ ] `num_to_select = max(0, num_to_select)`
            - [ ] **5. 應用選擇策略 (`target_logic_detail.selection_strategy`)：**
                - [ ] `if target_logic_detail.selection_strategy == "FIRST_N":`
                    - [ ] `final_targets = potential_targets[:num_to_select]`
                - [ ] `elif target_logic_detail.selection_strategy == "LAST_N":`
                    - [ ] `final_targets = potential_targets[-num_to_select:]`
                - [ ] `elif target_logic_detail.selection_strategy == "RANDOM_N":`
                    - [ ] `final_targets = battle_context._rng.sample(potential_targets, min(num_to_select, len(potential_targets)))`
                - [ ] `else: final_targets = potential_targets[:num_to_select]` (默認為 FIRST_N)
            - [ ] 返回 `final_targets`。

## 2. DamageHandler 服務 (可選，或集成到 EffectApplier)

根據設計，傷害計算可能直接在 `EffectApplier` 中處理，或者有一個專門的 `DamageHandler`。
如果決定創建 `DamageHandler`，則在 `rpg_system/battle_system/handlers/damage_handler.py` 中定義。

- [ ] **(如果創建) 定義 `DamageHandler` 類：**
    - [ ] `__init__(self, formula_evaluator: 'FormulaEvaluator')`
    - [ ] **主要方法：** `calculate_and_apply_damage(self, caster: 'Combatant', target: 'Combatant', damage_effect: 'EffectDefinition', base_damage_value: float, battle_context: 'Battle', config_loader: 'ConfigLoader', is_crit: bool, skill_tags: List[str]) -> Dict[str, Any]`
        - [ ] **返回值：** 包含 `final_damage`, `was_crit`, `was_miss`, `elemental_advantage` 等信息的字典。
        - [ ] **實現邏輯 (簡化版，詳細公式待定)：**
            - [ ] **1. 基礎傷害值 (`base_damage_value`)**: 通常由 `EffectDefinition` 的 `value_formula` 計算得到。
            - [ ] **2. 暴擊判斷與倍率 (`is_crit` 傳入，或內部計算)：**
                - [ ] `crit_multiplier = caster.get_stat('crit_dmg_multiplier') if is_crit else 1.0`
            - [ ] **3. 屬性克制與抗性 (基於 `damage_effect.damage_type` 和 `skill_tags`)**: (此部分為高級功能，初期可簡化)
                - [ ] `elemental_advantage_multiplier = 1.0` (根據攻防雙方的屬性/標籤計算)
            - [ ] **4. 防禦減免 (基於 `damage_effect.damage_type`)**: (非常簡化的示例)
                - [ ] `if damage_effect.damage_type == "PHYSICAL": defense = target.get_stat('pdef')`
                - [ ] `elif damage_effect.damage_type == "MAGICAL": defense = target.get_stat('mdef')`
                - [ ] `else: defense = 0`
                - [ ] `damage_reduction = defense / (defense + SOME_CONSTANT)` (需要實際的傷害公式)
            - [ ] **5. 最終傷害計算：**
                - [ ] `final_damage = base_damage_value * crit_multiplier * elemental_advantage_multiplier * (1 - damage_reduction)`
            - [ ] **6. 命中/閃避判斷：**
                - [ ] `hit_chance = caster.get_stat('accuracy') - target.get_stat('evasion')`
                - [ ] `roll = battle_context._rng.random()`
                - [ ] `was_miss = roll > hit_chance`
            - [ ] **7. 應用傷害 (如果命中)：**
                - [ ] `if not was_miss: target.take_damage(final_damage, damage_effect.damage_type, is_crit, battle_context)`
            - [ ] **8. (未來) 觸發傷害相關事件** (`ON_DEAL_DAMAGE`, `ON_TAKE_DAMAGE`)
            - [ ] 返回包含計算詳情的字典。

## 3. StatusEffectHandler 服務 (可選，或集成到 EffectApplier)

狀態效果的應用和管理邏輯。如果創建，則在 `rpg_system/battle_system/handlers/status_effect_handler.py` 中定義。

- [ ] **(如果創建) 定義 `StatusEffectInstance` 數據類或 Pydantic 模型 (可能在 `models/status_effect.py`)**
    - [ ] `status_effect_id: str`
    - [ ] `config: 'StatusEffectConfig'` (或直接存儲關鍵配置值)
    - [ ] `caster_id: str`
    - [ ] `applied_turn: int`
    - [ ] `duration_remaining: Optional[int]`
    - [ ] `current_stacks: int = 1`
    - [ ] `trigger_count: int = 0` (用於 `UNTIL_TRIGGERED`)
    - [ ] `custom_vars_from_application: Dict[str, Any]` (用於效果公式的上下文)
    - [ ] `is_active: bool = True`
- [ ] **(如果創建) 定義 `StatusEffectHandler` 類：**
    - [ ] `__init__(self, effect_applier: 'EffectApplier', formula_evaluator: 'FormulaEvaluator')`
    - [ ] `apply_status_effect(self, caster: 'Combatant', target: 'Combatant', status_effect_id: str, effect_definition_params: Dict[str, Any], battle_context: 'Battle', config_loader: 'ConfigLoader')`
        - [ ] **實現邏輯：**
            - [ ] 獲取 `StatusEffectConfig`。
            - [ ] 處理疊加邏輯 (`max_stacks`, `STACKABLE_DURATION`, `NON_STACKABLE_REFRESH_DURATION` 等)。
            - [ ] 如果目標已有同 ID 效果：
                - [ ] 根據疊加規則更新現有效果 (層數、持續時間) 或忽略新效果。
            - [ ] 如果沒有或可以疊加新實例：
                - [ ] 創建 `StatusEffectInstance`。
                - [ ] `target.add_status_effect(instance, battle_context)`。
                - [ ] 如果有 `effect_definitions_on_apply`，則調用 `effect_applier.apply_effect_definitions(...)`。
            - [ ] (未來) 觸發 `ON_STATUS_EFFECT_APPLIED` 事件。
            - [ ] `target.recalculate_battle_stats_from_buffs_debuffs(config_loader)` (如果狀態效果影響屬性)
    - [ ] `tick_status_effect(self, target: 'Combatant', status_instance: 'StatusEffectInstance', battle_context: 'Battle', config_loader: 'ConfigLoader', tick_phase: Literal["START", "END"])`
        - [ ] **實現邏輯：**
            - [ ] 根據 `tick_phase` 和效果的 `tick_at_turn_start`/`tick_at_turn_end` 判斷是否 tick。
            - [ ] 如果 tick，且有 `effect_definitions_per_tick`，則調用 `effect_applier.apply_effect_definitions(...)`。
            - [ ] 更新 `duration_remaining` (如果不是 `INFINITE`)。
            - [ ] 處理過期：如果 `duration_remaining <= 0`。
    - [ ] `handle_status_effect_expiration(self, target: 'Combatant', status_instance: 'StatusEffectInstance', battle_context: 'Battle', config_loader: 'ConfigLoader')`
        - [ ] `status_instance.is_active = False`。
        - [ ] 如果有 `effect_definitions_on_expire`，則調用 `effect_applier.apply_effect_definitions(...)`。
        - [ ] `target.remove_status_effect(status_instance.status_effect_id, battle_context)`。
        - [ ] (未來) 觸發 `ON_STATUS_EFFECT_EXPIRED` / `ON_STATUS_EFFECT_REMOVED` 事件。
        - [ ] `target.recalculate_battle_stats_from_buffs_debuffs(config_loader)`。
    - [ ] `dispel_status_effect(...)` (處理驅散邏輯)

## 4. EffectApplier 服務

在 `rpg_system/battle_system/handlers/effect_applier.py` 中定義 `EffectApplier` 類。這是效果系統的核心協調者。

- [x] **定義 `EffectApplier` 類：**
    - [ ] **依賴注入：**
        - [ ] `__init__(self, formula_evaluator: 'FormulaEvaluator', target_selector: 'TargetSelector', config_loader: 'ConfigLoader', damage_handler: Optional['DamageHandler'] = None, status_effect_handler: Optional['StatusEffectHandler'] = None)`
            *   如果 `DamageHandler` 和 `StatusEffectHandler` 被集成到 `EffectApplier` 中，則不需要這些參數。
    - [ ] **主要方法：** `apply_skill_effects(self, caster: 'Combatant', targets: List['Combatant'], skill_instance: 'SkillInstance', battle_context: 'Battle')`
        - [ ] **參數：**
            - `caster`: 施法者。
            - `targets`: 由 `TargetSelector` 預先選定好的目標列表。
            - `skill_instance`: 被施放的技能實例。
            - `battle_context`: 戰鬥上下文。
        - [ ] **實現邏輯：**
            - [ ] `skill_definition = skill_instance.get_definition(self.config_loader)` (需要傳入星級，或 `SkillInstance` 內部處理)。
            - [ ] `effect_definitions = skill_definition.get('effect_definitions', [])`
            - [ ] `skill_tags = skill_definition.get('tags', [])`
            - [ ] 調用 `self.apply_effect_definitions(caster, targets, effect_definitions, battle_context, skill_tags, source_skill_instance=skill_instance, custom_vars_from_source=None)`
    - [ ] **核心輔助方法：** `apply_effect_definitions(self, caster: 'Combatant', initial_targets: List['Combatant'], effect_definitions: List['EffectDefinition'], battle_context: 'Battle', source_skill_tags: List[str], source_skill_instance: Optional['SkillInstance'] = None, custom_vars_from_source: Optional[Dict[str, Any]] = None)`
        - [ ] **實現邏輯：**
            - [ ] `for effect_def in effect_definitions:`
                - [ ] **1. 確定最終目標 (效果可能有自己的目標覆蓋邏輯)：**
                    - [ ] `if effect_def.target_override:`
                        - [ ] `actual_targets = self.target_selector.select_targets(caster, effect_def.target_override, battle_context, self.formula_evaluator, self.config_loader)`
                    - [ ] `else:`
                        - [ ] `actual_targets = initial_targets`
                - [ ] `for target in actual_targets:`
                    - [ ] **2. 準備效果公式上下文 (`context_vars`)：**
                        - [ ] 包括 `caster`, `target`, `battle_context` 中的信息，`source_skill_instance` (等級等)，`custom_vars_from_source`。
                    - [ ] **3. 檢查條件 (`effect_def.conditions_to_apply`)：**
                        - [ ] 如果有條件，使用 `formula_evaluator` 評估，不滿足則跳過此目標的此效果。
                    - [ ] **4. 處理效果類型 (`effect_def.type`)：**
                        - [ ] `if effect_def.type == "DAMAGE":`
                            - [ ] `value = self.formula_evaluator.evaluate(effect_def.value_formula, context_vars)`
                            - [ ] `is_crit = battle_context._rng.random() < caster.get_stat('crit_rate')` (或從 `effect_def` 的參數中獲取暴擊判定方式)
                            - [ ] `if self.damage_handler:` (或直接調用內部傷害邏輯)
                                - [ ] `damage_result = self.damage_handler.calculate_and_apply_damage(caster, target, effect_def, value, battle_context, self.config_loader, is_crit, source_skill_tags)`
                            - [ ] 記錄戰鬥日誌 (`BattleLogEntry`)
                        - [ ] `elif effect_def.type == "HEAL":`
                            - [ ] `value = self.formula_evaluator.evaluate(effect_def.value_formula, context_vars)`
                            - [ ] `target.heal(value, battle_context)`
                            - [ ] 記錄戰鬥日誌
                        - [ ] `elif effect_def.type == "APPLY_STATUS_EFFECT":`
                            - [ ] `status_effect_id_to_apply = effect_def.params.get("status_effect_id")`
                            - [ ] `if self.status_effect_handler:` (或直接調用內部狀態效果邏輯)
                                - [ ] `self.status_effect_handler.apply_status_effect(caster, target, status_effect_id_to_apply, effect_def.params, battle_context, self.config_loader)`
                            - [ ] 記錄戰鬥日誌
                        - [ ] `elif effect_def.type == "STAT_MODIFICATION":` (通常由狀態效果觸發，但技能也可以直接修改短期屬性)
                            - [ ] `stat_to_modify = effect_def.params.get("stat")`
                            - [ ] `modification_type = effect_def.params.get("modification_type")` // "FLAT" or "PERCENT_CURRENT" or "PERCENT_MAX"
                            - [ ] `duration = effect_def.params.get("duration_turns", 0)` // 0 代表立即，否則可能需要臨時狀態
                            - [ ] `value = self.formula_evaluator.evaluate(effect_def.value_formula, context_vars)`
                            - [ ] ... (更新 `target.current_battle_stats`，可能需要創建一個臨時的、只持續短時間的狀態效果來承載這個修改) ...
                            - [ ] 記錄戰鬥日誌
                        - [ ] `elif effect_def.type == "DISPEL_STATUS_EFFECT":`
                            - [ ] ... (調用 `StatusEffectHandler` 或內部邏輯) ...
                            - [ ] 記錄戰鬥日誌
                        - [ ] `elif effect_def.type == "GAIN_MP" / "LOSE_MP":`
                            - [ ] ... (修改 `target.current_mp`) ...
                            - [ ] 記錄戰鬥日誌
                        - [ ] ... (其他效果類型，如 `SUMMON`, `MODIFY_COOLDOWN` 等)
                        - [ ] (未來) 觸發效果應用相關的事件 (`ON_EFFECT_APPLIED`)

## 5. 單元測試

- [x] 為 `TargetSelector` 編寫單元測試。
    - [ ] 測試不同的 `base_pool`。
    - [ ] 測試條件過濾。
    - [ ] 測試排序邏輯。
    - [ ] 測試數量選擇 (`ALL`, `FORMULA`)。
    - [ ] 測試選擇策略 (`FIRST_N`, `RANDOM_N`)。
    - [ ] 邊界情況 (無可選目標，公式錯誤等)。
- [ ] (如果創建) 為 `DamageHandler` 編寫單元測試。
    - [ ] 測試不同傷害類型 (物理、魔法)。
    - [ ] 測試暴擊計算。
    - [ ] 測試命中/閃避。
    - [ ] (如果實現) 測試屬性克制/抗性。
    - [ ] 測試防禦減免公式。
- [ ] (如果創建) 為 `StatusEffectHandler` (及 `StatusEffectInstance`) 編寫單元測試。
    - [ ] 測試狀態效果的正確應用 (`on_apply` 效果)。
    - [ ] 測試疊加邏輯 (層數、持續時間刷新、忽略)。
    - [ ] 測試狀態效果 tick (`per_tick` 效果)。
    - [ ] 測試狀態效果過期 (`on_expire` 效果)。
    - [ ] 測試驅散。
    - [ ] 測試狀態效果對戰鬥屬性的修改與恢復。
- [x] 為 `EffectApplier` 編寫集成度更高的測試。
    - [ ] Mock 依賴 (FormulaEvaluator, ConfigLoader, BattleContext, Combatants)。
    - [ ] 測試一個完整的技能施放，包含多個效果定義。
    - [ ] 測試效果的目標覆蓋邏輯。
    - [ ] 測試效果的條件應用。
    - [ ] 驗證對 `Combatant` 狀態的正確修改 (HP, MP, 狀態列表, 屬性)。
    - [ ] 驗證是否正確記錄了戰鬥日誌 (可選，或通過 Battle 類測試)。

## 6. 文檔與類型提示

- [x] 為所有創建的類和公共方法添加清晰的文檔字符串。
- [x] 使用 Python 的類型提示，並通過 `TYPE_CHECKING` 塊處理循環依賴。

---
完成所有上述任務後，請勾選並更新 `RPG_IMPLEMENTATION_PLAN.md` 中的相應條目。 