"""
技能格式化工具

提供技能相關的文本格式化功能
"""
from typing import Dict, Any, Optional, TYPE_CHECKING
import logging

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader

logger = logging.getLogger(__name__)


def format_skill_for_display(
    skill_instance: Dict[str, Any], 
    skill_config: Optional[Dict[str, Any]], 
    config_loader: 'ConfigLoader'
) -> str:
    """
    生成技能的文本描述
    
    Args:
        skill_instance: 技能實例數據
        skill_config: 技能配置數據
        config_loader: 配置加載器
        
    Returns:
        格式化的技能描述字符串
    """
    try:
        # 獲取基本信息
        skill_id = skill_instance.get('skill_id', 'unknown')
        skill_name = skill_config.get('name', skill_id) if skill_config else skill_id
        skill_level = skill_instance.get('level', 1)
        
        # 構建基本描述
        description = f"**{skill_name}** (Lv.{skill_level})"
        
        if skill_config:
            # 添加MP消耗
            mp_cost = skill_config.get('mp_cost', 0)
            if mp_cost > 0:
                description += f" | MP: {mp_cost}"
            
            # 添加冷卻時間
            cooldown = skill_config.get('cooldown', 0)
            if cooldown > 0:
                description += f" | 冷卻: {cooldown}回合"
            
            # 添加技能描述
            skill_desc = skill_config.get('description', '')
            if skill_desc:
                description += f"\n{skill_desc}"
            
            # 添加效果描述
            effects = skill_config.get('effects', [])
            if effects:
                effect_text = []
                for effect in effects:
                    effect_type = effect.get('type', 'unknown')
                    effect_value = effect.get('value', 0)
                    effect_text.append(f"{effect_type}: {effect_value}")
                
                if effect_text:
                    description += f"\n效果: {', '.join(effect_text)}"
        
        return description
        
    except Exception as e:
        logger.error(f"格式化技能顯示失敗: {e}")
        return f"**{skill_instance.get('skill_id', 'Unknown')}** (格式化錯誤)"


def format_skill_list_for_embed(
    skills: list, 
    config_loader: 'ConfigLoader',
    max_length: int = 1024
) -> str:
    """
    格式化技能列表用於 Discord Embed
    
    Args:
        skills: 技能列表
        config_loader: 配置加載器
        max_length: 最大字符長度
        
    Returns:
        格式化的技能列表字符串
    """
    try:
        if not skills:
            return "無技能"
        
        skill_lines = []
        current_length = 0
        
        for skill in skills:
            skill_id = skill.get('skill_id', 'unknown')
            skill_name = skill.get('skill_name', skill_id)
            skill_level = skill.get('current_level', 1)
            
            line = f"• **{skill_name}** (Lv.{skill_level})"
            
            # 檢查長度限制
            if current_length + len(line) + 1 > max_length:
                skill_lines.append("...")
                break
            
            skill_lines.append(line)
            current_length += len(line) + 1
        
        return "\n".join(skill_lines)
        
    except Exception as e:
        logger.error(f"格式化技能列表失敗: {e}")
        return "技能列表格式化錯誤"


def format_skill_cooldown_status(skill_instance: Dict[str, Any]) -> str:
    """
    格式化技能冷卻狀態
    
    Args:
        skill_instance: 技能實例數據
        
    Returns:
        冷卻狀態字符串
    """
    try:
        current_cooldown = skill_instance.get('current_cooldown', 0)
        
        if current_cooldown <= 0:
            return "✅ 可用"
        else:
            return f"⏳ 冷卻中 ({current_cooldown}回合)"
            
    except Exception as e:
        logger.error(f"格式化技能冷卻狀態失敗: {e}")
        return "❓ 狀態未知"


def format_skill_mp_requirement(skill_config: Dict[str, Any], current_mp: int) -> str:
    """
    格式化技能MP需求狀態
    
    Args:
        skill_config: 技能配置
        current_mp: 當前MP
        
    Returns:
        MP需求狀態字符串
    """
    try:
        mp_cost = skill_config.get('mp_cost', 0)
        
        if mp_cost <= 0:
            return "無MP消耗"
        
        if current_mp >= mp_cost:
            return f"✅ MP: {mp_cost}"
        else:
            return f"❌ MP: {mp_cost} (不足)"
            
    except Exception as e:
        logger.error(f"格式化技能MP需求失敗: {e}")
        return "❓ MP需求未知"
