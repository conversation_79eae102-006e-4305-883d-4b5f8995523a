"""
Innate Passive Skills Pydantic Models
Based on RPG_02_Configuration_Files.md
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from .active_skills_models import EffectDefinition

# -------------------- TriggerCondition Models --------------------
class TriggerConditionParams(BaseModel):
    """觸發條件參數 (用於 trigger_condition 的 params)"""
    pass  # 可以根據需要添加具體參數

class AdditionalCondition(BaseModel):
    """額外條件 (用於 trigger_condition.additional_conditions)"""
    type: str
    params: Optional[Dict[str, Any]] = None

class TriggerCondition(BaseModel):
    """觸發條件"""
    type: str
    sub_type: Optional[str] = None
    chance_formula: Optional[str] = None
    trigger_once_per_battle: Optional[bool] = False
    params: Optional[TriggerConditionParams] = None
    additional_conditions: Optional[List[AdditionalCondition]] = None

# -------------------- TargetOverride Model --------------------
class TargetOverride(BaseModel):
    """目標覆蓋"""
    target_type: str
    target_logic_details: Optional[List[Dict[str, Any]]] = None

# -------------------- PassiveEffectBlock Model --------------------
class PassiveEffectBlock(BaseModel):
    """被動效果塊"""
    trigger_condition: TriggerCondition
    target_override: Optional[TargetOverride] = None
    effect_definitions: List[EffectDefinition]

# -------------------- InnatePassiveSkillConfig Model --------------------
class InnatePassiveSkillConfig(BaseModel):
    """天賦被動技能配置"""
    name: str
    description_template: Optional[str] = None
    description_template_by_star_level: Optional[Dict[str, str]] = None
    skill_rarity: int = Field(..., ge=1, le=7)
    tags: Optional[List[str]] = None
    effects_by_star_level: Dict[str, List[PassiveEffectBlock]]  # 鍵為卡牌培養星級

# -------------------- Main Model --------------------
AllInnatePassiveSkillsConfig = Dict[str, InnatePassiveSkillConfig]
