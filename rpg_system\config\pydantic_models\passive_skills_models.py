"""
Passive Skills Pydantic Models
Based on RPG_02_Configuration_Files.md
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from .active_skills_models import EffectDefinition

# -------------------- PassiveEffectBlock Model --------------------
class PassiveEffectBlock(BaseModel):
    """被動效果塊模型 (類似 innate_passive_skills_models.py 的 PassiveEffectBlock，但公式變量使用 skill_level)"""
    trigger_condition: Dict[str, Any]  # 觸發條件，結構參考 innate_passive_skills_models.py
    target_override: Optional[Dict[str, Any]] = None  # 目標覆蓋，參考 innate_passive_skills_models.py
    effect_definitions: List[EffectDefinition]  # 使用 active_skills_models.EffectDefinition

# -------------------- PassiveSkillConfig Model --------------------
class PassiveSkillConfig(BaseModel):
    """被動技能配置"""
    name: str
    description_template: str
    description_template_by_level: Optional[Dict[str, str]] = None
    skill_rarity: int = Field(..., ge=1, le=7)
    max_level: int = Field(..., ge=1)
    xp_gain_on_sacrifice: Optional[int] = Field(None, ge=0)
    xp_to_next_level_config: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    effects_by_level: Dict[str, List[PassiveEffectBlock]]  # 鍵為全局技能等級
    trigger_priority: Optional[int] = 0  # 新增，參考 RPG_07_PassiveTriggerHandler.md

# -------------------- Main Model --------------------
AllPassiveSkillsConfig = Dict[str, PassiveSkillConfig]
