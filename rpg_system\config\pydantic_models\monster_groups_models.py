"""
Monster Groups Pydantic Models
Based on RPG_02_Configuration_Files.md
"""
from typing import List, Optional, Dict
from pydantic import BaseModel

# -------------------- MonsterInGroup Model --------------------
class MonsterInGroup(BaseModel):
    """群組中的怪物"""
    monster_id: str
    position: Optional[int] = None

# -------------------- MonsterGroupConfig Model --------------------
MonsterGroupConfig = List[MonsterInGroup]

# -------------------- Main Model --------------------
AllMonsterGroupsConfig = Dict[str, MonsterGroupConfig]
