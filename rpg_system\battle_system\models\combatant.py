"""
戰鬥者模型
"""
from typing import Dict, List, Optional, Any, TYPE_CHECKING
import logging
from dataclasses import dataclass, field

from .enums import SkillType
from .skill_instance import SkillInstance

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader

logger = logging.getLogger(__name__)


@dataclass
class CombatantStats:
    """戰鬥者屬性"""
    hp: int
    max_mp: int
    mp_regen_per_turn: int
    patk: int
    pdef: int
    matk: int
    mdef: int
    spd: int
    crit_rate: float
    crit_dmg_multiplier: float
    accuracy: float
    evasion: float


class Combatant:
    """
    戰鬥者類
    
    表示戰鬥中的一個參與者（玩家卡牌或怪物）
    """
    
    def __init__(self, combatant_id: str, name: str, stats: CombatantStats,
                 is_player: bool = True, card_id: Optional[str] = None,
                 star_level: int = 0, rpg_level: int = 1, definition_id: Optional[str] = None):
        """
        初始化戰鬥者

        Args:
            combatant_id: 戰鬥者唯一ID
            name: 戰鬥者名稱
            stats: 戰鬥者屬性
            is_player: 是否為玩家方
            card_id: 卡牌ID（玩家方）
            star_level: 星級
            rpg_level: RPG等級
            definition_id: 定義ID（卡牌ID或怪物ID）
        """
        self.combatant_id = combatant_id
        self.instance_id = combatant_id  # 兼容性別名
        self.name = name
        self.is_player = is_player
        self.is_player_side = is_player  # 兼容性別名
        self.card_id = card_id
        self.definition_id = definition_id or card_id  # 用於配置查找
        self.star_level = star_level
        self.rpg_level = rpg_level

        # 屬性
        self.base_stats = stats
        self.current_hp = stats.hp
        self.current_mp = 0  # 戰鬥開始時MP為0

        # 技能
        self.primary_attack: Optional[SkillInstance] = None
        self.active_skills: List[SkillInstance] = []
        self.passive_skills: List[SkillInstance] = []
        self.innate_passive: Optional[SkillInstance] = None

        # 技能使用順序偏好（AI用）
        self.skill_order_preference: List[str] = []

        # 狀態效果
        self.status_effects: Dict[str, Any] = {}

        # 戰鬥狀態
        self._is_alive = True
        self.position = 0  # 在隊伍中的位置
    
    def get_current_stats(self) -> CombatantStats:
        """
        獲取當前實際屬性（包含狀態效果修正）
        
        Returns:
            當前實際屬性
        """
        # TODO: 這裡需要考慮狀態效果的修正
        # 目前先返回基礎屬性
        return CombatantStats(
            hp=self.base_stats.hp,
            max_mp=self.base_stats.max_mp,
            mp_regen_per_turn=self.base_stats.mp_regen_per_turn,
            patk=self.base_stats.patk,
            pdef=self.base_stats.pdef,
            matk=self.base_stats.matk,
            mdef=self.base_stats.mdef,
            spd=self.base_stats.spd,
            crit_rate=self.base_stats.crit_rate,
            crit_dmg_multiplier=self.base_stats.crit_dmg_multiplier,
            accuracy=self.base_stats.accuracy,
            evasion=self.base_stats.evasion
        )
    
    def get_hp_percent(self) -> float:
        """獲取當前血量百分比"""
        if self.base_stats.hp <= 0:
            return 0.0
        return self.current_hp / self.base_stats.hp
    
    def get_mp_percent(self) -> float:
        """獲取當前MP百分比"""
        if self.base_stats.max_mp <= 0:
            return 1.0
        return self.current_mp / self.base_stats.max_mp
    
    def take_damage(self, damage: int) -> int:
        """
        受到傷害
        
        Args:
            damage: 傷害值
            
        Returns:
            實際受到的傷害
        """
        if damage <= 0:
            return 0
        
        actual_damage = min(damage, self.current_hp)
        self.current_hp -= actual_damage
        
        if self.current_hp <= 0:
            self.current_hp = 0
            self._is_alive = False

        return actual_damage
    
    def heal(self, heal_amount: int) -> int:
        """
        恢復生命值
        
        Args:
            heal_amount: 治療量
            
        Returns:
            實際恢復的生命值
        """
        if heal_amount <= 0 or not self._is_alive:
            return 0
        
        max_heal = self.base_stats.hp - self.current_hp
        actual_heal = min(heal_amount, max_heal)
        self.current_hp += actual_heal
        
        return actual_heal
    
    def consume_mp(self, mp_cost: int) -> bool:
        """
        消耗MP
        
        Args:
            mp_cost: MP消耗
            
        Returns:
            是否成功消耗
        """
        if mp_cost <= 0:
            return True
        
        if self.current_mp >= mp_cost:
            self.current_mp -= mp_cost
            return True
        
        return False
    
    def restore_mp(self, mp_amount: int) -> int:
        """
        恢復MP
        
        Args:
            mp_amount: MP恢復量
            
        Returns:
            實際恢復的MP
        """
        if mp_amount <= 0:
            return 0
        
        max_restore = self.base_stats.max_mp - self.current_mp
        actual_restore = min(mp_amount, max_restore)
        self.current_mp += actual_restore
        
        return actual_restore
    
    def regenerate_mp(self):
        """每回合MP自然恢復"""
        if self._is_alive:
            self.restore_mp(self.base_stats.mp_regen_per_turn)
    
    def get_usable_active_skills(self, config_loader: 'ConfigLoader') -> List[SkillInstance]:
        """
        獲取可用的主動技能列表
        
        Args:
            config_loader: 配置加載器
            
        Returns:
            可用的主動技能列表
        """
        usable_skills = []
        
        # 檢查普攻
        if (self.primary_attack and 
            self.primary_attack.is_usable(self.current_mp, config_loader, self.star_level)):
            usable_skills.append(self.primary_attack)
        
        # 檢查主動技能
        for skill in self.active_skills:
            if skill.is_usable(self.current_mp, config_loader, self.star_level):
                usable_skills.append(skill)
        
        return usable_skills
    
    def tick_cooldowns(self):
        """減少所有技能的冷卻時間"""
        if self.primary_attack:
            self.primary_attack.tick_cooldown()
        
        for skill in self.active_skills:
            skill.tick_cooldown()
    
    def add_skill(self, skill: SkillInstance):
        """
        添加技能
        
        Args:
            skill: 技能實例
        """
        if skill.skill_type == SkillType.PRIMARY_ATTACK:
            self.primary_attack = skill
        elif skill.skill_type == SkillType.ACTIVE:
            self.active_skills.append(skill)
        elif skill.skill_type == SkillType.PASSIVE:
            self.passive_skills.append(skill)
        elif skill.skill_type == SkillType.INNATE_PASSIVE:
            self.innate_passive = skill
    
    def is_alive(self) -> bool:
        """判斷是否存活"""
        return self._is_alive and self.current_hp > 0

    def get_stat(self, stat_name: str) -> float:
        """
        獲取指定屬性值

        Args:
            stat_name: 屬性名稱

        Returns:
            屬性值
        """
        # 映射常用屬性名
        stat_mapping = {
            'max_hp': 'hp',
            'hp': 'hp',
            'max_mp': 'max_mp',
            'mp': 'max_mp',
            'patk': 'patk',
            'pdef': 'pdef',
            'matk': 'matk',
            'mdef': 'mdef',
            'spd': 'spd',
            'crit_rate': 'crit_rate',
            'crit_dmg_multiplier': 'crit_dmg_multiplier',
            'accuracy': 'accuracy',
            'evasion': 'evasion'
        }

        actual_stat_name = stat_mapping.get(stat_name, stat_name)
        return getattr(self.base_stats, actual_stat_name, 0.0)

    def get_skill_instance(self, skill_id: str) -> Optional[SkillInstance]:
        """
        根據技能ID獲取技能實例

        Args:
            skill_id: 技能ID

        Returns:
            技能實例，如果不存在則返回None
        """
        # 檢查普攻
        if self.primary_attack and self.primary_attack.skill_id == skill_id:
            return self.primary_attack

        # 檢查主動技能
        for skill in self.active_skills:
            if skill.skill_id == skill_id:
                return skill

        # 檢查被動技能
        for skill in self.passive_skills:
            if skill.skill_id == skill_id:
                return skill

        # 檢查天賦被動
        if self.innate_passive and self.innate_passive.skill_id == skill_id:
            return self.innate_passive

        return None

    def apply_turn_start_effects(self, battle_context, config_loader: 'ConfigLoader'):
        """
        應用回合開始效果

        Args:
            battle_context: 戰鬥上下文
            config_loader: 配置加載器
        """
        try:
            # 減少技能冷卻
            self.tick_cooldowns()

            # 應用狀態效果tick（回合開始）
            self.tick_status_effects(battle_context, config_loader, "START")

            # MP自然恢復
            self.regenerate_mp()

        except Exception as e:
            logger.error(f"應用回合開始效果失敗: {self.name}, 錯誤: {e}")

    def apply_turn_end_effects(self, battle_context, config_loader: 'ConfigLoader'):
        """
        應用回合結束效果

        Args:
            battle_context: 戰鬥上下文
            config_loader: 配置加載器
        """
        try:
            # 應用狀態效果tick（回合結束）
            self.tick_status_effects(battle_context, config_loader, "END")

        except Exception as e:
            logger.error(f"應用回合結束效果失敗: {self.name}, 錯誤: {e}")

    def tick_status_effects(self, battle_context, config_loader: 'ConfigLoader', timing: str):
        """
        更新狀態效果

        Args:
            battle_context: 戰鬥上下文
            config_loader: 配置加載器
            timing: 時機（"START" 或 "END"）
        """
        # TODO: 實現狀態效果的tick邏輯
        # 這裡是存根實現
        pass

    def get_ai_action(self, battle_context, config_loader: 'ConfigLoader') -> tuple[str, List[str]]:
        """
        獲取AI行動（怪物用）

        Args:
            battle_context: 戰鬥上下文
            config_loader: 配置加載器

        Returns:
            (技能ID, 目標ID列表)
        """
        try:
            # 遍歷技能偏好順序
            for skill_id in self.skill_order_preference:
                skill_instance = self.get_skill_instance(skill_id)
                if skill_instance and skill_instance.is_usable(self.current_mp, config_loader):
                    # 簡單目標選擇：選擇第一個存活的敵人
                    enemies = battle_context.get_all_alive_enemies_of(self)
                    if enemies:
                        return skill_id, [enemies[0].instance_id]

            # 如果沒有可用技能，使用普攻
            if self.primary_attack and self.primary_attack.is_usable(self.current_mp, config_loader):
                enemies = battle_context.get_all_alive_enemies_of(self)
                if enemies:
                    return self.primary_attack.skill_id, [enemies[0].instance_id]

            # 如果連普攻都不能用，返回空行動
            return "", []

        except Exception as e:
            logger.error(f"獲取AI行動失敗: {self.name}, 錯誤: {e}")
            return "", []

    def __repr__(self) -> str:
        return (f"Combatant(id='{self.combatant_id}', name='{self.name}', "
                f"hp={self.current_hp}/{self.base_stats.hp}, "
                f"mp={self.current_mp}/{self.base_stats.max_mp}, "
                f"alive={self._is_alive})")

    def __str__(self) -> str:
        return f"{self.name} (HP: {self.current_hp}/{self.base_stats.hp})"
