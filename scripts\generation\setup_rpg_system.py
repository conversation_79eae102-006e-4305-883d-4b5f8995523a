"""
RPG 系統完整設置腳本

一鍵完成 RPG 系統的所有初始化工作
"""
import os
import sys
import asyncio
import argparse
import subprocess
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def run_command(command: list, description: str) -> bool:
    """
    運行命令並返回是否成功
    
    Args:
        command: 要執行的命令列表
        description: 命令描述
        
    Returns:
        命令是否成功執行
    """
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(
            command,
            cwd=project_root,
            capture_output=True,
            text=True,
            check=True
        )
        print(f"✅ {description} 成功")
        if result.stdout:
            print(f"輸出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失敗")
        print(f"錯誤: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ {description} 執行異常: {e}")
        return False


async def setup_rpg_system(
    database_url: str = None,
    config_output_dir: str = "rpg_system/config/generated",
    skip_database: bool = False,
    skip_configs: bool = False,
    dry_run: bool = False
) -> bool:
    """
    完整設置 RPG 系統
    
    Args:
        database_url: 數據庫連接 URL
        config_output_dir: 配置文件輸出目錄
        skip_database: 是否跳過數據庫初始化
        skip_configs: 是否跳過配置文件生成
        dry_run: 是否只是預覽而不執行
        
    Returns:
        設置是否成功
    """
    print("🚀 開始 RPG 系統完整設置...")
    print(f"📁 配置輸出目錄: {config_output_dir}")
    print(f"🗄️ 跳過數據庫: {'是' if skip_database else '否'}")
    print(f"📋 跳過配置: {'是' if skip_configs else '否'}")
    print(f"🔍 預覽模式: {'是' if dry_run else '否'}")
    print("=" * 60)
    
    success_count = 0
    total_tasks = 0
    
    # 1. 生成配置文件
    if not skip_configs:
        total_tasks += 1
        print("\n📋 步驟 1: 生成 RPG 配置文件")
        print("-" * 40)
        
        config_command = [
            sys.executable,
            "scripts/generation/generate_rpg_configs.py",
            "--output-dir", config_output_dir,
            "--config-types", "all"
        ]
        
        if dry_run:
            print(f"🔍 預覽: 將執行命令 {' '.join(config_command)}")
            success_count += 1
        else:
            if run_command(config_command, "生成 RPG 配置文件"):
                success_count += 1
    
    # 2. 初始化數據庫
    if not skip_database:
        total_tasks += 1
        print("\n🗄️ 步驟 2: 初始化 RPG 數據庫")
        print("-" * 40)
        
        db_command = [
            sys.executable,
            "scripts/generation/initialize_rpg_database.py"
        ]
        
        if database_url:
            db_command.extend(["--database-url", database_url])
        
        if dry_run:
            db_command.append("--dry-run")
        
        if dry_run:
            print(f"🔍 預覽: 將執行命令 {' '.join(db_command)}")
            success_count += 1
        else:
            if run_command(db_command, "初始化 RPG 數據庫"):
                success_count += 1
    
    # 3. 驗證設置
    if not dry_run and success_count == total_tasks:
        total_tasks += 1
        print("\n🔍 步驟 3: 驗證設置")
        print("-" * 40)
        
        # 檢查配置文件是否存在
        if not skip_configs:
            config_files = [
                "effect_templates.json",
                "status_effects.json", 
                "active_skills.json",
                "passive_skills.json",
                "cards.json",
                "monsters.json",
                "floors.json"
            ]
            
            missing_files = []
            for config_file in config_files:
                file_path = os.path.join(config_output_dir, config_file)
                if not os.path.exists(file_path):
                    missing_files.append(config_file)
            
            if missing_files:
                print(f"❌ 缺少配置文件: {', '.join(missing_files)}")
            else:
                print("✅ 所有配置文件已生成")
                success_count += 1
        else:
            success_count += 1
    
    # 總結
    print("\n" + "=" * 60)
    print(f"🎉 RPG 系統設置完成！")
    print(f"📊 成功任務: {success_count}/{total_tasks}")
    
    if success_count == total_tasks:
        print("✅ 所有設置任務成功完成！")
        print("\n🎮 RPG 系統已準備就緒，可以開始使用！")
        
        if not dry_run:
            print("\n📝 後續步驟:")
            print("1. 確保 Discord Bot 已配置 RPG Cogs")
            print("2. 重啟 Discord Bot 以加載新的配置")
            print("3. 使用 /pve_status 命令測試 RPG 功能")
        
        return True
    else:
        print("⚠️ 部分設置任務失敗，請檢查錯誤信息")
        return False


async def main():
    """主函數"""
    parser = argparse.ArgumentParser(description="一鍵設置 RPG 系統")
    parser.add_argument(
        "--database-url",
        help="數據庫連接 URL (如果不提供，將嘗試從環境變量獲取)"
    )
    parser.add_argument(
        "--config-output-dir",
        default="rpg_system/config/generated",
        help="配置文件輸出目錄 (默認: rpg_system/config/generated)"
    )
    parser.add_argument(
        "--skip-database",
        action="store_true",
        help="跳過數據庫初始化"
    )
    parser.add_argument(
        "--skip-configs",
        action="store_true",
        help="跳過配置文件生成"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="只預覽操作而不執行"
    )
    
    args = parser.parse_args()
    
    # 獲取數據庫 URL
    database_url = args.database_url
    if not database_url and not args.skip_database:
        database_url = os.getenv('DATABASE_URL')
        if not database_url and not args.dry_run:
            print("⚠️ 警告: 未提供數據庫 URL，將跳過數據庫初始化")
            print("   請設置 DATABASE_URL 環境變量或使用 --database-url 參數")
            args.skip_database = True
    
    # 執行設置
    success = await setup_rpg_system(
        database_url=database_url,
        config_output_dir=args.config_output_dir,
        skip_database=args.skip_database,
        skip_configs=args.skip_configs,
        dry_run=args.dry_run
    )
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
