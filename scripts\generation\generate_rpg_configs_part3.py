"""
RPG 配置數據生成腳本 - 第三部分

完成剩餘配置文件的生成函數
"""
import os
import sys
from pathlib import Path
from typing import Dict, Any

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from scripts.generation.generate_rpg_configs import save_config_to_json
from rpg_system.config.pydantic_models.reward_packages_models import AllRewardPackagesConfig
from rpg_system.config.pydantic_models.monster_groups_models import AllMonsterGroupsConfig
from rpg_system.config.pydantic_models.floors_models import AllFloorsConfig
from rpg_system.config.pydantic_models.star_level_effects_models import AllStarLevelEffectsConfig


def generate_reward_packages_config(output_dir: str) -> bool:
    """生成獎勵包配置"""
    print("🔧 生成獎勵包配置...")
    
    reward_packages = {
        "FLOOR_1_VICTORY": {
            "name": "第1層勝利獎勵",
            "description": "完成第1層戰鬥的獎勵",
            "rewards": [
                {
                    "type": "xp",
                    "amount": 50,
                    "chance": 1.0
                },
                {
                    "type": "gold",
                    "amount": 30,
                    "chance": 1.0
                },
                {
                    "type": "skill_xp",
                    "amount": 25,
                    "chance": 0.8,
                    "skill_id": "BASIC_ATTACK"
                }
            ]
        },
        "FLOOR_2_VICTORY": {
            "name": "第2層勝利獎勵",
            "description": "完成第2層戰鬥的獎勵",
            "rewards": [
                {
                    "type": "xp",
                    "amount": 80,
                    "chance": 1.0
                },
                {
                    "type": "gold",
                    "amount": 50,
                    "chance": 1.0
                },
                {
                    "type": "skill_xp",
                    "amount": 40,
                    "chance": 0.9,
                    "skill_id": "POWER_STRIKE"
                }
            ]
        },
        "FLOOR_3_VICTORY": {
            "name": "第3層勝利獎勵",
            "description": "完成第3層戰鬥的獎勵",
            "rewards": [
                {
                    "type": "xp",
                    "amount": 120,
                    "chance": 1.0
                },
                {
                    "type": "gold",
                    "amount": 80,
                    "chance": 1.0
                },
                {
                    "type": "skill_xp",
                    "amount": 60,
                    "chance": 1.0,
                    "skill_id": "FIREBALL"
                }
            ]
        },
        "BOSS_VICTORY": {
            "name": "BOSS勝利獎勵",
            "description": "擊敗BOSS的特殊獎勵",
            "rewards": [
                {
                    "type": "xp",
                    "amount": 300,
                    "chance": 1.0
                },
                {
                    "type": "gold",
                    "amount": 200,
                    "chance": 1.0
                },
                {
                    "type": "skill_xp",
                    "amount": 100,
                    "chance": 1.0,
                    "skill_id": "HEAL"
                },
                {
                    "type": "item",
                    "item_id": "RARE_CRYSTAL",
                    "amount": 1,
                    "chance": 0.3
                }
            ]
        }
    }
    
    file_path = os.path.join(output_dir, "reward_packages.json")
    return save_config_to_json(reward_packages, AllRewardPackagesConfig, file_path, validate=False)


def generate_monster_groups_config(output_dir: str) -> bool:
    """生成怪物群組配置"""
    print("🔧 生成怪物群組配置...")
    
    monster_groups = {
        "GOBLIN_PATROL": {
            "name": "哥布林巡邏隊",
            "description": "一小隊巡邏的哥布林",
            "monsters": [
                {
                    "monster_id": "GOBLIN",
                    "level": 1,
                    "position": 0
                },
                {
                    "monster_id": "GOBLIN",
                    "level": 1,
                    "position": 1
                }
            ],
            "formation": "STANDARD"
        },
        "ORC_SQUAD": {
            "name": "獸人小隊",
            "description": "由獸人戰士組成的小隊",
            "monsters": [
                {
                    "monster_id": "ORC_WARRIOR",
                    "level": 2,
                    "position": 0
                },
                {
                    "monster_id": "GOBLIN",
                    "level": 2,
                    "position": 1
                },
                {
                    "monster_id": "GOBLIN",
                    "level": 2,
                    "position": 2
                }
            ],
            "formation": "DEFENSIVE"
        },
        "ELEMENTAL_ENCOUNTER": {
            "name": "元素遭遇",
            "description": "火元素與其召喚的小怪",
            "monsters": [
                {
                    "monster_id": "FIRE_ELEMENTAL",
                    "level": 3,
                    "position": 0
                },
                {
                    "monster_id": "GOBLIN",
                    "level": 3,
                    "position": 1
                }
            ],
            "formation": "CASTER_PROTECTED"
        },
        "DRAGON_LAIR": {
            "name": "龍穴",
            "description": "幼龍的巢穴",
            "monsters": [
                {
                    "monster_id": "DRAGON_WHELP",
                    "level": 5,
                    "position": 0
                }
            ],
            "formation": "BOSS"
        }
    }
    
    file_path = os.path.join(output_dir, "monster_groups.json")
    return save_config_to_json(monster_groups, AllMonsterGroupsConfig, file_path, validate=False)


def generate_floors_config(output_dir: str) -> bool:
    """生成樓層配置"""
    print("🔧 生成樓層配置...")
    
    floors = {
        "1": {
            "name": "森林邊緣",
            "description": "冒險的起點，充滿了弱小的怪物",
            "floor_type": "NORMAL",
            "difficulty_level": 1,
            "unlock_requirements": [],
            "possible_monster_groups": [
                {
                    "monster_group_id": "GOBLIN_PATROL",
                    "weight": 10
                }
            ],
            "victory_reward_package_id": "FLOOR_1_VICTORY",
            "wins_required_to_advance": 3,
            "max_attempts_per_day": 10
        },
        "2": {
            "name": "深林小徑",
            "description": "更深入的森林，敵人變得更強",
            "floor_type": "NORMAL",
            "difficulty_level": 2,
            "unlock_requirements": [
                {
                    "type": "FLOOR_CLEARED",
                    "floor_id": "1",
                    "required_wins": 3
                }
            ],
            "possible_monster_groups": [
                {
                    "monster_group_id": "GOBLIN_PATROL",
                    "weight": 5
                },
                {
                    "monster_group_id": "ORC_SQUAD",
                    "weight": 8
                }
            ],
            "victory_reward_package_id": "FLOOR_2_VICTORY",
            "wins_required_to_advance": 3,
            "max_attempts_per_day": 8
        },
        "3": {
            "name": "魔法森林",
            "description": "充滿魔法能量的森林區域",
            "floor_type": "MAGICAL",
            "difficulty_level": 3,
            "unlock_requirements": [
                {
                    "type": "FLOOR_CLEARED",
                    "floor_id": "2",
                    "required_wins": 3
                }
            ],
            "possible_monster_groups": [
                {
                    "monster_group_id": "ELEMENTAL_ENCOUNTER",
                    "weight": 10
                },
                {
                    "monster_group_id": "ORC_SQUAD",
                    "weight": 3
                }
            ],
            "victory_reward_package_id": "FLOOR_3_VICTORY",
            "wins_required_to_advance": 5,
            "max_attempts_per_day": 6
        },
        "4": {
            "name": "古龍巢穴",
            "description": "傳說中的龍族居住地",
            "floor_type": "BOSS",
            "difficulty_level": 5,
            "unlock_requirements": [
                {
                    "type": "FLOOR_CLEARED",
                    "floor_id": "3",
                    "required_wins": 5
                },
                {
                    "type": "PLAYER_LEVEL",
                    "required_level": 10
                }
            ],
            "possible_monster_groups": [
                {
                    "monster_group_id": "DRAGON_LAIR",
                    "weight": 10
                }
            ],
            "victory_reward_package_id": "BOSS_VICTORY",
            "wins_required_to_advance": 1,
            "max_attempts_per_day": 3
        }
    }
    
    file_path = os.path.join(output_dir, "floors.json")
    return save_config_to_json(floors, AllFloorsConfig, file_path, validate=False)


def generate_star_level_effects_config(output_dir: str) -> bool:
    """生成星級效果配置"""
    print("🔧 生成星級效果配置...")
    
    star_level_effects = {
        "0": {
            "stat_multipliers": {
                "max_hp": 1.0,
                "max_mp": 1.0,
                "attack": 1.0,
                "defense": 1.0,
                "magic_attack": 1.0,
                "magic_defense": 1.0,
                "speed": 1.0
            },
            "additional_effects": []
        },
        "1": {
            "stat_multipliers": {
                "max_hp": 1.1,
                "max_mp": 1.1,
                "attack": 1.1,
                "defense": 1.1,
                "magic_attack": 1.1,
                "magic_defense": 1.1,
                "speed": 1.05
            },
            "additional_effects": [
                {
                    "effect_type": "STAT_MODIFICATION",
                    "modifications": [
                        {
                            "stat_name": "crit_rate",
                            "modification_type": "FLAT_ADD",
                            "value_formula": "0.02"
                        }
                    ]
                }
            ]
        },
        "2": {
            "stat_multipliers": {
                "max_hp": 1.25,
                "max_mp": 1.25,
                "attack": 1.25,
                "defense": 1.25,
                "magic_attack": 1.25,
                "magic_defense": 1.25,
                "speed": 1.1
            },
            "additional_effects": [
                {
                    "effect_type": "STAT_MODIFICATION",
                    "modifications": [
                        {
                            "stat_name": "crit_rate",
                            "modification_type": "FLAT_ADD",
                            "value_formula": "0.05"
                        },
                        {
                            "stat_name": "crit_damage",
                            "modification_type": "PERCENTAGE_ADD",
                            "value_formula": "0.1"
                        }
                    ]
                }
            ]
        },
        "3": {
            "stat_multipliers": {
                "max_hp": 1.5,
                "max_mp": 1.5,
                "attack": 1.5,
                "defense": 1.5,
                "magic_attack": 1.5,
                "magic_defense": 1.5,
                "speed": 1.2
            },
            "additional_effects": [
                {
                    "effect_type": "STAT_MODIFICATION",
                    "modifications": [
                        {
                            "stat_name": "crit_rate",
                            "modification_type": "FLAT_ADD",
                            "value_formula": "0.1"
                        },
                        {
                            "stat_name": "crit_damage",
                            "modification_type": "PERCENTAGE_ADD",
                            "value_formula": "0.25"
                        },
                        {
                            "stat_name": "accuracy",
                            "modification_type": "FLAT_ADD",
                            "value_formula": "0.05"
                        }
                    ]
                }
            ]
        }
    }
    
    file_path = os.path.join(output_dir, "star_level_effects.json")
    return save_config_to_json(star_level_effects, AllStarLevelEffectsConfig, file_path, validate=False)
