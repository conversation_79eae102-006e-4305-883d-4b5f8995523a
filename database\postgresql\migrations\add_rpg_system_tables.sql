-- RPG系統數據庫遷移腳本
-- 創建日期: 2024-12-19
-- 描述: 為RPG系統添加必要的數據庫表和字段

-- ==========================================
-- 1. 創建 gacha_user_learned_global_skills 表
-- ==========================================
CREATE TABLE IF NOT EXISTS gacha_user_learned_global_skills (
    user_id BIGINT NOT NULL,
    skill_id VARCHAR(100) NOT NULL,
    skill_type VARCHAR(10) NOT NULL CHECK (skill_type IN ('ACTIVE', 'PASSIVE')),
    skill_level INTEGER NOT NULL DEFAULT 1 CHECK (skill_level >= 1),
    skill_xp INTEGER NOT NULL DEFAULT 0 CHECK (skill_xp >= 0),
    unlocked_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 複合主鍵
    PRIMARY KEY (user_id, skill_id, skill_type),
    
    -- 外鍵約束 (假設用戶表為 gacha_users)
    FOREIGN KEY (user_id) REFERENCES gacha_users(user_id) ON DELETE CASCADE
);

-- 為查詢性能創建索引
CREATE INDEX IF NOT EXISTS idx_global_skills_user_id ON gacha_user_learned_global_skills(user_id);
CREATE INDEX IF NOT EXISTS idx_global_skills_skill_id ON gacha_user_learned_global_skills(skill_id);
CREATE INDEX IF NOT EXISTS idx_global_skills_skill_type ON gacha_user_learned_global_skills(skill_type);

-- ==========================================
-- 2. 修改 gacha_user_collections 表 - 添加RPG相關字段
-- ==========================================

-- 添加 rpg_level 字段
ALTER TABLE gacha_user_collections 
ADD COLUMN IF NOT EXISTS rpg_level INTEGER NOT NULL DEFAULT 1 CHECK (rpg_level >= 1);

-- 添加 rpg_xp 字段
ALTER TABLE gacha_user_collections 
ADD COLUMN IF NOT EXISTS rpg_xp INTEGER NOT NULL DEFAULT 0 CHECK (rpg_xp >= 0);

-- 確認 star_level 字段存在且約束正確 (應該已存在，但確保約束正確)
-- 如果字段不存在則添加，如果存在則更新約束
DO $$
BEGIN
    -- 檢查 star_level 字段是否存在
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'gacha_user_collections' 
        AND column_name = 'star_level'
    ) THEN
        -- 如果不存在則添加
        ALTER TABLE gacha_user_collections 
        ADD COLUMN star_level INTEGER NOT NULL DEFAULT 0 CHECK (star_level >= 0 AND star_level <= 35);
    ELSE
        -- 如果存在，確保約束正確 (先刪除舊約束再添加新約束)
        -- 注意：這裡假設可能需要更新約束範圍到0-35
        ALTER TABLE gacha_user_collections 
        DROP CONSTRAINT IF EXISTS gacha_user_collections_star_level_check;
        
        ALTER TABLE gacha_user_collections 
        ADD CONSTRAINT gacha_user_collections_star_level_check 
        CHECK (star_level >= 0 AND star_level <= 35);
    END IF;
END $$;

-- 添加 equipped_active_skill_ids 字段 (JSON數組)
ALTER TABLE gacha_user_collections 
ADD COLUMN IF NOT EXISTS equipped_active_skill_ids JSONB DEFAULT NULL;

-- 添加 equipped_common_passives 字段 (JSONB對象)
ALTER TABLE gacha_user_collections 
ADD COLUMN IF NOT EXISTS equipped_common_passives JSONB DEFAULT NULL;

-- 確認 is_favorite 字段存在 (應該已存在)
-- 如果不存在則添加
ALTER TABLE gacha_user_collections 
ADD COLUMN IF NOT EXISTS is_favorite BOOLEAN NOT NULL DEFAULT FALSE;

-- 移除 next_rpg_level_xp_required 字段 (如果存在)
ALTER TABLE gacha_user_collections 
DROP COLUMN IF EXISTS next_rpg_level_xp_required;

-- ==========================================
-- 3. 創建 rpg_user_progress 表
-- ==========================================
CREATE TABLE IF NOT EXISTS rpg_user_progress (
    user_id BIGINT NOT NULL PRIMARY KEY,
    current_floor_unlocked INTEGER NOT NULL DEFAULT 1 CHECK (current_floor_unlocked >= 1),
    current_floor_wins INTEGER NOT NULL DEFAULT 0 CHECK (current_floor_wins >= 0),
    max_floor_cleared INTEGER NOT NULL DEFAULT 0 CHECK (max_floor_cleared >= 0),
    current_team_formation JSONB DEFAULT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 外鍵約束
    FOREIGN KEY (user_id) REFERENCES gacha_users(user_id) ON DELETE CASCADE
);

-- 為查詢性能創建索引
CREATE INDEX IF NOT EXISTS idx_rpg_progress_floor_unlocked ON rpg_user_progress(current_floor_unlocked);
CREATE INDEX IF NOT EXISTS idx_rpg_progress_max_cleared ON rpg_user_progress(max_floor_cleared);

-- ==========================================
-- 4. 檢查並確認 gacha_master_cards 表
-- ==========================================

-- 確認 rarity 字段存在且範圍正確 (1-7)
DO $$
BEGIN
    -- 檢查並更新 rarity 字段約束
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'gacha_master_cards' 
        AND column_name = 'rarity'
    ) THEN
        -- 更新約束以確保範圍為1-7
        ALTER TABLE gacha_master_cards 
        DROP CONSTRAINT IF EXISTS gacha_master_cards_rarity_check;
        
        ALTER TABLE gacha_master_cards 
        ADD CONSTRAINT gacha_master_cards_rarity_check 
        CHECK (rarity >= 1 AND rarity <= 7);
    END IF;
END $$;

-- 確認 pool_type 字段存在 (應該已存在)
-- 移除 rpg_card_config_key 字段 (如果存在)
ALTER TABLE gacha_master_cards 
DROP COLUMN IF EXISTS rpg_card_config_key;

-- ==========================================
-- 5. 創建更新時間戳觸發器 (用於 rpg_user_progress)
-- ==========================================

-- 創建更新時間戳函數 (如果不存在)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 為 rpg_user_progress 表創建觸發器
DROP TRIGGER IF EXISTS update_rpg_user_progress_updated_at ON rpg_user_progress;
CREATE TRIGGER update_rpg_user_progress_updated_at
    BEFORE UPDATE ON rpg_user_progress
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ==========================================
-- 6. 添加註釋
-- ==========================================

-- 為新表添加註釋
COMMENT ON TABLE gacha_user_learned_global_skills IS 'RPG系統：用戶學習的全局技能等級和經驗';
COMMENT ON TABLE rpg_user_progress IS 'RPG系統：用戶在RPG模式中的進度數據';

-- 為新字段添加註釋
COMMENT ON COLUMN gacha_user_collections.rpg_level IS 'RPG系統：卡牌的RPG等級';
COMMENT ON COLUMN gacha_user_collections.rpg_xp IS 'RPG系統：卡牌當前RPG等級的經驗值';
COMMENT ON COLUMN gacha_user_collections.equipped_active_skill_ids IS 'RPG系統：裝備的主動技能ID數組';
COMMENT ON COLUMN gacha_user_collections.equipped_common_passives IS 'RPG系統：裝備的通用被動技能對象';

-- ==========================================
-- 遷移完成
-- ==========================================
