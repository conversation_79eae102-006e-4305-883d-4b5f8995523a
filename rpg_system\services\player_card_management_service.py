"""
玩家卡牌管理服務

負責玩家卡牌的技能槽位管理、被動技能裝備/卸下
"""
from typing import Dict, Any, Optional, Tuple, List, TYPE_CHECKING
import logging

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader
    from rpg_system.repositories.player_collection_rpg_repository import PlayerCollectionRPGRepository
    from rpg_system.repositories.global_skill_repository import GlobalSkillRepository

logger = logging.getLogger(__name__)


class PlayerCardManagementServiceError(Exception):
    """玩家卡牌管理服務異常"""
    pass


class PlayerCardManagementService:
    """
    玩家卡牌管理服務

    負責玩家卡牌的技能槽位管理、被動技能裝備/卸下
    """

    def __init__(
        self,
        config_loader: 'ConfigLoader',
        player_collection_repo: 'PlayerCollectionRPGRepository',
        global_skill_repo: 'GlobalSkillRepository'
    ):
        """
        初始化玩家卡牌管理服務

        Args:
            config_loader: 配置加載器
            player_collection_repo: 玩家收藏倉庫
            global_skill_repo: 全局技能倉庫
        """
        self.config_loader = config_loader
        self.player_collection_repo = player_collection_repo
        self.global_skill_repo = global_skill_repo

    async def equip_active_skill(
        self,
        user_id: int,
        collection_id: int,
        active_skill_id: str,
        slot_index: int
    ) -> Tuple[bool, str]:
        """
        裝備主動技能

        Args:
            user_id: 用戶ID
            collection_id: 收藏ID
            active_skill_id: 主動技能ID
            slot_index: 槽位索引

        Returns:
            (成功/失敗, 消息)
        """
        try:
            # 獲取玩家卡牌數據
            player_card_data = await self.player_collection_repo.get_player_card_by_collection_id(collection_id)
            if not player_card_data:
                return False, f"卡牌不存在: collection_id={collection_id}"

            if player_card_data.user_id != user_id:
                return False, "無權限操作此卡牌"

            # 獲取卡牌配置
            card_config = self.config_loader.get_card_config(player_card_data.card_id)
            if not card_config:
                return False, f"卡牌配置不存在: card_id={player_card_data.card_id}"

            # 驗證槽位索引
            max_slots = 3  # 假設最多3個主動技能槽位
            if slot_index < 0 or slot_index >= max_slots:
                return False, f"無效的槽位索引: {slot_index}"

            # 驗證玩家是否已學習該技能
            learned_skill = await self.global_skill_repo.get_learned_skill(user_id, active_skill_id, "ACTIVE")
            if not learned_skill:
                return False, f"尚未學習該技能: {active_skill_id}"

            # 更新裝備的主動技能
            equipped_skills = player_card_data.equipped_active_skill_ids.copy()

            # 確保列表長度足夠
            while len(equipped_skills) <= slot_index:
                equipped_skills.append(None)

            equipped_skills[slot_index] = active_skill_id

            # 更新數據庫
            success = await self.player_collection_repo.update_player_card_active_skills(
                collection_id, equipped_skills
            )

            if success:
                return True, f"成功裝備主動技能 {active_skill_id} 到槽位 {slot_index}"
            else:
                return False, "更新數據庫失敗"

        except Exception as e:
            logger.error(f"裝備主動技能失敗: {e}")
            return False, f"裝備主動技能失敗: {str(e)}"

    async def unequip_active_skill(
        self,
        user_id: int,
        collection_id: int,
        slot_index: int
    ) -> Tuple[bool, str]:
        """
        卸下主動技能

        Args:
            user_id: 用戶ID
            collection_id: 收藏ID
            slot_index: 槽位索引

        Returns:
            (成功/失敗, 消息)
        """
        try:
            # 獲取玩家卡牌數據
            player_card_data = await self.player_collection_repo.get_player_card_by_collection_id(collection_id)
            if not player_card_data:
                return False, f"卡牌不存在: collection_id={collection_id}"

            if player_card_data.user_id != user_id:
                return False, "無權限操作此卡牌"

            # 驗證槽位索引
            if slot_index < 0 or slot_index >= len(player_card_data.equipped_active_skill_ids):
                return False, f"無效的槽位索引: {slot_index}"

            # 更新裝備的主動技能
            equipped_skills = player_card_data.equipped_active_skill_ids.copy()
            equipped_skills[slot_index] = None

            # 更新數據庫
            success = await self.player_collection_repo.update_player_card_active_skills(
                collection_id, equipped_skills
            )

            if success:
                return True, f"成功卸下槽位 {slot_index} 的主動技能"
            else:
                return False, "更新數據庫失敗"

        except Exception as e:
            logger.error(f"卸下主動技能失敗: {e}")
            return False, f"卸下主動技能失敗: {str(e)}"

    async def equip_passive_skill(
        self,
        user_id: int,
        collection_id: int,
        passive_skill_id: str,
        passive_skill_level: int,
        slot_key: str
    ) -> Tuple[bool, str]:
        """
        裝備被動技能

        Args:
            user_id: 用戶ID
            collection_id: 收藏ID
            passive_skill_id: 被動技能ID
            passive_skill_level: 被動技能等級
            slot_key: 槽位鍵（如 "slot_0", "slot_1"）

        Returns:
            (成功/失敗, 消息)
        """
        try:
            # 獲取玩家卡牌數據
            player_card_data = await self.player_collection_repo.get_player_card_by_collection_id(collection_id)
            if not player_card_data:
                return False, f"卡牌不存在: collection_id={collection_id}"

            if player_card_data.user_id != user_id:
                return False, "無權限操作此卡牌"

            # 獲取卡牌配置
            card_config = self.config_loader.get_card_config(player_card_data.card_id)
            if not card_config:
                return False, f"卡牌配置不存在: card_id={player_card_data.card_id}"

            # 驗證槽位鍵格式
            if not slot_key.startswith("slot_"):
                return False, f"無效的槽位鍵格式: {slot_key}"

            try:
                slot_index = int(slot_key.split("_")[1])
            except (IndexError, ValueError):
                return False, f"無效的槽位鍵格式: {slot_key}"

            # 驗證卡牌是否有足夠的被動槽位
            max_passive_slots = getattr(card_config, 'passive_skill_slots', 0)
            if slot_index >= max_passive_slots:
                return False, f"卡牌沒有槽位 {slot_index}，最大槽位數: {max_passive_slots}"

            # 驗證玩家是否已學習該技能且等級足夠
            learned_skill = await self.global_skill_repo.get_learned_skill(user_id, passive_skill_id, "PASSIVE")
            if not learned_skill:
                return False, f"尚未學習該被動技能: {passive_skill_id}"

            if learned_skill.skill_level < passive_skill_level:
                return False, f"技能等級不足，當前等級: {learned_skill.skill_level}，需要等級: {passive_skill_level}"

            # 更新裝備的被動技能
            equipped_passives = player_card_data.equipped_common_passives.copy()
            equipped_passives[slot_key] = {
                "skill_id": passive_skill_id,
                "level": passive_skill_level
            }

            # 更新數據庫
            success = await self.player_collection_repo.update_player_card_passive_skills(
                collection_id, equipped_passives
            )

            if success:
                return True, f"成功裝備被動技能 {passive_skill_id} (等級 {passive_skill_level}) 到 {slot_key}"
            else:
                return False, "更新數據庫失敗"

        except Exception as e:
            logger.error(f"裝備被動技能失敗: {e}")
            return False, f"裝備被動技能失敗: {str(e)}"

    async def unequip_passive_skill(
        self,
        user_id: int,
        collection_id: int,
        slot_key: str
    ) -> Tuple[bool, str]:
        """
        卸下被動技能

        Args:
            user_id: 用戶ID
            collection_id: 收藏ID
            slot_key: 槽位鍵（如 "slot_0", "slot_1"）

        Returns:
            (成功/失敗, 消息)
        """
        try:
            # 獲取玩家卡牌數據
            player_card_data = await self.player_collection_repo.get_player_card_by_collection_id(collection_id)
            if not player_card_data:
                return False, f"卡牌不存在: collection_id={collection_id}"

            if player_card_data.user_id != user_id:
                return False, "無權限操作此卡牌"

            # 驗證槽位鍵是否存在
            if slot_key not in player_card_data.equipped_common_passives:
                return False, f"槽位不存在: {slot_key}"

            # 更新裝備的被動技能
            equipped_passives = player_card_data.equipped_common_passives.copy()
            equipped_passives[slot_key] = None

            # 更新數據庫
            success = await self.player_collection_repo.update_player_card_passive_skills(
                collection_id, equipped_passives
            )

            if success:
                return True, f"成功卸下 {slot_key} 的被動技能"
            else:
                return False, "更新數據庫失敗"

        except Exception as e:
            logger.error(f"卸下被動技能失敗: {e}")
            return False, f"卸下被動技能失敗: {str(e)}"

    async def get_card_details_for_display(
        self,
        user_id: int,
        collection_id: int
    ) -> Optional[Dict[str, Any]]:
        """
        獲取卡牌詳細信息用於前端展示

        Args:
            user_id: 用戶ID
            collection_id: 收藏ID

        Returns:
            卡牌詳細信息字典或 None
        """
        try:
            # 獲取玩家卡牌數據
            player_card_data = await self.player_collection_repo.get_player_card_by_collection_id(collection_id)
            if not player_card_data:
                return None

            if player_card_data.user_id != user_id:
                return None

            # 獲取卡牌配置
            card_config = self.config_loader.get_card_config(player_card_data.card_id)
            if not card_config:
                return None

            # 構建返回數據
            result = {
                "collection_id": collection_id,
                "card_id": player_card_data.card_id,
                "card_name": getattr(card_config, 'name', player_card_data.card_id),
                "rpg_level": player_card_data.rpg_level,
                "rpg_xp": player_card_data.rpg_xp,
                "star_level": player_card_data.star_level,
                "is_favorite": player_card_data.is_favorite,
                "equipped_active_skills": player_card_data.equipped_active_skill_ids,
                "equipped_passive_skills": player_card_data.equipped_common_passives,
                "max_passive_slots": getattr(card_config, 'passive_skill_slots', 0),
                "innate_passive_skill_id": getattr(card_config, 'innate_passive_skill_id', None),
                "primary_attack_skill_id": getattr(card_config, 'primary_attack_skill_id', None)
            }

            return result

        except Exception as e:
            logger.error(f"獲取卡牌詳細信息失敗: {e}")
            return None
