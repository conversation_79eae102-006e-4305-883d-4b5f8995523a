"""
屬性計算器單元測試
"""
import unittest
from unittest.mock import Mock, MagicMock

from rpg_system.battle_system.services.attribute_calculator import (
    AttributeCalculator, AttributeCalculatorError
)


class TestAttributeCalculator(unittest.TestCase):
    """AttributeCalculator 測試類"""
    
    def setUp(self):
        """測試前設置"""
        self.calculator = AttributeCalculator()
        self.mock_config_loader = Mock()
    
    def test_card_base_attributes_level_1_no_star(self):
        """測試1級卡牌無星級的屬性計算"""
        # 設置mock配置
        card_config = {
            "base_stats": {
                "max_hp": 100,
                "max_mp": 50,
                "patk": 80,
                "pdef": 40,
                "matk": 60,
                "mdef": 30,
                "spd": 70,
                "crit_rate": 0.1,
                "crit_dmg_multiplier": 1.5,
                "accuracy": 0.95,
                "evasion": 0.05
            },
            "growth_per_rpg_level": {
                "max_hp_growth": 10,
                "patk_growth": 5
            },
            "star_level_effects_key": "fire_mage_effects"
        }
        
        self.mock_config_loader.get_card_config.return_value = card_config
        
        # 計算屬性
        result = self.calculator.calculate_attributes(
            "card_001", "CARD", 1, 0, self.mock_config_loader
        )
        
        # 驗證結果
        self.assertEqual(result["max_hp"], 100.0)
        self.assertEqual(result["max_mp"], 50.0)
        self.assertEqual(result["patk"], 80.0)
        self.assertEqual(result["pdef"], 40.0)
        self.assertEqual(result["matk"], 60.0)
        self.assertEqual(result["mdef"], 30.0)
        self.assertEqual(result["spd"], 70.0)
        self.assertEqual(result["crit_rate"], 0.1)
        self.assertEqual(result["crit_dmg_multiplier"], 1.5)
        self.assertEqual(result["accuracy"], 0.95)
        self.assertEqual(result["evasion"], 0.05)
        self.assertEqual(result["mp_regen_per_turn"], 0.0)  # 默認值
    
    def test_card_rpg_level_growth(self):
        """測試卡牌RPG等級成長"""
        card_config = {
            "base_stats": {
                "max_hp": 100,
                "patk": 80,
                "matk": 60
            },
            "growth_per_rpg_level": {
                "max_hp_growth": 10,
                "patk_growth": 5,
                "matk_growth": 3
            }
        }
        
        self.mock_config_loader.get_card_config.return_value = card_config
        
        # 測試10級卡牌
        result = self.calculator.calculate_attributes(
            "card_001", "CARD", 10, 0, self.mock_config_loader
        )
        
        # 驗證成長計算 (10級 = 基礎 + 9級成長)
        self.assertEqual(result["max_hp"], 100.0 + 10.0 * 9)  # 190
        self.assertEqual(result["patk"], 80.0 + 5.0 * 9)      # 125
        self.assertEqual(result["matk"], 60.0 + 3.0 * 9)      # 87
    
    def test_card_star_level_flat_bonus(self):
        """測試卡牌星級扁平屬性加成"""
        card_config = {
            "base_stats": {
                "max_hp": 100,
                "patk": 80
            },
            "star_level_effects_key": "test_effects"
        }
        
        star_effects_config = {
            "1": {
                "additional_stats_flat": {
                    "max_hp": 20,
                    "patk": 10
                }
            },
            "2": {
                "additional_stats_flat": {
                    "max_hp": 25,
                    "patk": 12
                }
            }
        }
        
        self.mock_config_loader.get_card_config.return_value = card_config
        self.mock_config_loader.get_star_level_effects_config.return_value = (
            star_effects_config
        )
        
        # 測試2星級卡牌
        result = self.calculator.calculate_attributes(
            "card_001", "CARD", 1, 2, self.mock_config_loader
        )
        
        # 驗證星級加成 (1星 + 2星)
        self.assertEqual(result["max_hp"], 100.0 + 20.0 + 25.0)  # 145
        self.assertEqual(result["patk"], 80.0 + 10.0 + 12.0)     # 102
    
    def test_card_star_level_percent_bonus(self):
        """測試卡牌星級百分比屬性加成"""
        card_config = {
            "base_stats": {
                "max_hp": 100,
                "patk": 80
            },
            "star_level_effects_key": "test_effects"
        }
        
        star_effects_config = {
            "1": {
                "additional_stats_percent": {
                    "max_hp": 0.1,  # 10%
                    "patk": 0.05    # 5%
                }
            }
        }
        
        self.mock_config_loader.get_card_config.return_value = card_config
        self.mock_config_loader.get_star_level_effects_config.return_value = (
            star_effects_config
        )
        
        # 測試1星級卡牌
        result = self.calculator.calculate_attributes(
            "card_001", "CARD", 1, 1, self.mock_config_loader
        )
        
        # 驗證百分比加成 (基於基礎屬性)
        self.assertEqual(result["max_hp"], 100.0 + 100.0 * 0.1)  # 110
        self.assertEqual(result["patk"], 80.0 + 80.0 * 0.05)     # 84
    
    def test_card_mixed_flat_and_percent_bonus(self):
        """測試卡牌混合扁平與百分比加成"""
        card_config = {
            "base_stats": {
                "max_hp": 100,
                "patk": 80
            },
            "star_level_effects_key": "test_effects"
        }
        
        star_effects_config = {
            "1": {
                "additional_stats_flat": {
                    "max_hp": 20
                },
                "additional_stats_percent": {
                    "patk": 0.1  # 10%
                }
            }
        }
        
        self.mock_config_loader.get_card_config.return_value = card_config
        self.mock_config_loader.get_star_level_effects_config.return_value = (
            star_effects_config
        )
        
        result = self.calculator.calculate_attributes(
            "card_001", "CARD", 1, 1, self.mock_config_loader
        )
        
        self.assertEqual(result["max_hp"], 100.0 + 20.0)        # 120 (扁平)
        self.assertEqual(result["patk"], 80.0 + 80.0 * 0.1)    # 88 (百分比)
    
    def test_card_rpg_level_and_star_level_combined(self):
        """測試卡牌RPG等級成長和星級效果組合"""
        card_config = {
            "base_stats": {
                "max_hp": 100,
                "patk": 80
            },
            "growth_per_rpg_level": {
                "max_hp_growth": 10,
                "patk_growth": 5
            },
            "star_level_effects_key": "test_effects"
        }
        
        star_effects_config = {
            "1": {
                "additional_stats_flat": {
                    "max_hp": 20
                },
                "additional_stats_percent": {
                    "patk": 0.1  # 基於基礎80的10%
                }
            }
        }
        
        self.mock_config_loader.get_card_config.return_value = card_config
        self.mock_config_loader.get_star_level_effects_config.return_value = (
            star_effects_config
        )
        
        # 測試5級1星卡牌
        result = self.calculator.calculate_attributes(
            "card_001", "CARD", 5, 1, self.mock_config_loader
        )
        
        # RPG等級成長: 5級 = 基礎 + 4級成長
        # 星級效果: 1星
        expected_hp = 100.0 + 10.0 * 4 + 20.0  # 160
        expected_patk = 80.0 + 5.0 * 4 + 80.0 * 0.1  # 108
        
        self.assertEqual(result["max_hp"], expected_hp)
        self.assertEqual(result["patk"], expected_patk)
    
    def test_monster_attributes(self):
        """測試怪物屬性計算"""
        monster_config = {
            "max_hp": 200,
            "max_mp": 30,
            "mp_regen_per_turn": 3,
            "patk": 60,
            "pdef": 30,
            "matk": 40,
            "mdef": 20,
            "spd": 50,
            "crit_rate": 0.05,
            "crit_dmg_multiplier": 1.3,
            "accuracy": 0.9,
            "evasion": 0.1
        }
        
        self.mock_config_loader.get_monster_config.return_value = monster_config
        
        result = self.calculator.calculate_attributes(
            "monster_001", "MONSTER", 1, 0, self.mock_config_loader
        )
        
        # 怪物屬性應該直接從配置中讀取
        self.assertEqual(result["max_hp"], 200.0)
        self.assertEqual(result["max_mp"], 30.0)
        self.assertEqual(result["mp_regen_per_turn"], 3.0)
        self.assertEqual(result["patk"], 60.0)
        self.assertEqual(result["pdef"], 30.0)
        self.assertEqual(result["matk"], 40.0)
        self.assertEqual(result["mdef"], 20.0)
        self.assertEqual(result["spd"], 50.0)
        self.assertEqual(result["crit_rate"], 0.05)
        self.assertEqual(result["crit_dmg_multiplier"], 1.3)
        self.assertEqual(result["accuracy"], 0.9)
        self.assertEqual(result["evasion"], 0.1)
    
    def test_invalid_card_id(self):
        """測試無效卡牌ID"""
        self.mock_config_loader.get_card_config.return_value = None
        
        with self.assertRaises(AttributeCalculatorError) as context:
            self.calculator.calculate_attributes(
                "invalid_card", "CARD", 1, 0, self.mock_config_loader
            )
        
        self.assertIn("卡牌配置不存在", str(context.exception))
    
    def test_invalid_monster_id(self):
        """測試無效怪物ID"""
        self.mock_config_loader.get_monster_config.return_value = None
        
        with self.assertRaises(AttributeCalculatorError) as context:
            self.calculator.calculate_attributes(
                "invalid_monster", "MONSTER", 1, 0, self.mock_config_loader
            )
        
        self.assertIn("怪物配置不存在", str(context.exception))
    
    def test_invalid_combatant_type(self):
        """測試無效戰鬥單位類型"""
        with self.assertRaises(AttributeCalculatorError) as context:
            self.calculator.calculate_attributes(
                "test_id", "INVALID", 1, 0, self.mock_config_loader
            )
        
        self.assertIn("未知的戰鬥單位類型", str(context.exception))
    
    def test_missing_star_level_effects_config(self):
        """測試缺失星級效果配置"""
        card_config = {
            "base_stats": {"max_hp": 100},
            "star_level_effects_key": "missing_effects"
        }
        
        self.mock_config_loader.get_card_config.return_value = card_config
        self.mock_config_loader.get_star_level_effects_config.return_value = None
        
        # 應該不拋出異常，只是警告
        result = self.calculator.calculate_attributes(
            "card_001", "CARD", 1, 1, self.mock_config_loader
        )
        
        # 應該只有基礎屬性
        self.assertEqual(result["max_hp"], 100.0)
    
    def test_attribute_bounds_validation(self):
        """測試屬性邊界驗證"""
        card_config = {
            "base_stats": {
                "max_hp": -10,  # 負HP
                "max_mp": -5,   # 負MP
                "crit_rate": 1.5,  # 超過100%暴擊率
                "accuracy": 1.2,   # 超過100%命中率
                "evasion": -0.1,   # 負閃避率
                "crit_dmg_multiplier": 0.5  # 小於1.0的暴擊倍率
            }
        }
        
        self.mock_config_loader.get_card_config.return_value = card_config
        
        result = self.calculator.calculate_attributes(
            "card_001", "CARD", 1, 0, self.mock_config_loader
        )
        
        # 驗證邊界修正
        self.assertEqual(result["max_hp"], 1.0)    # 負HP修正為1
        self.assertEqual(result["max_mp"], 0.0)    # 負MP修正為0
        self.assertEqual(result["crit_rate"], 1.0) # 暴擊率限制在1.0
        self.assertEqual(result["accuracy"], 1.0)  # 命中率限制在1.0
        self.assertEqual(result["evasion"], 0.0)   # 閃避率限制在0.0
        self.assertEqual(result["crit_dmg_multiplier"], 1.0)  # 暴擊倍率最小1.0
    
    def test_required_attributes_presence(self):
        """測試必需屬性的存在"""
        # 空的基礎配置
        card_config = {"base_stats": {}}
        
        self.mock_config_loader.get_card_config.return_value = card_config
        
        result = self.calculator.calculate_attributes(
            "card_001", "CARD", 1, 0, self.mock_config_loader
        )
        
        # 驗證所有必需屬性都存在
        required_attributes = [
            "max_hp", "max_mp", "mp_regen_per_turn", "patk", "pdef",
            "matk", "mdef", "spd", "crit_rate", "crit_dmg_multiplier",
            "accuracy", "evasion"
        ]
        
        for attr_name in required_attributes:
            self.assertIn(attr_name, result)
            self.assertIsInstance(result[attr_name], float)


if __name__ == '__main__':
    unittest.main()
