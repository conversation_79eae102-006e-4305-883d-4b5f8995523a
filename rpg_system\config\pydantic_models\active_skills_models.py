"""
Active Skills Pydantic Models
Based on RPG_02_Configuration_Files.md
"""
from typing import List, Optional, Dict, Any, Union, Literal
from pydantic import BaseModel, Field, validator

# -------------------- Modifier Model --------------------
# (來自 RPG_02_Configuration_Files.md 5.3. 傷害修正器 (modifiers) 詳細說明)
class ModifierCondition(BaseModel):
    """用於 CONDITIONAL_BOOST 的內部條件"""
    source_combatant: Literal["caster", "target"]
    check: str  # e.g., "has_status_effect", "hp_below_percent"
    value: Optional[Any] = None
    status_effect_id: Optional[str] = None

class ModifierConditionGroup(BaseModel):
    """用於 CONDITIONAL_BOOST 的條件組"""
    type: Optional[Literal["AND", "OR"]] = "AND"  # 假設默認為 AND
    conditions: List[ModifierCondition]

class Modifier(BaseModel):
    """傷害修正器模型"""
    modifier_type: str  # e.g., "SCALING_MODIFIER", "CONDITIONAL_BOOST"
    
    # SCALING_MODIFIER 參數
    scaling_source: Optional[str] = None 
    source_combatant: Optional[Literal["caster", "target"]] = None
    scaling_type: Optional[str] = None 
    scaling_factor: Optional[float] = None
    max_bonus_value: Optional[float] = None
    apply_as_damage_reduction: Optional[bool] = False
    
    # CONDITIONAL_BOOST 參數
    condition_group: Optional[ModifierConditionGroup] = None
    bonus_type: Optional[str] = None 
    bonus_value: Optional[float] = None

    @validator('source_combatant', always=True)
    def check_source_combatant_for_scaling_modifier(cls, v, values):
        if values.get('modifier_type') == "SCALING_MODIFIER" and v is None:
            raise ValueError('source_combatant is required for modifier_type "SCALING_MODIFIER"')
        return v

    @validator('scaling_source', 'scaling_type', 'scaling_factor', always=True)
    def check_scaling_modifier_params(cls, v, values):
        if values.get('modifier_type') == "SCALING_MODIFIER":
            # 粗略檢查，實際應更精確
            if values.get('scaling_source') is None or values.get('scaling_type') is None or values.get('scaling_factor') is None:
                raise ValueError('scaling_source, scaling_type, and scaling_factor are required for SCALING_MODIFIER')
        return v

    @validator('condition_group', 'bonus_type', 'bonus_value', always=True)
    def check_conditional_boost_params(cls, v, values):
        if values.get('modifier_type') == "CONDITIONAL_BOOST":
            if values.get('condition_group') is None or values.get('bonus_type') is None or values.get('bonus_value') is None:
                raise ValueError('condition_group, bonus_type, and bonus_value are required for CONDITIONAL_BOOST')
        return v

# -------------------- EffectDefinition Model --------------------
class StatModificationItem(BaseModel):
    """統計修改項目"""
    stat_name: str
    modification_type: str  # e.g., "PERCENTAGE_ADD", "FLAT_ADD"
    value_formula: str 

class EffectDefinition(BaseModel):
    """效果定義模型"""
    effect_template: Optional[str] = None 
    
    multiplier: Optional[float] = None 
    can_crit: Optional[bool] = None
    duration_turns: Optional[int] = Field(None, ge=0)
    chance: Optional[float] = Field(None, ge=0, le=1)
    stack_count: Optional[int] = Field(None, ge=1)
    
    effect_type: Optional[str] = None 
    
    # DAMAGE 效果參數
    damage_type: Optional[str] = None
    base_power_multiplier: Optional[float] = None
    flat_damage_add: Optional[float] = None
    
    # HEAL 效果參數
    heal_type: Optional[str] = None
    value: Optional[float] = None
    
    # APPLY_STATUS_EFFECT 效果參數
    status_effect_id: Optional[str] = None
    
    # STAT_MODIFICATION 效果參數
    trigger_condition: Optional[str] = None
    modifications: Optional[List[StatModificationItem]] = None
    
    modifiers: Optional[List[Modifier]] = None

    @validator('effect_type', always=True)
    def check_definition_method(cls, v, values):
        effect_template_defined = values.get('effect_template') is not None
        effect_type_defined = v is not None

        if effect_template_defined and effect_type_defined:
            raise ValueError('Cannot define both effect_template and effect_type simultaneously for an effect.')
        if not effect_template_defined and not effect_type_defined:
            raise ValueError('Either effect_template or effect_type must be defined for an effect.')
        return v

# -------------------- Target Logic Models --------------------
class TargetLogicParams(BaseModel):
    """目標邏輯參數"""
    pass  # 可以根據需要添加具體參數

class TargetLogicCondition(BaseModel):
    """目標邏輯條件"""
    type: str  # e.g., "TARGET_HP_BELOW_PERCENT", "TARGET_HAS_STATUS_EFFECT"
    params: Optional[TargetLogicParams] = None

class TargetLogicDetail(BaseModel):
    """目標邏輯詳細信息"""
    priority_score: int
    condition: Optional[TargetLogicCondition] = None
    target_selection: str  # 目標選擇邏輯

# -------------------- ActiveSkillEffectLevel Model --------------------
class ActiveSkillEffectLevel(BaseModel):
    """主動技能效果等級"""
    mp_cost: int = Field(..., ge=0)
    cooldown_turns: int = Field(..., ge=0)
    effect_definitions: List[EffectDefinition]

# -------------------- ActiveSkillConfig Model (Main) --------------------
class ActiveSkillConfig(BaseModel):
    """主動技能配置"""
    name: str
    description_template: str
    skill_rarity: int = Field(..., ge=1, le=7)
    max_level: int = Field(..., ge=1)
    target_type: str 
    target_logic_details: Optional[List[TargetLogicDetail]] = None
    effects_by_level: Dict[str, ActiveSkillEffectLevel]
    xp_gain_on_sacrifice: Optional[int] = Field(None, ge=0)
    upgrade_cost_modifier: Optional[float] = Field(None, gt=0)
    tags: Optional[List[str]] = None

    @validator('effects_by_level')
    def check_effects_by_level_keys(cls, v, values):
        max_level = values.get('max_level')
        if max_level is not None:
            expected_keys = {str(i) for i in range(1, max_level + 1)}
            actual_keys = set(v.keys())
            if expected_keys != actual_keys:
                raise ValueError(f'effects_by_level keys must match levels 1 to {max_level}. Expected: {expected_keys}, Got: {actual_keys}')
        return v

# -------------------- Main Model --------------------
AllActiveSkillsConfig = Dict[str, ActiveSkillConfig]
