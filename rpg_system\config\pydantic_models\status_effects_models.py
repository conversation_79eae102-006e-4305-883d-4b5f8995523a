"""
Status Effects Pydantic Models
Based on RPG_02_Configuration_Files.md
"""
from typing import List, Optional, Dict, Literal
from pydantic import BaseModel, Field
from .active_skills_models import EffectDefinition

# -------------------- StatusEffectConfig Model --------------------
class StatusEffectConfig(BaseModel):
    """狀態效果配置"""
    name: str
    icon_key: Optional[str] = None
    is_buff: bool
    max_stacks: Optional[int] = Field(1, ge=1)
    duration_type: Literal["TURNS", "INFINITE", "UNTIL_TRIGGERED"]
    default_duration: Optional[int] = Field(None, ge=0)
    tick_at_turn_start: Optional[bool] = False
    tick_at_turn_end: Optional[bool] = False
    effect_definitions_on_apply: Optional[List[EffectDefinition]] = None
    effect_definitions_per_tick: Optional[List[EffectDefinition]] = None
    effect_definitions_on_expire: Optional[List[EffectDefinition]] = None
    effect_definitions_triggered: Optional[List[EffectDefinition]] = None  # 結構可能需要細化，或與被動的 PassiveEffectBlock 類似
    special_flags: Optional[List[Literal[
        "CANNOT_ACT", "DISPELLABLE", "UNDISPELLABLE", 
        "STACKABLE_DURATION", "STACKABLE_INTENSITY", 
        "NON_STACKABLE_REFRESH_DURATION", "NON_STACKABLE_IGNORE",
        "PERSISTS_THROUGH_DEATH", "REMOVE_ON_DEATH", "LINKED_TO_CASTER"
    ]]] = None

# -------------------- Main Model --------------------
AllStatusEffectsConfig = Dict[str, StatusEffectConfig]
