"""
Cards Pydantic Models
Based on RPG_02_Configuration_Files.md
"""
from typing import List, Optional, Dict
from pydantic import BaseModel, Field

# -------------------- BaseStats Model --------------------
class BaseStats(BaseModel):
    """基礎屬性"""
    hp: int = Field(..., ge=1)
    max_mp: int = Field(..., ge=0)
    mp_regen_per_turn: int = Field(..., ge=0)
    patk: int = Field(..., ge=0)
    pdef: int = Field(..., ge=0)
    matk: int = Field(..., ge=0)
    mdef: int = Field(..., ge=0)
    spd: int = Field(..., ge=0)
    crit_rate: float = Field(..., ge=0, le=1)
    crit_dmg_multiplier: float = Field(..., ge=1)
    accuracy: float = Field(..., ge=0, le=1)
    evasion: float = Field(..., ge=0, le=1)

# -------------------- GrowthPerRPGLevel Model --------------------
class GrowthPerRPGLevel(BaseModel):
    """每RPG等級成長"""
    hp: float = Field(..., ge=0)
    max_mp: float = Field(..., ge=0)
    mp_regen_per_turn: float = Field(..., ge=0)
    patk: float = Field(..., ge=0)
    pdef: float = Field(..., ge=0)
    matk: float = Field(..., ge=0)
    mdef: float = Field(..., ge=0)
    spd: float = Field(..., ge=0)
    crit_rate: float = Field(..., ge=0)
    crit_dmg_multiplier: float = Field(..., ge=0)
    accuracy: float = Field(..., ge=0)
    evasion: float = Field(..., ge=0)

# -------------------- DefaultPassiveSlot Model --------------------
class DefaultPassiveSlot(BaseModel):
    """默認被動技能槽"""
    slot_index: int = Field(..., ge=0)
    skill_id: str
    initial_level: int = Field(1, ge=1)

# -------------------- CardConfig Model --------------------
class CardConfig(BaseModel):
    """卡牌配置"""
    innate_passive_skill_id: str
    primary_attack_skill_id: str
    default_active_skill_slot_1_id: Optional[str] = None
    default_active_skill_slot_2_id: Optional[str] = None
    default_active_skill_slot_3_id: Optional[str] = None
    passive_skill_slots: int = Field(..., ge=0)
    default_passives_on_acquire: Optional[List[DefaultPassiveSlot]] = None
    base_stats: BaseStats
    growth_per_rpg_level: GrowthPerRPGLevel
    star_level_effects_key: Optional[str] = None

# -------------------- Main Model --------------------
AllCardsConfig = Dict[str, CardConfig]
