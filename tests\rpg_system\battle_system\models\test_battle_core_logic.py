"""
戰鬥系統核心邏輯測試
"""
import pytest
from unittest.mock import Mock, MagicMock
from datetime import datetime

from rpg_system.battle_system.models.battle import Battle, BattleError
from rpg_system.battle_system.models.combatant import Combatant, CombatantStats
from rpg_system.battle_system.models.enums import BattleStatus, SkillType
from rpg_system.battle_system.models.skill_instance import SkillInstance


class TestBattleCoreLogic:
    """戰鬥核心邏輯測試"""
    
    def setup_method(self):
        """設置測試環境"""
        # 創建模擬依賴
        self.config_loader = Mock()
        self.effect_applier = Mock()
        self.target_selector = Mock()
        self.passive_trigger_handler = Mock()
        self.formula_evaluator = Mock()
        
        # 創建測試戰鬥者
        self.player_stats = CombatantStats(
            hp=100, max_mp=50, mp_regen_per_turn=5,
            patk=20, pdef=10, matk=15, mdef=8,
            spd=12, crit_rate=0.1, crit_dmg_multiplier=1.5,
            accuracy=0.9, evasion=0.1
        )
        
        self.monster_stats = CombatantStats(
            hp=80, max_mp=30, mp_regen_per_turn=3,
            patk=18, pdef=8, matk=12, mdef=6,
            spd=10, crit_rate=0.05, crit_dmg_multiplier=1.3,
            accuracy=0.85, evasion=0.05
        )
        
        # 創建戰鬥者
        self.player = Combatant(
            combatant_id="player_1",
            name="測試玩家",
            stats=self.player_stats,
            is_player=True,
            card_id="card_001",
            definition_id="card_001"
        )
        
        self.monster = Combatant(
            combatant_id="monster_1", 
            name="測試怪物",
            stats=self.monster_stats,
            is_player=False,
            definition_id="monster_001"
        )
        
        # 添加基本技能
        self._setup_combatant_skills()
        
        # 創建戰鬥實例
        self.battle = Battle(
            player_team=[self.player],
            monster_team=[self.monster],
            config_loader=self.config_loader,
            effect_applier=self.effect_applier,
            target_selector=self.target_selector,
            passive_trigger_handler=self.passive_trigger_handler,
            formula_evaluator=self.formula_evaluator,
            rng_seed=42
        )
    
    def _setup_combatant_skills(self):
        """設置戰鬥者技能"""
        # 玩家普攻
        player_attack = SkillInstance(
            skill_id="basic_attack",
            skill_type=SkillType.PRIMARY_ATTACK,
            current_level=1,
            current_cooldown=0
        )
        self.player.add_skill(player_attack)

        # 怪物普攻
        monster_attack = SkillInstance(
            skill_id="monster_attack",
            skill_type=SkillType.PRIMARY_ATTACK,
            current_level=1,
            current_cooldown=0
        )
        self.monster.add_skill(monster_attack)
        self.monster.skill_order_preference = ["monster_attack"]
    
    def test_battle_initialization(self):
        """測試戰鬥初始化"""
        assert self.battle.battle_status == BattleStatus.PENDING
        assert self.battle.current_turn == 0
        assert len(self.battle.player_team) == 1
        assert len(self.battle.monster_team) == 1
        assert self.battle.player_team[0] == self.player
        assert self.battle.monster_team[0] == self.monster
    
    def test_battle_start(self):
        """測試戰鬥啟動"""
        # 模擬配置
        self.config_loader.get_card_config.return_value = {}
        
        # 啟動戰鬥
        self.battle.start()
        
        # 驗證狀態
        assert self.battle.battle_status == BattleStatus.IN_PROGRESS
        assert self.battle.current_turn == 1
        assert self.battle.started_at is not None
        assert len(self.battle.combatant_queue) == 2
        
        # 驗證行動順序（速度高的先行動）
        acting_combatant = self.battle.get_acting_combatant()
        assert acting_combatant is not None
        assert acting_combatant.get_stat('spd') >= 10  # 至少有一個戰鬥者
    
    def test_battle_start_already_started(self):
        """測試重複啟動戰鬥"""
        self.config_loader.get_card_config.return_value = {}
        
        # 第一次啟動
        self.battle.start()
        
        # 第二次啟動應該失敗
        with pytest.raises(BattleError):
            self.battle.start()
    
    def test_get_acting_combatant(self):
        """測試獲取當前行動者"""
        self.config_loader.get_card_config.return_value = {}
        
        # 戰鬥未開始時
        assert self.battle.get_acting_combatant() is None
        
        # 啟動戰鬥
        self.battle.start()
        
        # 應該有行動者
        acting_combatant = self.battle.get_acting_combatant()
        assert acting_combatant is not None
        assert acting_combatant in [self.player, self.monster]
    
    def test_next_turn_or_combatant(self):
        """測試推進回合"""
        self.config_loader.get_card_config.return_value = {}
        self.battle.start()
        
        initial_acting = self.battle.get_acting_combatant()
        initial_turn = self.battle.current_turn
        
        # 推進到下一個行動者
        next_acting = self.battle.next_turn_or_combatant()
        
        # 驗證結果
        if next_acting:
            # 如果還有下一個行動者，應該不同於當前行動者
            assert next_acting != initial_acting or self.battle.current_turn > initial_turn
    
    def test_get_all_alive_combatants(self):
        """測試獲取存活戰鬥者"""
        alive_combatants = self.battle.get_all_alive_combatants()
        assert len(alive_combatants) == 2
        assert self.player in alive_combatants
        assert self.monster in alive_combatants
        
        # 殺死一個戰鬥者
        self.player.take_damage(1000)
        
        alive_combatants = self.battle.get_all_alive_combatants()
        assert len(alive_combatants) == 1
        assert self.monster in alive_combatants
        assert self.player not in alive_combatants
    
    def test_get_enemies_and_allies(self):
        """測試獲取敵人和盟友"""
        # 玩家的敵人應該是怪物
        player_enemies = self.battle.get_all_alive_enemies_of(self.player)
        assert len(player_enemies) == 1
        assert self.monster in player_enemies
        
        # 玩家的盟友應該是自己
        player_allies = self.battle.get_all_alive_allies_of(self.player)
        assert len(player_allies) == 1
        assert self.player in player_allies
        
        # 怪物的敵人應該是玩家
        monster_enemies = self.battle.get_all_alive_enemies_of(self.monster)
        assert len(monster_enemies) == 1
        assert self.player in monster_enemies
    
    def test_check_win_condition_player_win(self):
        """測試玩家勝利條件"""
        self.config_loader.get_card_config.return_value = {}
        self.battle.start()
        
        # 殺死所有怪物
        self.monster.take_damage(1000)
        
        # 檢查勝負條件
        self.battle.check_win_condition()
        
        assert self.battle.battle_status == BattleStatus.PLAYER_WIN
        assert self.battle.ended_at is not None
    
    def test_check_win_condition_monster_win(self):
        """測試怪物勝利條件"""
        self.config_loader.get_card_config.return_value = {}
        self.battle.start()
        
        # 殺死所有玩家
        self.player.take_damage(1000)
        
        # 檢查勝負條件
        self.battle.check_win_condition()
        
        assert self.battle.battle_status == BattleStatus.MONSTER_WIN
        assert self.battle.ended_at is not None
    
    def test_check_win_condition_draw(self):
        """測試平局條件"""
        self.config_loader.get_card_config.return_value = {}
        self.battle.start()
        
        # 殺死所有戰鬥者
        self.player.take_damage(1000)
        self.monster.take_damage(1000)
        
        # 檢查勝負條件
        self.battle.check_win_condition()
        
        assert self.battle.battle_status == BattleStatus.DRAW
        assert self.battle.ended_at is not None
    
    def test_process_action_basic(self):
        """測試基本行動處理"""
        self.config_loader.get_card_config.return_value = {}
        
        # 模擬技能定義
        skill_def = {
            "name": "基礎攻擊",
            "mp_cost": 0,
            "effect_definitions": []
        }
        
        # 模擬技能實例方法
        self.player.primary_attack.get_definition = Mock(return_value=skill_def)
        self.player.primary_attack.is_usable = Mock(return_value=True)
        self.player.primary_attack.put_on_cooldown = Mock()
        
        # 模擬目標選擇
        self.target_selector.select_targets.return_value = [self.monster]
        
        # 模擬效果應用
        self.effect_applier.apply_skill_effects.return_value = []
        
        self.battle.start()
        
        # 處理行動
        self.battle.process_action(
            caster=self.player,
            skill_id="basic_attack",
            explicit_target_ids=[self.monster.instance_id]
        )
        
        # 驗證調用
        self.effect_applier.apply_skill_effects.assert_called_once()
        self.player.primary_attack.put_on_cooldown.assert_called_once()
    
    def test_battle_summary(self):
        """測試戰鬥摘要"""
        summary = self.battle.get_battle_summary()
        
        assert summary["battle_id"] == self.battle.battle_id
        assert summary["status"] == BattleStatus.PENDING.value
        assert summary["current_turn"] == 0
        assert len(summary["player_team"]) == 1
        assert len(summary["monster_team"]) == 1
        assert summary["acting_combatant"] is None
        
        # 啟動戰鬥後
        self.config_loader.get_card_config.return_value = {}
        self.battle.start()
        
        summary = self.battle.get_battle_summary()
        assert summary["status"] == BattleStatus.IN_PROGRESS.value
        assert summary["current_turn"] == 1
        assert summary["acting_combatant"] is not None
