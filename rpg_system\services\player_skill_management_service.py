"""
玩家技能管理服務

負責玩家全局技能的學習、升級、獲取進度等
"""
from typing import Dict, Any, Optional, Tuple, List, Literal, Union, TYPE_CHECKING
import logging

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader
    from rpg_system.repositories.global_skill_repository import GlobalSkillRepository, LearnedGlobalSkillDBData
    from rpg_system.config.pydantic_models.skills_models import ActiveSkillConfig, PassiveSkillConfig

logger = logging.getLogger(__name__)


class PlayerSkillManagementServiceError(Exception):
    """玩家技能管理服務異常"""
    pass


class PlayerSkillManagementService:
    """
    玩家技能管理服務

    負責玩家全局技能的學習、升級、獲取進度等
    """

    def __init__(
        self,
        config_loader: 'ConfigLoader',
        global_skill_repo: 'GlobalSkillRepository'
    ):
        """
        初始化玩家技能管理服務

        Args:
            config_loader: 配置加載器
            global_skill_repo: 全局技能倉庫
        """
        self.config_loader = config_loader
        self.global_skill_repo = global_skill_repo

    async def learn_global_skill(
        self,
        user_id: int,
        skill_id: str,
        skill_type: Literal["ACTIVE", "PASSIVE"]
    ) -> Tuple[bool, str]:
        """
        學習全局技能

        Args:
            user_id: 用戶ID
            skill_id: 技能ID
            skill_type: 技能類型

        Returns:
            (成功/失敗, 消息)
        """
        try:
            # 檢查技能是否存在於配置中
            if skill_type == "ACTIVE":
                skill_config = self.config_loader.get_active_skill_config(skill_id)
            else:
                skill_config = self.config_loader.get_passive_skill_config(skill_id)

            if not skill_config:
                return False, f"技能配置不存在: {skill_id}"

            # 檢查玩家是否已學習此技能
            existing_skill = await self.global_skill_repo.get_learned_skill(user_id, skill_id, skill_type)
            if existing_skill:
                return False, f"已經學習過該技能: {skill_id}"

            # 添加學習記錄
            learned_skill = await self.global_skill_repo.add_learned_skill(
                user_id, skill_id, skill_type, initial_level=1, initial_xp=0
            )

            if learned_skill:
                return True, f"成功學習技能: {skill_id}"
            else:
                return False, "添加學習記錄失敗"

        except Exception as e:
            logger.error(f"學習全局技能失敗: {e}")
            return False, f"學習全局技能失敗: {str(e)}"

    async def upgrade_global_skill(
        self,
        user_id: int,
        skill_id: str,
        skill_type: Literal["ACTIVE", "PASSIVE"]
    ) -> Tuple[bool, str]:
        """
        升級全局技能

        Args:
            user_id: 用戶ID
            skill_id: 技能ID
            skill_type: 技能類型

        Returns:
            (成功/失敗, 消息)
        """
        try:
            # 獲取玩家已學習的技能數據
            learned_skill = await self.global_skill_repo.get_learned_skill(user_id, skill_id, skill_type)
            if not learned_skill:
                return False, f"尚未學習該技能: {skill_id}"

            # 獲取技能配置
            if skill_type == "ACTIVE":
                skill_config = self.config_loader.get_active_skill_config(skill_id)
            else:
                skill_config = self.config_loader.get_passive_skill_config(skill_id)

            if not skill_config:
                return False, f"技能配置不存在: {skill_id}"

            # 檢查是否達到最大等級
            max_level = getattr(skill_config, 'max_level', 10)
            if learned_skill.skill_level >= max_level:
                return False, f"技能已達到最大等級: {max_level}"

            # 檢查是否有足夠的XP
            xp_required = self.get_skill_xp_requirements(skill_config, learned_skill.skill_level)
            if xp_required is None:
                return False, "無法計算升級所需XP"

            if learned_skill.skill_xp < xp_required:
                return False, f"XP不足，當前: {learned_skill.skill_xp}，需要: {xp_required}"

            # 升級技能
            new_level = learned_skill.skill_level + 1
            new_xp = learned_skill.skill_xp - xp_required  # 消耗XP

            success = await self.global_skill_repo.update_learned_skill_progress(
                user_id, skill_id, skill_type, new_level, new_xp
            )

            if success:
                return True, f"成功升級技能 {skill_id} 到等級 {new_level}"
            else:
                return False, "更新技能進度失敗"

        except Exception as e:
            logger.error(f"升級全局技能失敗: {e}")
            return False, f"升級全局技能失敗: {str(e)}"

    async def add_xp_to_global_skill(
        self,
        user_id: int,
        skill_id: str,
        skill_type: Literal["ACTIVE", "PASSIVE"],
        xp_amount: int
    ) -> Tuple[Optional['LearnedGlobalSkillDBData'], str]:
        """
        為全局技能增加XP

        Args:
            user_id: 用戶ID
            skill_id: 技能ID
            skill_type: 技能類型
            xp_amount: 增加的XP數量

        Returns:
            (更新後的技能數據, 消息)
        """
        try:
            # 獲取玩家已學習的技能數據
            learned_skill = await self.global_skill_repo.get_learned_skill(user_id, skill_id, skill_type)
            if not learned_skill:
                return None, f"尚未學習該技能: {skill_id}"

            # 增加XP
            new_xp = learned_skill.skill_xp + xp_amount

            # 更新技能進度
            success = await self.global_skill_repo.update_learned_skill_progress(
                user_id, skill_id, skill_type, learned_skill.skill_level, new_xp
            )

            if success:
                # 獲取更新後的數據
                updated_skill = await self.global_skill_repo.get_learned_skill(user_id, skill_id, skill_type)
                return updated_skill, f"成功為技能 {skill_id} 增加 {xp_amount} XP"
            else:
                return None, "更新技能XP失敗"

        except Exception as e:
            logger.error(f"增加全局技能XP失敗: {e}")
            return None, f"增加全局技能XP失敗: {str(e)}"

    async def get_player_learned_skills(
        self,
        user_id: int,
        skill_type: Optional[Literal["ACTIVE", "PASSIVE"]] = None
    ) -> List[Dict[str, Any]]:
        """
        獲取玩家已學習的技能列表

        Args:
            user_id: 用戶ID
            skill_type: 技能類型過濾（可選）

        Returns:
            技能信息字典列表
        """
        try:
            # 獲取玩家技能列表
            learned_skills = await self.global_skill_repo.get_all_learned_skills_by_user_id(user_id, skill_type)

            result = []
            for skill in learned_skills:
                # 獲取技能配置信息
                if skill.skill_type == "ACTIVE":
                    skill_config = self.config_loader.get_active_skill_config(skill.skill_id)
                else:
                    skill_config = self.config_loader.get_passive_skill_config(skill.skill_id)

                skill_info = {
                    "skill_id": skill.skill_id,
                    "skill_type": skill.skill_type,
                    "current_level": skill.skill_level,
                    "current_xp": skill.skill_xp,
                    "unlocked_at": skill.unlocked_at.isoformat() if skill.unlocked_at else None,
                    "skill_name": getattr(skill_config, 'name', skill.skill_id) if skill_config else skill.skill_id,
                    "skill_description": getattr(skill_config, 'description', '') if skill_config else '',
                    "max_level": getattr(skill_config, 'max_level', 10) if skill_config else 10
                }

                # 計算升級所需XP
                if skill_config:
                    xp_required = self.get_skill_xp_requirements(skill_config, skill.skill_level)
                    skill_info["xp_to_next_level"] = xp_required
                else:
                    skill_info["xp_to_next_level"] = None

                result.append(skill_info)

            return result

        except Exception as e:
            logger.error(f"獲取玩家已學習技能列表失敗: {e}")
            return []

    def get_skill_xp_requirements(
        self,
        skill_config: Union['ActiveSkillConfig', 'PassiveSkillConfig'],
        current_level: int
    ) -> Optional[int]:
        """
        計算技能升級所需的XP

        Args:
            skill_config: 技能配置
            current_level: 當前等級

        Returns:
            升級所需XP或None
        """
        try:
            # 檢查是否已達到最大等級
            max_level = getattr(skill_config, 'max_level', 10)
            if current_level >= max_level:
                return None

            # 獲取XP需求配置
            xp_config = getattr(skill_config, 'xp_to_next_level_config', None)
            if not xp_config:
                # 如果沒有配置，使用默認公式：base_xp * level
                base_xp = getattr(skill_config, 'base_xp_requirement', 100)
                return base_xp * current_level

            # 如果有具體的XP配置，根據配置類型處理
            if isinstance(xp_config, dict):
                # 如果是字典，直接查找對應等級
                level_key = str(current_level + 1)  # 升到下一級
                return xp_config.get(level_key, 100 * current_level)
            elif isinstance(xp_config, list):
                # 如果是列表，使用索引
                if current_level < len(xp_config):
                    return xp_config[current_level]
                else:
                    # 超出列表範圍，使用最後一個值或默認公式
                    return xp_config[-1] if xp_config else 100 * current_level
            else:
                # 其他情況，使用默認公式
                return 100 * current_level

        except Exception as e:
            logger.error(f"計算技能XP需求失敗: {e}")
            return 100 * current_level  # 返回默認值
