"""
被動觸發處理器

負責監聽戰鬥中發生的事件，並根據被動技能的觸發條件來激活相應的被動效果
"""
from typing import List, Dict, Any, Optional, Tuple, TYPE_CHECKING
import logging
import random

if TYPE_CHECKING:
    from rpg_system.battle_system.models.combatant import Combatant
    from rpg_system.battle_system.models.battle import Battle
    from rpg_system.battle_system.models.skill_instance import SkillInstance
    from rpg_system.formula_engine.evaluator import FormulaEvaluator
    from rpg_system.config.loader import ConfigLoader
    from .effect_applier import EffectApplier
    from .target_selector import TargetSelector

logger = logging.getLogger(__name__)


class PassiveTriggerHandlerError(Exception):
    """被動觸發處理器異常"""
    pass


class PassiveTriggerHandler:
    """
    被動觸發處理器
    
    負責監聽戰鬥事件並觸發相應的被動技能效果
    """
    
    def __init__(
        self,
        effect_applier: 'EffectApplier',
        formula_evaluator: 'FormulaEvaluator',
        config_loader: 'ConfigLoader'
    ):
        """
        初始化被動觸發處理器
        
        Args:
            effect_applier: 效果應用器
            formula_evaluator: 公式求值器
            config_loader: 配置加載器
        """
        self.effect_applier = effect_applier
        self.formula_evaluator = formula_evaluator
        self.config_loader = config_loader
        
        # 記錄每場戰鬥中已觸發的一次性被動
        # 格式: {(combatant_id, passive_skill_id, effect_block_index): True}
        self._triggered_once_per_battle: Dict[Tuple[str, str, int], bool] = {}
    
    def handle_event(
        self,
        event_type: str,
        event_data: Dict[str, Any],
        battle_context: 'Battle'
    ) -> List[Dict[str, Any]]:
        """
        處理戰鬥事件，觸發相應的被動技能
        
        Args:
            event_type: 事件類型
            event_data: 事件數據
            battle_context: 戰鬥上下文
            
        Returns:
            被觸發的被動技能信息列表
            
        Raises:
            PassiveTriggerHandlerError: 當處理失敗時
        """
        try:
            logger.debug(
                "處理事件: 類型=%s, 數據=%s",
                event_type, {k: str(v) for k, v in event_data.items()}
            )
            
            # 1. 收集潛在的被動技能
            potential_passives = self._collect_potential_passives(
                battle_context, event_type
            )
            
            # 2. 過濾和排序被動技能
            triggered_passives = self._filter_and_sort_passives(
                potential_passives, event_type, event_data, battle_context
            )
            
            # 3. 執行被動效果
            execution_results = self._execute_passive_effects(
                triggered_passives, event_data, battle_context
            )
            
            logger.debug(
                "事件處理完成: 觸發被動數=%d", len(execution_results)
            )
            
            return execution_results
            
        except Exception as e:
            logger.error(
                "事件處理失敗: 類型=%s, 錯誤=%s",
                event_type, str(e)
            )
            raise PassiveTriggerHandlerError(
                f"事件處理失敗: {str(e)}"
            ) from e
    
    def reset_battle_triggers(self):
        """重置戰鬥觸發記錄（新戰鬥開始時調用）"""
        self._triggered_once_per_battle.clear()
        logger.debug("重置戰鬥觸發記錄")
    
    def _collect_potential_passives(
        self,
        battle_context: 'Battle',
        event_type: str
    ) -> List[Tuple['Combatant', 'SkillInstance', Dict[str, Any], int]]:
        """
        收集所有可能被觸發的被動技能
        
        Args:
            battle_context: 戰鬥上下文
            event_type: 事件類型
            
        Returns:
            (擁有者, 被動技能實例, 效果塊, 效果塊索引) 的列表
        """
        potential_passives = []
        
        # 遍歷所有存活的戰鬥者
        all_combatants = battle_context.get_alive_combatants()
        
        for combatant in all_combatants:
            # 獲取戰鬥者的所有被動技能
            passive_skills = self._get_combatant_passives(combatant)
            
            for passive_skill in passive_skills:
                try:
                    # 獲取被動技能配置
                    passive_config = passive_skill.get_definition(
                        self.config_loader, combatant.star_level
                    )
                    
                    # 獲取當前等級的效果塊
                    effect_blocks = self._get_effects_for_current_level(
                        passive_config, passive_skill.current_level
                    )
                    
                    # 檢查每個效果塊
                    for block_index, effect_block in enumerate(effect_blocks):
                        trigger_condition = effect_block.get("trigger_condition", {})
                        
                        # 檢查事件類型是否匹配
                        if trigger_condition.get("type") == event_type:
                            potential_passives.append((
                                combatant, passive_skill, effect_block, block_index
                            ))
                            
                except Exception as e:
                    logger.warning(
                        "獲取被動技能配置失敗: 戰鬥者=%s, 技能=%s, 錯誤=%s",
                        combatant.combatant_id, passive_skill.skill_id, str(e)
                    )
                    continue
        
        return potential_passives
    
    def _get_combatant_passives(self, combatant: 'Combatant') -> List['SkillInstance']:
        """
        獲取戰鬥者的所有被動技能
        
        Args:
            combatant: 戰鬥者
            
        Returns:
            被動技能實例列表
        """
        passives = []
        
        # 添加通用被動技能
        if hasattr(combatant, 'common_passives') and combatant.common_passives:
            passives.extend(combatant.common_passives)
        
        # 添加固有被動技能
        if hasattr(combatant, 'innate_passive_skill') and combatant.innate_passive_skill:
            passives.append(combatant.innate_passive_skill)
        
        return passives
    
    def _get_effects_for_current_level(
        self,
        passive_config: Dict[str, Any],
        current_level: int
    ) -> List[Dict[str, Any]]:
        """
        獲取當前等級的效果塊
        
        Args:
            passive_config: 被動技能配置
            current_level: 當前等級
            
        Returns:
            效果塊列表
        """
        # 根據配置結構獲取效果塊
        if "effects_by_level" in passive_config:
            level_effects = passive_config["effects_by_level"]
            if str(current_level) in level_effects:
                return level_effects[str(current_level)]
        
        # 如果沒有按等級分組的效果，返回默認效果
        return passive_config.get("effects", [])
    
    def _filter_and_sort_passives(
        self,
        potential_passives: List[Tuple['Combatant', 'SkillInstance', Dict[str, Any], int]],
        event_type: str,
        event_data: Dict[str, Any],
        battle_context: 'Battle'
    ) -> List[Tuple['Combatant', 'SkillInstance', Dict[str, Any], int, int]]:
        """
        過濾和排序被動技能
        
        Args:
            potential_passives: 潛在被動技能列表
            event_type: 事件類型
            event_data: 事件數據
            battle_context: 戰鬥上下文
            
        Returns:
            (擁有者, 被動技能實例, 效果塊, 效果塊索引, 優先級) 的列表
        """
        triggered_passives = []
        
        for combatant, passive_skill, effect_block, block_index in potential_passives:
            try:
                trigger_condition = effect_block.get("trigger_condition", {})
                
                # 檢查觸發源
                if not self._check_trigger_source(
                    trigger_condition, combatant, event_data
                ):
                    continue
                
                # 檢查子類型
                if not self._check_sub_type(
                    trigger_condition, event_data
                ):
                    continue
                
                # 檢查觸發機率
                if not self._check_trigger_chance(
                    trigger_condition, combatant, passive_skill, event_data
                ):
                    continue
                
                # 檢查額外條件
                if not self._check_additional_conditions(
                    trigger_condition, combatant, passive_skill, event_data
                ):
                    continue
                
                # 檢查一次性觸發限制
                if not self._check_once_per_battle(
                    combatant, passive_skill, block_index, effect_block
                ):
                    continue
                
                # 獲取優先級
                priority = trigger_condition.get("trigger_priority", 0)
                
                triggered_passives.append((
                    combatant, passive_skill, effect_block, block_index, priority
                ))
                
            except Exception as e:
                logger.warning(
                    "被動技能過濾失敗: 戰鬥者=%s, 技能=%s, 錯誤=%s",
                    combatant.combatant_id, passive_skill.skill_id, str(e)
                )
                continue
        
        # 按優先級排序（高優先級先執行）
        triggered_passives.sort(key=lambda x: x[4], reverse=True)
        
        return triggered_passives
    
    def _check_trigger_source(
        self,
        trigger_condition: Dict[str, Any],
        combatant: 'Combatant',
        event_data: Dict[str, Any]
    ) -> bool:
        """
        檢查觸發源條件
        
        Args:
            trigger_condition: 觸發條件
            combatant: 被動技能擁有者
            event_data: 事件數據
            
        Returns:
            是否滿足條件
        """
        source = trigger_condition.get("source", "ANY")
        
        if source == "ANY":
            return True
        elif source == "SELF":
            # 檢查事件是否與自己相關
            return (
                event_data.get("caster") == combatant or
                event_data.get("target") == combatant or
                event_data.get("combatant") == combatant or
                event_data.get("current_combatant") == combatant
            )
        elif source == "ALLY":
            # 檢查事件是否與盟友相關
            relevant_combatant = (
                event_data.get("target") or
                event_data.get("combatant") or
                event_data.get("current_combatant")
            )
            return (
                relevant_combatant and
                relevant_combatant != combatant and
                relevant_combatant.is_player == combatant.is_player
            )
        elif source == "ENEMY":
            # 檢查事件是否與敵人相關
            relevant_combatant = (
                event_data.get("target") or
                event_data.get("combatant") or
                event_data.get("current_combatant")
            )
            return (
                relevant_combatant and
                relevant_combatant.is_player != combatant.is_player
            )
        
        return False

    def _check_sub_type(
        self,
        trigger_condition: Dict[str, Any],
        event_data: Dict[str, Any]
    ) -> bool:
        """
        檢查子類型條件

        Args:
            trigger_condition: 觸發條件
            event_data: 事件數據

        Returns:
            是否滿足條件
        """
        sub_type = trigger_condition.get("sub_type")

        if not sub_type:
            return True

        # 檢查傷害類型
        damage_type = event_data.get("damage_type")
        if damage_type and sub_type in ["PHYSICAL", "MAGICAL", "TRUE"]:
            return damage_type == sub_type

        # 檢查元素類型
        source_element = event_data.get("source_element")
        if source_element:
            return source_element == sub_type

        # 檢查技能標籤
        skill_tags = event_data.get("skill_tags", [])
        if skill_tags:
            return sub_type in skill_tags

        return True

    def _check_trigger_chance(
        self,
        trigger_condition: Dict[str, Any],
        combatant: 'Combatant',
        passive_skill: 'SkillInstance',
        event_data: Dict[str, Any]
    ) -> bool:
        """
        檢查觸發機率

        Args:
            trigger_condition: 觸發條件
            combatant: 被動技能擁有者
            passive_skill: 被動技能實例
            event_data: 事件數據

        Returns:
            是否觸發
        """
        chance_formula = trigger_condition.get("chance_formula", "1.0")

        try:
            # 準備上下文變量
            context_vars = self._prepare_context_vars(
                combatant, passive_skill, event_data
            )

            # 計算觸發機率
            chance = self.formula_evaluator.evaluate(chance_formula, context_vars)
            chance = max(0.0, min(1.0, float(chance)))  # 限制在0-1之間

            # 隨機判斷
            return random.random() < chance

        except Exception as e:
            logger.warning(
                "觸發機率計算失敗: 公式=%s, 錯誤=%s",
                chance_formula, str(e)
            )
            return False

    def _check_additional_conditions(
        self,
        trigger_condition: Dict[str, Any],
        combatant: 'Combatant',
        passive_skill: 'SkillInstance',
        event_data: Dict[str, Any]
    ) -> bool:
        """
        檢查額外條件

        Args:
            trigger_condition: 觸發條件
            combatant: 被動技能擁有者
            passive_skill: 被動技能實例
            event_data: 事件數據

        Returns:
            是否滿足所有條件
        """
        additional_conditions = trigger_condition.get("additional_conditions", [])

        if not additional_conditions:
            return True

        # 準備上下文變量
        context_vars = self._prepare_context_vars(
            combatant, passive_skill, event_data
        )

        # 檢查每個額外條件
        for condition in additional_conditions:
            try:
                formula = condition.get("formula", "")
                if formula:
                    result = self.formula_evaluator.evaluate(formula, context_vars)
                    if not result:
                        return False
            except Exception as e:
                logger.warning(
                    "額外條件檢查失敗: 公式=%s, 錯誤=%s",
                    condition.get("formula", ""), str(e)
                )
                return False

        return True

    def _check_once_per_battle(
        self,
        combatant: 'Combatant',
        passive_skill: 'SkillInstance',
        block_index: int,
        effect_block: Dict[str, Any]
    ) -> bool:
        """
        檢查一次性觸發限制

        Args:
            combatant: 被動技能擁有者
            passive_skill: 被動技能實例
            block_index: 效果塊索引
            effect_block: 效果塊

        Returns:
            是否可以觸發
        """
        trigger_once_per_battle = effect_block.get("trigger_once_per_battle", False)

        if not trigger_once_per_battle:
            return True

        # 檢查是否已經觸發過
        trigger_key = (combatant.combatant_id, passive_skill.skill_id, block_index)
        return trigger_key not in self._triggered_once_per_battle

    def _prepare_context_vars(
        self,
        combatant: 'Combatant',
        passive_skill: 'SkillInstance',
        event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        準備公式上下文變量

        Args:
            combatant: 被動技能擁有者
            passive_skill: 被動技能實例
            event_data: 事件數據

        Returns:
            上下文變量字典
        """
        context_vars = {
            # 被動技能擁有者信息
            "caster": combatant,
            "caster_current_hp": combatant.current_hp,
            "caster_current_hp_percent": combatant.get_hp_percent(),
            "caster_current_mp": combatant.current_mp,
            "caster_current_mp_percent": combatant.get_mp_percent(),
            "caster_level": combatant.rpg_level,
            "caster_star_level": combatant.star_level,

            # 被動技能信息
            "passive_skill": passive_skill,
            "skill_level": passive_skill.current_level,
            "skill_id": passive_skill.skill_id,
        }

        # 添加被動技能擁有者的當前屬性
        caster_stats = combatant.get_current_stats()
        for attr_name in ["patk", "pdef", "matk", "mdef", "spd",
                         "crit_rate", "crit_dmg_multiplier", "accuracy", "evasion"]:
            context_vars[f"caster_stat_{attr_name}"] = getattr(caster_stats, attr_name, 0)

        # 添加事件數據
        context_vars.update(event_data)

        # 如果事件數據中有目標，添加目標相關信息
        target = event_data.get("target")
        if target:
            context_vars.update({
                "target_current_hp": target.current_hp,
                "target_current_hp_percent": target.get_hp_percent(),
                "target_current_mp": target.current_mp,
                "target_current_mp_percent": target.get_mp_percent(),
                "target_level": target.rpg_level,
                "target_star_level": target.star_level,
            })

            # 添加目標當前屬性
            target_stats = target.get_current_stats()
            for attr_name in ["patk", "pdef", "matk", "mdef", "spd",
                             "crit_rate", "crit_dmg_multiplier", "accuracy", "evasion"]:
                context_vars[f"target_stat_{attr_name}"] = getattr(target_stats, attr_name, 0)

        return context_vars

    def _execute_passive_effects(
        self,
        triggered_passives: List[Tuple['Combatant', 'SkillInstance', Dict[str, Any], int, int]],
        event_data: Dict[str, Any],
        battle_context: 'Battle'
    ) -> List[Dict[str, Any]]:
        """
        執行被動效果

        Args:
            triggered_passives: 被觸發的被動技能列表
            event_data: 事件數據
            battle_context: 戰鬥上下文

        Returns:
            執行結果列表
        """
        execution_results = []

        for combatant, passive_skill, effect_block, block_index, priority in triggered_passives:
            try:
                # 確定目標
                targets = self._determine_passive_targets(
                    combatant, effect_block, event_data, battle_context
                )

                if not targets:
                    logger.debug(
                        "被動技能無有效目標: 擁有者=%s, 技能=%s",
                        combatant.combatant_id, passive_skill.skill_id
                    )
                    continue

                # 獲取技能標籤
                passive_config = passive_skill.get_definition(
                    self.config_loader, combatant.star_level
                )
                skill_tags = passive_config.get("tags", [])

                # 應用效果
                effect_definitions = effect_block.get("effect_definitions", [])
                if effect_definitions:
                    log_entries = self.effect_applier.apply_effect_definitions(
                        caster=combatant,
                        initial_targets=targets,
                        effect_definitions=effect_definitions,
                        battle_context=battle_context,
                        source_skill_tags=skill_tags,
                        source_skill_instance=passive_skill,
                        custom_vars_from_source=event_data
                    )

                    # 記錄觸發（用於一次性被動）
                    if effect_block.get("trigger_once_per_battle", False):
                        trigger_key = (combatant.combatant_id, passive_skill.skill_id, block_index)
                        self._triggered_once_per_battle[trigger_key] = True

                    # 記錄執行結果
                    execution_result = {
                        "passive_owner": combatant.combatant_id,
                        "passive_skill_id": passive_skill.skill_id,
                        "effect_block_index": block_index,
                        "targets": [t.combatant_id for t in targets],
                        "log_entries": log_entries,
                        "priority": priority
                    }
                    execution_results.append(execution_result)

                    logger.debug(
                        "被動技能觸發成功: 擁有者=%s, 技能=%s, 目標=%s",
                        combatant.combatant_id, passive_skill.skill_id,
                        [t.combatant_id for t in targets]
                    )

            except Exception as e:
                logger.error(
                    "被動技能執行失敗: 擁有者=%s, 技能=%s, 錯誤=%s",
                    combatant.combatant_id, passive_skill.skill_id, str(e)
                )
                continue

        return execution_results

    def _determine_passive_targets(
        self,
        combatant: 'Combatant',
        effect_block: Dict[str, Any],
        event_data: Dict[str, Any],
        battle_context: 'Battle'
    ) -> List['Combatant']:
        """
        確定被動技能的目標

        Args:
            combatant: 被動技能擁有者
            effect_block: 效果塊
            event_data: 事件數據
            battle_context: 戰鬥上下文

        Returns:
            目標列表
        """
        # 檢查是否有目標覆蓋
        target_override = effect_block.get("target_override")

        if target_override:
            # 使用目標選擇器
            from .target_selector import TargetSelector
            target_selector = TargetSelector()

            return target_selector.select_targets(
                combatant, target_override, battle_context,
                self.formula_evaluator, self.config_loader
            )
        else:
            # 使用默認目標邏輯
            return self._get_default_passive_targets(
                combatant, effect_block, event_data
            )

    def _get_default_passive_targets(
        self,
        combatant: 'Combatant',
        effect_block: Dict[str, Any],
        event_data: Dict[str, Any]
    ) -> List['Combatant']:
        """
        獲取被動技能的默認目標

        Args:
            combatant: 被動技能擁有者
            effect_block: 效果塊
            event_data: 事件數據

        Returns:
            目標列表
        """
        # 根據效果塊配置確定默認目標
        default_target_key = effect_block.get("default_target_from_event_data", "target")

        # 嘗試從事件數據中獲取目標
        target = event_data.get(default_target_key)
        if target and hasattr(target, 'combatant_id'):
            return [target]

        # 如果沒有找到目標，嘗試其他常見的目標鍵
        for key in ["target", "caster", "combatant", "current_combatant"]:
            target = event_data.get(key)
            if target and hasattr(target, 'combatant_id'):
                return [target]

        # 最後默認為被動技能擁有者自己
        return [combatant]
