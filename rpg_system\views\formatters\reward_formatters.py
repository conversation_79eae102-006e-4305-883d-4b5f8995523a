"""
獎勵格式化工具

提供獎勵相關的文本格式化功能
"""
from typing import List, Dict, Any, Optional, TYPE_CHECKING
import logging

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader

logger = logging.getLogger(__name__)


def format_rewards(
    rewards_list: List[Dict[str, Any]], 
    config_loader: 'ConfigLoader'
) -> str:
    """
    將獎勵列表格式化為用戶友好的文本
    
    Args:
        rewards_list: 獎勵列表
        config_loader: 配置加載器
        
    Returns:
        格式化的獎勵文本
    """
    try:
        if not rewards_list:
            return "無獎勵"
        
        reward_lines = []
        
        for reward in rewards_list:
            reward_type = reward.get('type', 'unknown')
            reward_amount = reward.get('amount', 0)
            reward_item_id = reward.get('item_id', '')
            
            # 根據獎勵類型格式化
            if reward_type == 'xp':
                reward_lines.append(f"🌟 經驗值: +{reward_amount}")
            elif reward_type == 'gold':
                reward_lines.append(f"💰 金幣: +{reward_amount}")
            elif reward_type == 'skill_xp':
                skill_id = reward.get('skill_id', 'unknown')
                reward_lines.append(f"📚 技能經驗 ({skill_id}): +{reward_amount}")
            elif reward_type == 'item':
                item_name = get_item_name(reward_item_id, config_loader)
                reward_lines.append(f"🎁 {item_name}: x{reward_amount}")
            elif reward_type == 'card':
                card_name = get_card_name(reward_item_id, config_loader)
                reward_lines.append(f"🃏 卡牌: {card_name}")
            elif reward_type == 'currency':
                currency_name = reward.get('currency_name', '未知貨幣')
                reward_lines.append(f"💎 {currency_name}: +{reward_amount}")
            else:
                # 通用格式
                reward_lines.append(f"🎁 {reward_type}: +{reward_amount}")
        
        return "\n".join(reward_lines)
        
    except Exception as e:
        logger.error(f"格式化獎勵列表失敗: {e}")
        return "獎勵格式化錯誤"


def format_single_reward(
    reward: Dict[str, Any], 
    config_loader: 'ConfigLoader'
) -> str:
    """
    格式化單個獎勵
    
    Args:
        reward: 獎勵數據
        config_loader: 配置加載器
        
    Returns:
        格式化的獎勵文本
    """
    try:
        reward_type = reward.get('type', 'unknown')
        reward_amount = reward.get('amount', 0)
        reward_item_id = reward.get('item_id', '')
        
        if reward_type == 'xp':
            return f"🌟 經驗值 +{reward_amount}"
        elif reward_type == 'gold':
            return f"💰 金幣 +{reward_amount}"
        elif reward_type == 'skill_xp':
            skill_id = reward.get('skill_id', 'unknown')
            return f"📚 技能經驗 ({skill_id}) +{reward_amount}"
        elif reward_type == 'item':
            item_name = get_item_name(reward_item_id, config_loader)
            return f"🎁 {item_name} x{reward_amount}"
        elif reward_type == 'card':
            card_name = get_card_name(reward_item_id, config_loader)
            return f"🃏 {card_name}"
        else:
            return f"🎁 {reward_type} +{reward_amount}"
            
    except Exception as e:
        logger.error(f"格式化單個獎勵失敗: {e}")
        return "🎁 獎勵格式化錯誤"


def format_reward_summary(
    rewards_list: List[Dict[str, Any]], 
    config_loader: 'ConfigLoader'
) -> str:
    """
    格式化獎勵摘要（合併相同類型的獎勵）
    
    Args:
        rewards_list: 獎勵列表
        config_loader: 配置加載器
        
    Returns:
        格式化的獎勵摘要文本
    """
    try:
        if not rewards_list:
            return "無獎勵"
        
        # 合併相同類型的獎勵
        reward_summary = {}
        
        for reward in rewards_list:
            reward_type = reward.get('type', 'unknown')
            reward_amount = reward.get('amount', 0)
            
            if reward_type in reward_summary:
                reward_summary[reward_type] += reward_amount
            else:
                reward_summary[reward_type] = reward_amount
        
        # 格式化摘要
        summary_lines = []
        for reward_type, total_amount in reward_summary.items():
            if reward_type == 'xp':
                summary_lines.append(f"🌟 經驗值: +{total_amount}")
            elif reward_type == 'gold':
                summary_lines.append(f"💰 金幣: +{total_amount}")
            elif reward_type == 'skill_xp':
                summary_lines.append(f"📚 技能經驗: +{total_amount}")
            else:
                summary_lines.append(f"🎁 {reward_type}: +{total_amount}")
        
        return "\n".join(summary_lines)
        
    except Exception as e:
        logger.error(f"格式化獎勵摘要失敗: {e}")
        return "獎勵摘要格式化錯誤"


def get_item_name(item_id: str, config_loader: 'ConfigLoader') -> str:
    """
    獲取道具名稱
    
    Args:
        item_id: 道具ID
        config_loader: 配置加載器
        
    Returns:
        道具名稱
    """
    try:
        # 這裡應該從配置加載器獲取道具配置
        # 暫時返回ID作為名稱
        return item_id
        
    except Exception as e:
        logger.error(f"獲取道具名稱失敗: {e}")
        return item_id


def get_card_name(card_id: str, config_loader: 'ConfigLoader') -> str:
    """
    獲取卡牌名稱
    
    Args:
        card_id: 卡牌ID
        config_loader: 配置加載器
        
    Returns:
        卡牌名稱
    """
    try:
        card_config = config_loader.get_card_config(card_id)
        if card_config:
            return getattr(card_config, 'name', card_id)
        return card_id
        
    except Exception as e:
        logger.error(f"獲取卡牌名稱失敗: {e}")
        return card_id


def format_progress_updates(
    progress_updates: Dict[str, Any], 
    config_loader: 'ConfigLoader'
) -> str:
    """
    格式化進度更新信息
    
    Args:
        progress_updates: 進度更新數據
        config_loader: 配置加載器
        
    Returns:
        格式化的進度更新文本
    """
    try:
        if not progress_updates:
            return "無進度更新"
        
        update_lines = []
        
        # 樓層進度
        if 'floor_progress' in progress_updates:
            floor_data = progress_updates['floor_progress']
            current_floor = floor_data.get('current_floor', 1)
            wins = floor_data.get('wins', 0)
            update_lines.append(f"🏢 當前樓層: {current_floor}")
            update_lines.append(f"🏆 勝利次數: {wins}")
        
        # 等級提升
        if 'level_ups' in progress_updates:
            level_ups = progress_updates['level_ups']
            for level_up in level_ups:
                card_name = level_up.get('card_name', 'Unknown')
                new_level = level_up.get('new_level', 1)
                update_lines.append(f"⬆️ {card_name} 升級到 Lv.{new_level}")
        
        # 技能升級
        if 'skill_level_ups' in progress_updates:
            skill_level_ups = progress_updates['skill_level_ups']
            for skill_up in skill_level_ups:
                skill_name = skill_up.get('skill_name', 'Unknown')
                new_level = skill_up.get('new_level', 1)
                update_lines.append(f"📈 {skill_name} 升級到 Lv.{new_level}")
        
        # 解鎖內容
        if 'unlocks' in progress_updates:
            unlocks = progress_updates['unlocks']
            for unlock in unlocks:
                unlock_type = unlock.get('type', 'unknown')
                unlock_name = unlock.get('name', 'Unknown')
                update_lines.append(f"🔓 解鎖: {unlock_name}")
        
        return "\n".join(update_lines) if update_lines else "無進度更新"
        
    except Exception as e:
        logger.error(f"格式化進度更新失敗: {e}")
        return "進度更新格式化錯誤"


def format_battle_statistics(
    battle_stats: Dict[str, Any]
) -> str:
    """
    格式化戰鬥統計信息
    
    Args:
        battle_stats: 戰鬥統計數據
        
    Returns:
        格式化的戰鬥統計文本
    """
    try:
        if not battle_stats:
            return "無戰鬥統計"
        
        stats_lines = []
        
        # 基本統計
        turns = battle_stats.get('total_turns', 0)
        damage_dealt = battle_stats.get('total_damage_dealt', 0)
        damage_taken = battle_stats.get('total_damage_taken', 0)
        
        stats_lines.append(f"⚔️ 總回合數: {turns}")
        stats_lines.append(f"💥 造成傷害: {damage_dealt}")
        stats_lines.append(f"🩸 承受傷害: {damage_taken}")
        
        # 技能使用統計
        if 'skills_used' in battle_stats:
            skills_used = battle_stats['skills_used']
            stats_lines.append(f"🔮 技能使用次數: {skills_used}")
        
        # MVP
        if 'mvp' in battle_stats:
            mvp = battle_stats['mvp']
            stats_lines.append(f"🏆 MVP: {mvp}")
        
        return "\n".join(stats_lines)
        
    except Exception as e:
        logger.error(f"格式化戰鬥統計失敗: {e}")
        return "戰鬥統計格式化錯誤"
