"""
RPG Config Loader
Loads and validates all JSON configuration files using Pydantic models
"""
import json
import os
from typing import Dict, Any, Optional
from pathlib import Path
import logging

from .pydantic_models.active_skills_models import AllActiveSkillsConfig, ActiveSkillConfig
from .pydantic_models.passive_skills_models import AllPassiveSkillsConfig, PassiveSkillConfig
from .pydantic_models.innate_passive_skills_models import AllInnatePassiveSkillsConfig, InnatePassiveSkillConfig
from .pydantic_models.cards_models import AllCardsConfig, CardConfig
from .pydantic_models.status_effects_models import AllStatusEffectsConfig, StatusEffectConfig
from .pydantic_models.effect_templates_models import AllEffectTemplatesConfig
from .pydantic_models.star_level_effects_models import AllStarLevelEffectsConfig
from .pydantic_models.monsters_models import AllMonstersConfig, MonsterConfig
from .pydantic_models.floors_models import AllFloorsConfig, FloorConfig
from .pydantic_models.monster_groups_models import AllMonsterGroupsConfig
from .pydantic_models.reward_packages_models import AllRewardPackagesConfig

logger = logging.getLogger(__name__)

class ConfigLoader:
    """RPG配置加載器"""

    def __init__(self, config_dir: str):
        """
        初始化配置加載器

        Args:
            config_dir: 配置數據文件夾路徑 (e.g., rpg_system/config/data/)
        """
        self.config_dir = Path(config_dir)

        # 存儲各類配置的屬性
        self.active_skills: Dict[str, ActiveSkillConfig] = {}
        self.passive_skills: Dict[str, PassiveSkillConfig] = {}
        self.innate_passive_skills: Dict[str, InnatePassiveSkillConfig] = {}
        self.cards: Dict[str, CardConfig] = {}
        self.status_effects: Dict[str, StatusEffectConfig] = {}
        self.effect_templates: Dict[str, Any] = {}
        self.star_level_effects: Dict[str, Any] = {}
        self.monsters: Dict[str, MonsterConfig] = {}
        self.floors: Dict[str, FloorConfig] = {}
        self.monster_groups: Dict[str, Any] = {}
        self.reward_packages: Dict[str, Any] = {}

        # 配置文件映射
        self._config_mappings = {
            'active_skills.json': (AllActiveSkillsConfig, 'active_skills'),
            'passive_skills.json': (AllPassiveSkillsConfig, 'passive_skills'),
            'innate_passive_skills.json': (AllInnatePassiveSkillsConfig, 'innate_passive_skills'),
            'cards.json': (AllCardsConfig, 'cards'),
            'status_effects.json': (AllStatusEffectsConfig, 'status_effects'),
            'effect_templates.json': (AllEffectTemplatesConfig, 'effect_templates'),
            'star_level_effects.json': (AllStarLevelEffectsConfig, 'star_level_effects'),
            'monsters.json': (AllMonstersConfig, 'monsters'),
            'floors.json': (AllFloorsConfig, 'floors'),
            'monster_groups.json': (AllMonsterGroupsConfig, 'monster_groups'),
            'reward_packages.json': (AllRewardPackagesConfig, 'reward_packages'),
        }

    def load_all_configs(self) -> bool:
        """
        加載所有配置文件

        Returns:
            bool: 是否全部加載成功
        """
        success = True

        for filename, (model_class, attr_name) in self._config_mappings.items():
            try:
                file_path = self.config_dir / filename

                if not file_path.exists():
                    logger.warning(f"配置文件不存在: {file_path}")
                    continue

                # 讀取文件內容
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 使用對應的 Pydantic 主模型進行解析和驗證
                if isinstance(model_class, type) and hasattr(model_class, '__annotations__'):
                    # 對於 Dict 類型的模型，直接驗證
                    validated_data = {}
                    for key, value in data.items():
                        # 獲取字典值的類型
                        value_type = model_class.__args__[1] if hasattr(model_class, '__args__') else None
                        if value_type:
                            validated_data[key] = value_type(**value)
                        else:
                            validated_data[key] = value
                else:
                    # 對於其他類型，直接使用模型驗證
                    validated_data = data

                # 存儲到對應的類屬性中
                setattr(self, attr_name, validated_data)
                logger.info(f"成功加載配置文件: {filename}")

            except Exception as e:
                logger.error(f"加載配置文件 {filename} 失敗: {e}")
                success = False

        return success

    # 提供獲取特定配置的方法
    def get_card_config(self, card_id: str) -> Optional[CardConfig]:
        """獲取卡牌配置"""
        return self.cards.get(card_id)

    def get_active_skill_config(self, skill_id: str) -> Optional[ActiveSkillConfig]:
        """獲取主動技能配置"""
        return self.active_skills.get(skill_id)

    def get_passive_skill_config(self, skill_id: str) -> Optional[PassiveSkillConfig]:
        """獲取被動技能配置"""
        return self.passive_skills.get(skill_id)

    def get_innate_passive_skill_config(self, skill_id: str) -> Optional[InnatePassiveSkillConfig]:
        """獲取天賦被動技能配置"""
        return self.innate_passive_skills.get(skill_id)

    def get_status_effect_config(self, effect_id: str) -> Optional[StatusEffectConfig]:
        """獲取狀態效果配置"""
        return self.status_effects.get(effect_id)

    def get_monster_config(self, monster_id: str) -> Optional[MonsterConfig]:
        """獲取怪物配置"""
        return self.monsters.get(monster_id)

    def get_floor_config(self, floor_id: str) -> Optional[FloorConfig]:
        """獲取樓層配置"""
        return self.floors.get(floor_id)

    def get_effect_template(self, template_id: str) -> Optional[Any]:
        """獲取效果模板"""
        return self.effect_templates.get(template_id)

    def get_star_level_effects(self, effects_key: str) -> Optional[Any]:
        """獲取星級效果"""
        return self.star_level_effects.get(effects_key)

    def get_monster_group(self, group_id: str) -> Optional[Any]:
        """獲取怪物群組"""
        return self.monster_groups.get(group_id)

    def get_reward_package(self, package_id: str) -> Optional[Any]:
        """獲取獎勵包"""
        return self.reward_packages.get(package_id)
