"""
戰鬥模型
"""
from typing import List, Dict, Optional, Any, TYPE_CHECKING
import logging
from datetime import datetime
import uuid
import random

from .enums import BattleStatus
from .combatant import Combatant
from .battle_log import BattleLogEntry
from ..events import (
    EVENT_ON_BATTLE_START, EVENT_ON_BATTLE_END, EVENT_ON_TURN_START, EVENT_ON_TURN_END,
    EVENT_ON_BEFORE_SKILL_USE, EVENT_ON_AFTER_SKILL_USE,
    EventOnBattleStartData, EventOnBattleEndData, EventOnTurnStartData, EventOnTurnEndData,
    EventOnBeforeSkillUseData, EventOnAfterSkillUseData
)

if TYPE_CHECKING:
    from rpg_system.config.loader import ConfigLoader
    from rpg_system.battle_system.handlers.effect_applier import EffectApplier
    from rpg_system.battle_system.handlers.target_selector import TargetSelector
    from rpg_system.battle_system.handlers.passive_trigger_handler import PassiveTriggerHandler
    from rpg_system.formula_engine.evaluator import FormulaEvaluator

logger = logging.getLogger(__name__)


class BattleError(Exception):
    """戰鬥系統異常"""
    pass


class Battle:
    """
    戰鬥類

    管理整個戰鬥的狀態和流程
    """

    def __init__(
        self,
        player_team: List[Combatant],
        monster_team: List[Combatant],
        config_loader: 'ConfigLoader',
        effect_applier: 'EffectApplier',
        target_selector: 'TargetSelector',
        passive_trigger_handler: 'PassiveTriggerHandler',
        formula_evaluator: 'FormulaEvaluator',
        rng_seed: Optional[int] = None,
        battle_id: Optional[str] = None
    ):
        """
        初始化戰鬥

        Args:
            player_team: 玩家隊伍
            monster_team: 怪物隊伍
            config_loader: 配置加載器
            effect_applier: 效果應用器
            target_selector: 目標選擇器
            passive_trigger_handler: 被動觸發處理器
            formula_evaluator: 公式求值器
            rng_seed: 隨機種子
            battle_id: 戰鬥ID，如果為None則自動生成
        """
        # 基本信息
        self.battle_id = battle_id or str(uuid.uuid4())

        # 戰鬥狀態
        self.battle_status = BattleStatus.PENDING
        self.current_turn = 0
        self.max_turns = 100  # 最大回合數限制

        # 參戰隊伍
        self.player_team = player_team.copy()
        self.monster_team = monster_team.copy()

        # 行動順序管理
        self.combatant_queue: List[str] = []  # 存儲combatant instance_id
        self._acting_combatant_index = 0

        # 戰鬥日誌
        self.battle_log: List[BattleLogEntry] = []

        # 依賴服務
        self.config_loader = config_loader
        self.effect_applier = effect_applier
        self.target_selector = target_selector
        self.passive_trigger_handler = passive_trigger_handler
        self.formula_evaluator = formula_evaluator

        # 隨機數生成器
        self._rng = random.Random(rng_seed)

        # 時間戳
        self.created_at = datetime.now()
        self.started_at: Optional[datetime] = None
        self.ended_at: Optional[datetime] = None
    
    def start(self):
        """
        啟動戰鬥

        Raises:
            BattleError: 當戰鬥無法啟動時
        """
        if self.battle_status != BattleStatus.PENDING:
            raise BattleError(f"無法啟動戰鬥，當前狀態: {self.battle_status}")

        try:
            logger.info(f"啟動戰鬥: {self.battle_id}")

            # 設置戰鬥狀態
            self.battle_status = BattleStatus.IN_PROGRESS
            self.current_turn = 1
            self.started_at = datetime.now()

            # 計算初始行動順序
            self._calculate_initial_combatant_queue()
            self._acting_combatant_index = 0

            # 觸發戰鬥開始事件
            event_data = EventOnBattleStartData(battle_context=self)
            self.dispatch_event(EVENT_ON_BATTLE_START, event_data)

            # 應用戰鬥開始時的星級效果
            self._apply_battle_start_star_effects()

            # 記錄戰鬥開始日誌
            self.add_log_entry(BattleLogEntry.create_battle_start(
                self.battle_id, len(self.player_team), len(self.monster_team)
            ))

            logger.info(f"戰鬥 {self.battle_id} 已啟動")

        except Exception as e:
            logger.error(f"戰鬥啟動失敗: {e}")
            self.battle_status = BattleStatus.PENDING
            raise BattleError(f"戰鬥啟動失敗: {str(e)}") from e

    def _calculate_initial_combatant_queue(self):
        """
        計算初始行動順序

        根據速度屬性排序，速度高的先行動
        """
        live_combatants = self.get_all_alive_combatants()

        # 按速度降序排列，速度相同時按玩家優先，然後隨機
        def sort_key(combatant):
            spd = combatant.get_stat('spd')
            is_player_priority = 1 if combatant.is_player_side else 0
            random_tie_breaker = self._rng.random()
            return (spd, is_player_priority, random_tie_breaker)

        sorted_combatants = sorted(live_combatants, key=sort_key, reverse=True)
        self.combatant_queue = [c.instance_id for c in sorted_combatants]

        logger.debug(f"行動順序計算完成: {[c.name for c in sorted_combatants]}")

    def _apply_battle_start_star_effects(self):
        """應用戰鬥開始時的星級效果"""
        all_combatants = self.player_team + self.monster_team

        for combatant in all_combatants:
            if not combatant.is_player_side or combatant.star_level <= 0:
                continue

            try:
                card_config = self.config_loader.get_card_config(combatant.definition_id)
                if not card_config:
                    continue

                star_effects_key = card_config.get('star_level_effects_key')
                if not star_effects_key:
                    continue

                star_level_effects_config_group = self.config_loader.get_star_level_effects_config(star_effects_key)
                if not star_level_effects_config_group:
                    continue

                for s_level in range(1, combatant.star_level + 1):
                    star_effect_detail = star_level_effects_config_group.get(str(s_level))
                    if not star_effect_detail:
                        continue

                    battle_start_effects = star_effect_detail.get('apply_self_effect_on_battle_start')
                    if battle_start_effects:
                        self.effect_applier.apply_effect_definitions(
                            caster=combatant,
                            initial_targets=[combatant],
                            effect_definitions=battle_start_effects,
                            battle_context=self,
                            source_skill_tags=["STAR_LEVEL_EFFECT"],
                            source_skill_instance=None
                        )

            except Exception as e:
                logger.warning(f"應用戰鬥開始星級效果失敗: {combatant.name}, 錯誤: {e}")

    def get_all_alive_combatants(self) -> List[Combatant]:
        """獲取所有存活的戰鬥者"""
        all_combatants = self.player_team + self.monster_team
        return [c for c in all_combatants if c.is_alive()]

    def get_all_alive_enemies_of(self, combatant: Combatant) -> List[Combatant]:
        """獲取指定戰鬥者的所有存活敵人"""
        if combatant.is_player_side:
            return [c for c in self.monster_team if c.is_alive()]
        else:
            return [c for c in self.player_team if c.is_alive()]

    def get_all_alive_allies_of(self, combatant: Combatant) -> List[Combatant]:
        """獲取指定戰鬥者的所有存活盟友（包括自身）"""
        if combatant.is_player_side:
            return [c for c in self.player_team if c.is_alive()]
        else:
            return [c for c in self.monster_team if c.is_alive()]

    def get_combatant_by_id(self, instance_id: str) -> Optional[Combatant]:
        """
        根據instance_id獲取戰鬥者

        Args:
            instance_id: 戰鬥者實例ID

        Returns:
            戰鬥者實例，如果不存在則返回None
        """
        for combatant in self.player_team + self.monster_team:
            if combatant.instance_id == instance_id:
                return combatant
        return None
    
    def get_acting_combatant(self) -> Optional[Combatant]:
        """獲取當前行動者"""
        if (not self.combatant_queue or
            not (0 <= self._acting_combatant_index < len(self.combatant_queue))):
            return None

        acting_id = self.combatant_queue[self._acting_combatant_index]
        return self.get_combatant_by_id(acting_id)

    def next_turn_or_combatant(self) -> Optional[Combatant]:
        """
        推進到下一個行動者或下一回合

        Returns:
            新的行動者，如果戰鬥結束則返回None
        """
        # 1. 當前行動者回合結束效果
        acting_combatant = self.get_acting_combatant()
        if acting_combatant and acting_combatant.is_alive():
            self._apply_turn_end_effects(acting_combatant)

        # 2. 推進到下一個行動者
        self._acting_combatant_index += 1

        # 3. 檢查是否一個邏輯回合結束
        if self._acting_combatant_index >= len(self.combatant_queue):
            self.current_turn += 1
            self.add_log_entry(BattleLogEntry.create_turn_start(self.current_turn))

            # 重新計算行動順序
            self._calculate_initial_combatant_queue()
            self._acting_combatant_index = 0

            # 如果沒有存活的戰鬥者，檢查勝負條件
            if not self.combatant_queue:
                self.check_win_condition()
                return None

        # 4. 獲取新的行動者
        new_acting_combatant = self.get_acting_combatant()

        # 5. 新行動者回合開始效果
        if new_acting_combatant and new_acting_combatant.is_alive():
            self._apply_turn_start_effects(new_acting_combatant)

        # 6. 檢查戰鬥是否結束
        self.check_win_condition()

        return new_acting_combatant if self.battle_status == BattleStatus.IN_PROGRESS else None

    def _apply_turn_end_effects(self, combatant: Combatant):
        """應用回合結束效果"""
        try:
            # 觸發回合結束事件
            event_data = EventOnTurnEndData(
                battle_context=self,
                combatant=combatant,
                turn_number=self.current_turn
            )
            self.dispatch_event(EVENT_ON_TURN_END, event_data)

            # 應用狀態效果tick
            combatant.apply_turn_end_effects(self, self.config_loader)

        except Exception as e:
            logger.error(f"應用回合結束效果失敗: {combatant.name}, 錯誤: {e}")

    def _apply_turn_start_effects(self, combatant: Combatant):
        """應用回合開始效果"""
        try:
            # 觸發回合開始事件
            event_data = EventOnTurnStartData(
                battle_context=self,
                combatant=combatant,
                turn_number=self.current_turn
            )
            self.dispatch_event(EVENT_ON_TURN_START, event_data)

            # 應用狀態效果tick和冷卻減少
            combatant.apply_turn_start_effects(self, self.config_loader)

        except Exception as e:
            logger.error(f"應用回合開始效果失敗: {combatant.name}, 錯誤: {e}")
    
    def process_action(
        self,
        caster: Combatant,
        skill_id: str,
        explicit_target_ids: Optional[List[str]] = None
    ):
        """
        處理戰鬥行動

        Args:
            caster: 行動的戰鬥單位
            skill_id: 使用的技能ID
            explicit_target_ids: 明確指定的目標ID列表

        Raises:
            BattleError: 當行動處理失敗時
        """
        try:
            logger.debug(f"處理行動: {caster.name} 使用 {skill_id}")

            # 1. 獲取技能實例和定義
            skill_instance = caster.get_skill_instance(skill_id)
            if not skill_instance:
                logger.warning(f"技能實例不存在: {skill_id}")
                return

            skill_definition = skill_instance.get_definition(self.config_loader)
            if not skill_definition:
                logger.warning(f"技能定義不存在: {skill_id}")
                return

            # 2. 檢查技能可用性
            if not skill_instance.is_usable(caster.current_mp, self.config_loader):
                self.add_log_entry(BattleLogEntry.create_skill_unusable(
                    caster.name, skill_definition.get('name', skill_id)
                ))
                # 可選：執行普攻作為替代
                return

            # 3. 消耗資源，技能進入冷卻
            mp_cost = skill_definition.get('mp_cost', 0)
            caster.consume_mp(mp_cost)
            skill_instance.put_on_cooldown(self.config_loader)

            # 4. 觸發技能使用前事件
            event_data = EventOnBeforeSkillUseData(
                battle_context=self,
                caster=caster,
                skill_instance=skill_instance,
                intended_targets=explicit_target_ids or [],
                mp_consumed=mp_cost
            )
            self.dispatch_event(EVENT_ON_BEFORE_SKILL_USE, event_data)

            # 5. 選擇目標
            if explicit_target_ids:
                selected_targets = [
                    self.get_combatant_by_id(tid)
                    for tid in explicit_target_ids
                    if self.get_combatant_by_id(tid) and self.get_combatant_by_id(tid).is_alive()
                ]
            else:
                target_logic_detail = skill_definition.get('target_logic', {})
                selected_targets = self.target_selector.select_targets(
                    caster, target_logic_detail, self,
                    self.formula_evaluator, self.config_loader
                )

            # 6. 記錄技能施放日誌
            self.add_log_entry(BattleLogEntry.create_skill_cast(
                caster.name, skill_definition.get('name', skill_id),
                [t.name for t in selected_targets]
            ))

            # 7. 應用技能效果
            self.effect_applier.apply_skill_effects(
                caster, selected_targets, skill_instance, self
            )

            # 8. 觸發技能使用後事件
            event_data = EventOnAfterSkillUseData(
                battle_context=self,
                caster=caster,
                skill_instance=skill_instance,
                actual_targets=selected_targets,
                mp_consumed=mp_cost
            )
            self.dispatch_event(EVENT_ON_AFTER_SKILL_USE, event_data)

            # 9. 檢查勝負條件
            self.check_win_condition()

        except Exception as e:
            logger.error(f"行動處理失敗: {caster.name}, 技能: {skill_id}, 錯誤: {e}")
            raise BattleError(f"行動處理失敗: {str(e)}") from e

    def check_win_condition(self):
        """檢查勝負條件"""
        if self.battle_status != BattleStatus.IN_PROGRESS:
            return

        player_team_alive = any(c.is_alive() for c in self.player_team)
        monster_team_alive = any(c.is_alive() for c in self.monster_team)

        new_status = None

        if not player_team_alive and not monster_team_alive:
            new_status = BattleStatus.DRAW
        elif not player_team_alive:
            new_status = BattleStatus.MONSTER_WIN
        elif not monster_team_alive:
            new_status = BattleStatus.PLAYER_WIN
        elif self.current_turn > self.max_turns:
            new_status = BattleStatus.DRAW

        if new_status is not None:
            self.battle_status = new_status
            self.ended_at = datetime.now()

            # 記錄戰鬥結束日誌
            self.add_log_entry(BattleLogEntry.create_battle_end(
                self.battle_id, new_status.value
            ))

            # 觸發戰鬥結束事件
            winning_side = None
            if new_status == BattleStatus.PLAYER_WIN:
                winning_side = "player"
            elif new_status == BattleStatus.MONSTER_WIN:
                winning_side = "monster"

            event_data = EventOnBattleEndData(
                battle_context=self,
                winning_side=winning_side
            )
            self.dispatch_event(EVENT_ON_BATTLE_END, event_data)

            logger.info(f"戰鬥結束: {self.battle_id}, 結果: {new_status.value}")
    
    def dispatch_event(self, event_type: str, event_data: Dict[str, Any]):
        """
        派發事件

        Args:
            event_type: 事件類型
            event_data: 事件數據
        """
        try:
            if self.passive_trigger_handler:
                self.passive_trigger_handler.handle_event(
                    event_type, event_data, self
                )
        except Exception as e:
            logger.error(f"事件派發失敗: 類型={event_type}, 錯誤={e}")

    def add_log_entry(self, entry: BattleLogEntry):
        """
        添加戰鬥日誌條目

        Args:
            entry: 日誌條目
        """
        self.battle_log.append(entry)

        # 可選：限制日誌長度以防止內存過度使用
        if len(self.battle_log) > 1000:
            self.battle_log = self.battle_log[-500:]  # 保留最後500條

    def get_battle_summary(self) -> Dict[str, Any]:
        """
        獲取戰鬥摘要

        Returns:
            戰鬥摘要字典
        """
        return {
            "battle_id": self.battle_id,
            "status": self.battle_status.value,
            "current_turn": self.current_turn,
            "max_turns": self.max_turns,
            "player_team": [
                {
                    "instance_id": c.instance_id,
                    "name": c.name,
                    "hp": f"{c.current_hp}/{c.get_stat('max_hp')}",
                    "mp": f"{c.current_mp}/{c.get_stat('max_mp')}",
                    "alive": c.is_alive()
                }
                for c in self.player_team
            ],
            "monster_team": [
                {
                    "instance_id": c.instance_id,
                    "name": c.name,
                    "hp": f"{c.current_hp}/{c.get_stat('max_hp')}",
                    "mp": f"{c.current_mp}/{c.get_stat('max_mp')}",
                    "alive": c.is_alive()
                }
                for c in self.monster_team
            ],
            "acting_combatant": self.get_acting_combatant().name if self.get_acting_combatant() else None,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "ended_at": self.ended_at.isoformat() if self.ended_at else None,
            "log_entries_count": len(self.battle_log)
        }

    def get_alive_combatants(self, player_side: Optional[bool] = None) -> List[Combatant]:
        """
        獲取存活的戰鬥者（兼容舊接口）

        Args:
            player_side: True為玩家方，False為怪物方，None為全部

        Returns:
            存活的戰鬥者列表
        """
        if player_side is None:
            return self.get_all_alive_combatants()
        elif player_side:
            return [c for c in self.player_team if c.is_alive()]
        else:
            return [c for c in self.monster_team if c.is_alive()]

    def __repr__(self) -> str:
        return (f"Battle(id='{self.battle_id}', status={self.battle_status.value}, "
                f"turn={self.current_turn}, players={len(self.player_team)}, "
                f"monsters={len(self.monster_team)})")

    def __str__(self) -> str:
        return f"Battle {self.battle_id} - Turn {self.current_turn} ({self.battle_status.value})"
