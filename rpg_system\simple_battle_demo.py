"""
簡單戰鬥系統演示
"""
import sys
sys.path.append('.')

from unittest.mock import Mock
from rpg_system.battle_system.models.battle import Battle
from rpg_system.battle_system.models.combatant import Combatant, CombatantStats
from rpg_system.battle_system.models.enums import SkillType
from rpg_system.battle_system.models.skill_instance import SkillInstance


def main():
    """簡單演示"""
    print("=== 簡單戰鬥系統演示 ===")
    
    # 創建模擬依賴
    config_loader = Mock()
    effect_applier = Mock()
    target_selector = Mock()
    passive_trigger_handler = Mock()
    formula_evaluator = Mock()
    
    # 模擬配置
    config_loader.get_card_config.return_value = {}
    
    # 創建戰鬥者
    player_stats = CombatantStats(
        hp=100, max_mp=50, mp_regen_per_turn=5,
        patk=20, pdef=10, matk=15, mdef=8,
        spd=12, crit_rate=0.1, crit_dmg_multiplier=1.5,
        accuracy=0.9, evasion=0.1
    )
    
    monster_stats = CombatantStats(
        hp=80, max_mp=30, mp_regen_per_turn=3,
        patk=18, pdef=8, matk=12, mdef=6,
        spd=10, crit_rate=0.05, crit_dmg_multiplier=1.3,
        accuracy=0.85, evasion=0.05
    )
    
    player = Combatant(
        combatant_id="player_1",
        name="勇者",
        stats=player_stats,
        is_player=True,
        card_id="hero",
        definition_id="hero"
    )
    
    monster = Combatant(
        combatant_id="monster_1",
        name="哥布林",
        stats=monster_stats,
        is_player=False,
        definition_id="goblin"
    )
    
    # 添加技能
    player_attack = SkillInstance(
        skill_id="basic_attack",
        skill_type=SkillType.PRIMARY_ATTACK,
        current_level=1,
        current_cooldown=0
    )
    player.add_skill(player_attack)
    
    monster_attack = SkillInstance(
        skill_id="monster_attack",
        skill_type=SkillType.PRIMARY_ATTACK,
        current_level=1,
        current_cooldown=0
    )
    monster.add_skill(monster_attack)
    monster.skill_order_preference = ["monster_attack"]
    
    # 創建戰鬥
    battle = Battle(
        player_team=[player],
        monster_team=[monster],
        config_loader=config_loader,
        effect_applier=effect_applier,
        target_selector=target_selector,
        passive_trigger_handler=passive_trigger_handler,
        formula_evaluator=formula_evaluator,
        rng_seed=42
    )
    
    print(f"戰鬥ID: {battle.battle_id}")
    print(f"初始狀態: {battle.battle_status.value}")
    
    # 啟動戰鬥
    print("\n啟動戰鬥...")
    battle.start()
    print(f"戰鬥狀態: {battle.battle_status.value}")
    print(f"當前回合: {battle.current_turn}")
    
    # 顯示行動順序
    acting_combatant = battle.get_acting_combatant()
    if acting_combatant:
        print(f"當前行動者: {acting_combatant.name}")
        print(f"  速度: {acting_combatant.get_stat('spd')}")
        print(f"  HP: {acting_combatant.current_hp}/{acting_combatant.get_stat('hp')}")
        print(f"  MP: {acting_combatant.current_mp}/{acting_combatant.get_stat('max_mp')}")
    
    # 測試推進回合
    print("\n推進到下一個行動者...")
    next_combatant = battle.next_turn_or_combatant()
    if next_combatant:
        print(f"下一個行動者: {next_combatant.name}")
        print(f"  速度: {next_combatant.get_stat('spd')}")
    
    # 測試勝負條件
    print("\n測試勝負條件...")
    print("殺死怪物...")
    monster.take_damage(1000)
    battle.check_win_condition()
    print(f"戰鬥結果: {battle.battle_status.value}")
    
    # 顯示戰鬥摘要
    print("\n=== 戰鬥摘要 ===")
    summary = battle.get_battle_summary()
    print(f"戰鬥ID: {summary['battle_id']}")
    print(f"狀態: {summary['status']}")
    print(f"回合數: {summary['current_turn']}")
    print(f"日誌條目: {summary['log_entries_count']}")
    
    print("\n最終狀態:")
    for team_name, team_data in [("玩家隊伍", summary['player_team']), ("怪物隊伍", summary['monster_team'])]:
        print(f"  {team_name}:")
        for member in team_data:
            print(f"    {member['name']}: {member['hp']} HP ({'存活' if member['alive'] else '死亡'})")
    
    print("\n=== 戰鬥系統核心功能驗證完成 ===")
    print("✅ 戰鬥初始化")
    print("✅ 戰鬥啟動")
    print("✅ 行動順序計算")
    print("✅ 回合推進")
    print("✅ 勝負條件判斷")
    print("✅ 戰鬥摘要生成")


if __name__ == "__main__":
    main()
