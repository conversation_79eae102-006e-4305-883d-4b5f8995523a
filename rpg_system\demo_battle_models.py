#!/usr/bin/env python3
"""
RPG戰鬥系統核心模型演示腳本
展示SkillInstance、Combatant、Battle等核心模型的功能
"""
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

from rpg_system.battle_system.models import (
    SkillType, BattleStatus, SkillInstance, Combatant, 
    CombatantStats, BattleLogEntry, Battle
)


def demo_skill_instance():
    """演示技能實例功能"""
    print("=== 技能實例演示 ===")
    
    # 創建不同類型的技能
    fireball = SkillInstance("fireball", SkillType.ACTIVE, 5, 0)
    heal = SkillInstance("heal", SkillType.ACTIVE, 3, 2)
    strength = SkillInstance("strength", SkillType.PASSIVE, 2, 0)
    fire_mastery = SkillInstance("fire_mastery", SkillType.INNATE_PASSIVE, 1, 0)
    basic_attack = SkillInstance("basic_attack", SkillType.PRIMARY_ATTACK, 1, 0)
    
    skills = [fireball, heal, strength, fire_mastery, basic_attack]
    
    print("創建的技能:")
    for skill in skills:
        print(f"  {skill}")
    
    print("\n冷卻機制演示:")
    print(f"治療術初始冷卻: {heal.current_cooldown}")
    
    for turn in range(1, 4):
        heal.tick_cooldown()
        print(f"第{turn}回合後冷卻: {heal.current_cooldown}")
    
    print()


def demo_combatant():
    """演示戰鬥者功能"""
    print("=== 戰鬥者演示 ===")
    
    # 創建戰鬥者屬性
    hero_stats = CombatantStats(
        hp=120, max_mp=60, mp_regen_per_turn=8,
        patk=85, pdef=45, matk=70, mdef=35,
        spd=75, crit_rate=0.15, crit_dmg_multiplier=1.6,
        accuracy=0.95, evasion=0.08
    )
    
    # 創建英雄
    hero = Combatant(
        combatant_id="hero_001",
        name="火焰法師",
        stats=hero_stats,
        is_player=True,
        card_id="card_fire_mage",
        star_level=4,
        rpg_level=15
    )
    
    print(f"創建戰鬥者: {hero}")
    print(f"血量百分比: {hero.get_hp_percent():.1%}")
    print(f"MP百分比: {hero.get_mp_percent():.1%}")
    
    # 添加技能
    fireball = SkillInstance("fireball", SkillType.ACTIVE, 5)
    heal = SkillInstance("heal", SkillType.ACTIVE, 3)
    basic_attack = SkillInstance("basic_attack", SkillType.PRIMARY_ATTACK, 1)
    fire_mastery = SkillInstance("fire_mastery", SkillType.INNATE_PASSIVE, 1)
    
    hero.add_skill(fireball)
    hero.add_skill(heal)
    hero.add_skill(basic_attack)
    hero.add_skill(fire_mastery)
    
    print(f"\n技能配置:")
    print(f"  普攻: {hero.primary_attack}")
    print(f"  主動技能: {[str(s) for s in hero.active_skills]}")
    print(f"  天賦被動: {hero.innate_passive}")
    
    # 戰鬥演示
    print(f"\n戰鬥演示:")
    print(f"初始狀態: {hero}")
    
    # 恢復MP
    hero.restore_mp(40)
    print(f"恢復MP後: HP={hero.current_hp}, MP={hero.current_mp}")
    
    # 受到傷害
    damage_taken = hero.take_damage(35)
    print(f"受到{damage_taken}點傷害後: {hero}")
    
    # 治療
    heal_amount = hero.heal(20)
    print(f"治療{heal_amount}點後: {hero}")
    
    # 消耗MP
    mp_consumed = hero.consume_mp(25)
    print(f"消耗25MP {'成功' if mp_consumed else '失敗'}: MP={hero.current_mp}")
    
    # MP自然恢復
    hero.regenerate_mp()
    print(f"MP自然恢復後: MP={hero.current_mp}")
    
    print()


def demo_battle_log():
    """演示戰鬥日誌功能"""
    print("=== 戰鬥日誌演示 ===")
    
    # 創建不同類型的日誌條目
    skill_use = BattleLogEntry.create_skill_use(
        turn_number=1,
        actor_id="hero_001",
        skill_id="fireball",
        target_ids=["monster_001", "monster_002"],
        mp_consumed=20
    )
    
    damage_log = BattleLogEntry.create_damage(
        turn_number=1,
        actor_id="hero_001",
        target_id="monster_001",
        damage=45,
        skill_id="fireball"
    )
    
    healing_log = BattleLogEntry.create_healing(
        turn_number=2,
        actor_id="hero_002",
        target_id="hero_001",
        healing=30,
        skill_id="heal"
    )
    
    status_log = BattleLogEntry.create_status_effect(
        turn_number=3,
        actor_id="monster_001",
        target_id="hero_001",
        effect_id="poison",
        applied=True,
        skill_id="poison_bite"
    )
    
    turn_start = BattleLogEntry.create_turn_start(4)
    
    logs = [skill_use, damage_log, healing_log, status_log, turn_start]
    
    print("戰鬥日誌條目:")
    for log in logs:
        print(f"  {log}")
    
    print(f"\n日誌詳情示例:")
    print(f"技能使用日誌: {skill_use.to_dict()}")
    
    print()


def demo_battle():
    """演示戰鬥功能"""
    print("=== 戰鬥演示 ===")
    
    # 創建戰鬥
    battle = Battle(
        battle_id="demo_battle_001",
        user_id=12345,
        floor_number=1,
        monster_group_id="goblin_group"
    )
    
    print(f"創建戰鬥: {battle}")
    
    # 創建玩家隊伍
    hero_stats = CombatantStats(
        hp=100, max_mp=50, mp_regen_per_turn=5,
        patk=80, pdef=40, matk=60, mdef=30,
        spd=75, crit_rate=0.1, crit_dmg_multiplier=1.5,
        accuracy=0.95, evasion=0.05
    )
    
    mage_stats = CombatantStats(
        hp=80, max_mp=80, mp_regen_per_turn=10,
        patk=50, pdef=25, matk=90, mdef=40,
        spd=65, crit_rate=0.08, crit_dmg_multiplier=1.4,
        accuracy=0.92, evasion=0.12
    )
    
    hero = Combatant("hero_001", "戰士", hero_stats, True)
    mage = Combatant("mage_001", "法師", mage_stats, True)
    
    # 創建怪物隊伍
    goblin_stats = CombatantStats(
        hp=60, max_mp=20, mp_regen_per_turn=2,
        patk=45, pdef=20, matk=25, mdef=15,
        spd=55, crit_rate=0.05, crit_dmg_multiplier=1.2,
        accuracy=0.85, evasion=0.15
    )
    
    orc_stats = CombatantStats(
        hp=120, max_mp=30, mp_regen_per_turn=3,
        patk=70, pdef=50, matk=30, mdef=25,
        spd=40, crit_rate=0.03, crit_dmg_multiplier=1.3,
        accuracy=0.90, evasion=0.05
    )
    
    goblin = Combatant("goblin_001", "哥布林", goblin_stats, False)
    orc = Combatant("orc_001", "獸人", orc_stats, False)
    
    # 添加戰鬥者到戰鬥
    battle.add_player_combatant(hero)
    battle.add_player_combatant(mage)
    battle.add_monster_combatant(goblin)
    battle.add_monster_combatant(orc)
    
    print(f"\n隊伍配置:")
    print(f"玩家方: {[c.name for c in battle.player_combatants]}")
    print(f"怪物方: {[c.name for c in battle.monster_combatants]}")
    
    # 開始戰鬥
    battle.start_battle()
    print(f"\n戰鬥開始: {battle}")
    
    # 計算行動順序
    print(f"行動順序: {[c.name for c in battle.turn_order]}")
    
    # 模擬幾回合
    for turn in range(3):
        current_actor = battle.get_current_actor()
        if current_actor:
            print(f"第{battle.current_turn}回合 - {current_actor.name} 行動")
            
            # 模擬行動（這裡只是演示，實際戰鬥邏輯會更複雜）
            if current_actor.is_player:
                # 玩家攻擊怪物
                targets = battle.get_alive_combatants(player_side=False)
                if targets:
                    target = targets[0]
                    damage = 25
                    target.take_damage(damage)
                    print(f"  {current_actor.name} 攻擊 {target.name}，造成 {damage} 點傷害")
                    
                    # 記錄日誌
                    battle.add_log_entry(BattleLogEntry.create_damage(
                        battle.current_turn, current_actor.combatant_id, 
                        target.combatant_id, damage
                    ))
            else:
                # 怪物攻擊玩家
                targets = battle.get_alive_combatants(player_side=True)
                if targets:
                    target = targets[0]
                    damage = 20
                    target.take_damage(damage)
                    print(f"  {current_actor.name} 攻擊 {target.name}，造成 {damage} 點傷害")
                    
                    # 記錄日誌
                    battle.add_log_entry(BattleLogEntry.create_damage(
                        battle.current_turn, current_actor.combatant_id,
                        target.combatant_id, damage
                    ))
            
            # 推進回合
            battle.advance_turn()
            
            # 檢查戰鬥是否結束
            if battle.check_battle_end():
                break
    
    # 顯示戰鬥結果
    print(f"\n戰鬥狀態: {battle.status.value}")
    if battle.winner_side:
        print(f"勝利方: {battle.winner_side}")
    
    # 顯示戰鬥摘要
    summary = battle.get_battle_summary()
    print(f"\n戰鬥摘要:")
    print(f"  戰鬥ID: {summary['battle_id']}")
    print(f"  回合數: {summary['current_turn']}")
    print(f"  日誌條目: {summary['log_entries']}")
    print(f"  玩家存活: {sum(1 for c in summary['player_combatants'] if c['alive'])}")
    print(f"  怪物存活: {sum(1 for c in summary['monster_combatants'] if c['alive'])}")
    
    print()


def main():
    """主函數"""
    print("RPG戰鬥系統核心模型演示")
    print("=" * 50)
    print()
    
    demo_skill_instance()
    demo_combatant()
    demo_battle_log()
    demo_battle()
    
    print("演示完成！")


if __name__ == "__main__":
    main()
